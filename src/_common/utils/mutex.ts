import type {Mutex} from 'async-mutex';

/**
 * Wraps an async function to ensure sequential execution per mutex.
 * The wrapped function will run sequentially, one after another per mutex.
 *
 * @param mutex - Mutex to ensure sequential execution
 * @param fn - The async function to wrap
 * @returns Wrapped function that ensures sequential execution
 */
export const withMutex =
  <TArgs extends unknown[], TReturn>(mutex: Mutex, fn: (...args: TArgs) => Promise<TReturn>) =>
  (...args: TArgs): Promise<TReturn> =>
    mutex.runExclusive(() => fn(...args));
