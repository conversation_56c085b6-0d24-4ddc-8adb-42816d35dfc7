import {FieldEventType} from 'views/mrv/project/phases/stages/field-events/types';
import {getFieldEventCount} from 'views/mrv/project/phases/stages/field-events/update-events/copy-field-events/utils/getFieldEventCount';

const field = {
  id: 'field-1',
  name: 'Test Field',
};

describe('getFieldEventCount', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should return null values when query data is undefined', () => {
    const result = getFieldEventCount(undefined);
    expect(result).toEqual({
      field: null,
      eventTypeCounts: null,
    });
  });

  it('should return null values when no phases exist', () => {
    const result = getFieldEventCount([]);
    expect(result).toEqual({
      field: null,
      eventTypeCounts: null,
    });
  });

  it('should return null values when phases have no stages', () => {
    const result = getFieldEventCount([
      {
        id: 'phase-1',
        stages: [],
      },
    ]);
    expect(result).toEqual({
      field: null,
      eventTypeCounts: null,
    });
  });

  it('should return null values when stages have no field data', () => {
    const result = getFieldEventCount([
      {
        id: 'phase-1',
        stages: [{id: 'stage-1'}],
      },
    ]);
    expect(result).toEqual({
      field: null,
      eventTypeCounts: null,
    });
  });

  it('should count event types correctly from single field with single event', () => {
    const mockFieldData = {
      ...field,
      cultivation_cycles: [
        {
          events: [{id: 'event-1', type: FieldEventType.CroppingEvent}],
        },
      ],
    };

    const phases = [
      {
        id: 'phase-1',
        stages: [
          {
            id: 'stage-1',
            type: null,
            field: mockFieldData,
          },
        ],
      },
    ];

    const result = getFieldEventCount(phases);

    expect(result).toEqual({
      field,
      eventTypeCounts: {
        [FieldEventType.CroppingEvent]: 1,
      },
    });
  });

  it('should count events across stages', () => {
    const mockFieldData = {
      ...field,
      cultivation_cycles: [
        {
          events: [
            {id: 'event-1', type: FieldEventType.CroppingEvent},
            {id: 'event-5', type: FieldEventType.IrrigationEvent},
            {id: 'event-6', type: FieldEventType.ApplicationEvent},
            {id: 'event-7', type: FieldEventType.TillageEvent},
            {id: 'event-8', type: FieldEventType.CroppingEvent},
          ],
        },
        {
          events: [
            {id: 'event-4', type: FieldEventType.IrrigationEvent},
            {id: 'event-5', type: FieldEventType.ApplicationEvent},
            {id: 'event-6', type: FieldEventType.CroppingEvent},
            {id: 'event-7', type: FieldEventType.CroppingEvent},
            {id: 'event-8', type: FieldEventType.TillageEvent},
          ],
        },
      ],
    };

    const phases = [
      {
        id: 'phase-1',
        stages: [
          {
            id: 'stage-1',
            type: null,
            field: mockFieldData,
          },
        ],
      },
    ];

    const result = getFieldEventCount(phases);

    expect(result).toEqual({
      field,
      eventTypeCounts: {
        [FieldEventType.CroppingEvent]: 4,
        [FieldEventType.TillageEvent]: 2,
        [FieldEventType.IrrigationEvent]: 2,
        [FieldEventType.ApplicationEvent]: 2,
      },
    });
  });

  it('should process events from multiple stages and phases', () => {
    const mockFieldData1 = {
      ...field,
      cultivation_cycles: [
        {
          events: [{id: 'event-1', type: FieldEventType.CroppingEvent}],
        },
      ],
    };
    const mockFieldData2 = {
      id: 'field-2',
      name: 'Test Field 2',
      cultivation_cycles: [
        {
          events: [{id: 'event-2', type: FieldEventType.CroppingEvent}],
        },
      ],
    };

    const testPhases = [
      {
        id: 'phase-1',
        stages: [
          {
            id: 'stage-1',
            type: null,
            field: mockFieldData1,
          },
          {
            id: 'stage-2',
            type: null,
            field: mockFieldData2,
          },
        ],
      },
    ];

    const result = getFieldEventCount(testPhases);

    expect(result).toEqual({
      field,
      eventTypeCounts: {
        [FieldEventType.CroppingEvent]: 2,
      },
    });
  });

  it('should skip events without type', () => {
    const mockFieldData = {
      ...field,
      cultivation_cycles: [
        {
          events: [{id: 'event-1', type: FieldEventType.CroppingEvent}, {id: 'event-2'}],
        },
      ],
    };

    const testPhases = [
      {
        id: 'phase-1',
        stages: [
          {
            id: 'stage-1',
            type: null,
            field: mockFieldData,
          },
        ],
      },
    ];
    const result = getFieldEventCount(testPhases);

    expect(result).toEqual({
      field,
      eventTypeCounts: {
        [FieldEventType.CroppingEvent]: 1,
      },
    });
  });
});
