import {FieldEventType} from 'views/mrv/project/phases/stages/field-events/types';
import {getFieldEventCount} from 'views/mrv/project/phases/stages/field-events/update-events/copy-field-events/utils/getFieldEventCount';

describe('getFieldEventCount', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should return empty object when query data is undefined', () => {
    const result = getFieldEventCount(undefined);
    expect(result).toEqual({});
  });

  it('should return empty object when no phases exist', () => {
    const result = getFieldEventCount([]);
    expect(result).toEqual({});
  });

  it('should return empty object when phases have no stages', () => {
    const result = getFieldEventCount([
      {
        id: 'phase-1',
        stages: [],
      },
    ]);
    expect(result).toEqual({});
  });

  it('should return empty object when stages have no field data', () => {
    const result = getFieldEventCount([
      {
        id: 'phase-1',
        stages: [{id: 'stage-1'}],
      },
    ]);
    expect(result).toEqual({});
  });

  it('should count event types correctly from single field with single event', () => {
    const mockFieldData = {
      __typename: 'Field' as const,
      id: 'field-1',
      cultivation_cycles: [
        {
          events: [{id: 'event-1', type: FieldEventType.CroppingEvent}],
        },
      ],
    };

    const phases = [
      {
        __typename: 'Phase' as const,
        id: 'phase-1',
        stages: [
          {
            __typename: 'Stage' as const,
            id: 'stage-1',
            type: null,
            field: mockFieldData,
          },
        ],
      },
    ];

    const result = getFieldEventCount(phases);

    expect(result).toEqual({
      [FieldEventType.CroppingEvent]: 1,
    });
  });

  it('should count multiple event types correctly', () => {
    const mockFieldData = {
      __typename: 'Field' as const,
      id: 'field-1',
      cultivation_cycles: [
        {
          events: [
            {id: 'event-1', type: FieldEventType.CroppingEvent},
            {id: 'event-2', type: FieldEventType.CroppingEvent},
            {id: 'event-3', type: FieldEventType.TillageEvent},
            {id: 'event-4', type: FieldEventType.ApplicationEvent},
            {id: 'event-5', type: FieldEventType.IrrigationEvent},
          ],
        },
        {
          events: [
            {id: 'event-4', type: FieldEventType.IrrigationEvent},
            {id: 'event-5', type: FieldEventType.ApplicationEvent},
            {id: 'event-6', type: FieldEventType.CroppingEvent},
            {id: 'event-7', type: FieldEventType.CroppingEvent},
            {id: 'event-8', type: FieldEventType.TillageEvent},
          ],
        },
      ],
    };

    const phases = [
      {
        __typename: 'Phase' as const,
        id: 'phase-1',
        stages: [
          {
            __typename: 'Stage' as const,
            id: 'stage-1',
            type: null,
            field: mockFieldData,
          },
        ],
      },
    ];

    const result = getFieldEventCount(phases);

    expect(result).toEqual({
      [FieldEventType.CroppingEvent]: 4,
      [FieldEventType.TillageEvent]: 2,
      [FieldEventType.IrrigationEvent]: 2,
      [FieldEventType.ApplicationEvent]: 2,
    });
  });

  it('should process events from multiple stages and phases', () => {
    const mockFieldData1 = {
      __typename: 'Field' as const,
      id: 'field-1',
      cultivation_cycles: [
        {
          events: [{id: 'event-1', type: FieldEventType.CroppingEvent}],
        },
      ],
    };
    const mockFieldData2 = {
      __typename: 'Field' as const,
      id: 'field-2',
      cultivation_cycles: [
        {
          events: [{id: 'event-2', type: FieldEventType.CroppingEvent}],
        },
      ],
    };

    const testPhases = [
      {
        __typename: 'Phase' as const,
        id: 'phase-1',
        stages: [
          {
            __typename: 'Stage' as const,
            id: 'stage-1',
            type: null,
            field: mockFieldData1,
          },
          {
            __typename: 'Stage' as const,
            id: 'stage-2',
            type: null,
            field: mockFieldData2,
          },
        ],
      },
    ];

    const result = getFieldEventCount(testPhases);

    expect(result).toEqual({
      [FieldEventType.CroppingEvent]: 2,
    });
  });

  it('should skip events without type', () => {
    const mockFieldData = {
      __typename: 'Field' as const,
      id: 'field-1',
      cultivation_cycles: [
        {
          events: [{id: 'event-1', type: FieldEventType.CroppingEvent}, {id: 'event-2'}],
        },
      ],
    };

    const testPhases = [
      {
        __typename: 'Phase' as const,
        id: 'phase-1',
        stages: [
          {
            __typename: 'Stage' as const,
            id: 'stage-1',
            type: null,
            field: mockFieldData,
          },
        ],
      },
    ];
    const result = getFieldEventCount(testPhases);

    expect(result).toEqual({
      [FieldEventType.CroppingEvent]: 1,
    });
  });
});
