import type {MockedProviderProps} from '@apollo/client/testing';
import {MockedProvider} from '@apollo/client/testing';
import {act, waitFor} from '@testing-library/react';
import {renderHook} from '@testing-library/react-hooks';
import type {ReactNode} from 'react';
import React from 'react';

import {CopyFieldEventsMode} from '__generated__/gql/graphql';
import {showNotification} from '_common/components/NotificationSnackbar';
import {SimpleProviders} from '_common/test_utils/renderWithProviders';

import {GET_PHASE_CULTIVATION_CYCLE_EVENTS} from 'views/mrv/graphql/queries/field-events';
import {mockSelectedFieldContext} from 'views/mrv/project/phases/stages/__mocks__/SelectedFieldContext';
import {mockFieldId} from 'views/mrv/project/phases/stages/__tests__/mock-data/mocks';
import {FieldEventType} from 'views/mrv/project/phases/stages/field-events/types';
import {useCopyFieldEvents} from 'views/mrv/project/phases/stages/field-events/update-events/copy-field-events/useCopyFieldEvents';
import {refetchFieldEvents} from 'views/mrv/project/phases/stages/field-events/utils/refetchFieldEvents';
import {useSelectedFieldContext} from 'views/mrv/project/phases/stages/SelectedFieldContext';

import {
  BULK_COPY_FIELD_EVENTS_ERROR_MOCK,
  BULK_COPY_FIELD_EVENTS_SUCCESS_MOCK,
  mockSelectedFieldEvents,
} from './mockData';

jest.mock('views/mrv/project/phases/stages/SelectedFieldContext');
jest.mock('views/mrv/project/phases/PhaseContext');
jest.mock('views/mrv/project/phases/stages/StageContext');
jest.mock('store/useRedux');
jest.mock('views/mrv/project/phases/stages/field-events/utils/refetchFieldEvents');
jest.mock('_common/hooks/use-parsed-match-params', () => ({
  useParsedMatchParams: jest.fn().mockReturnValue({
    projectId: '1',
  }),
}));

jest.mock('_common/components/NotificationSnackbar', () => ({
  showNotification: jest.fn(),
}));

// Helper function to create mock field events data
const createMockFieldEvents = (fieldId: string, eventCount = 2) => {
  const events = [];
  for (let i = 1; i <= eventCount; i++) {
    const isCroppingEvent = i % 2 === 0;
    const eventType = isCroppingEvent ? FieldEventType.CroppingEvent : FieldEventType.TillageEvent;

    events.push({
      __typename: 'FieldEvent',
      id: `event-${fieldId}-${i}`,
      type: eventType,
      is_locked: false,
      event_values: {
        __typename: eventType,
        ...(isCroppingEvent
          ? {
              planting_date: '2023-05-01',
              harvest_date: '2023-10-01',
              crop_type: 'corn',
              crop_yield: null,
              yield_rate_unit: null,
              termination_method: null,
              residue_harvested: null,
              crop_usage: null,
            }
          : {
              tillage_practice: 'conventional_till',
              tillage_date: '2023-04-15',
              tillage_depth: null,
              tillage_depth_unit: null,
              soil_inversion: null,
            }),
      },
    });
  }
  return events;
};

// Helper function to create Apollo mock for GET_PHASE_CULTIVATION_CYCLE_EVENTS
const createGetPhaseFieldEventsMock = (
  fieldId: string,
  fieldName: string,
  projectId = '1',
  phaseType = 'ENROLMENT',
  hasEvents = true,
  eventCount = 2,
  isError = false
) => {
  if (isError) {
    return {
      request: {
        query: GET_PHASE_CULTIVATION_CYCLE_EVENTS,
        variables: {
          fieldId,
          projectId,
          phaseType,
          prefill_monitoring_phase: false,
        },
      },
      error: new Error('Failed to fetch field events'),
    };
  }

  const events = hasEvents ? createMockFieldEvents(fieldId, eventCount) : [];

  return {
    request: {
      query: GET_PHASE_CULTIVATION_CYCLE_EVENTS,
      variables: {
        fieldId,
        projectId,
        phaseType,
        prefill_monitoring_phase: false,
      },
    },
    result: {
      data: {
        mrv: {
          __typename: 'MRV',
          project: {
            __typename: 'Project',
            id: projectId,
            program: {
              __typename: 'Program',
              id: 'program-1',
              phases: [
                {
                  __typename: 'Phase',
                  id: 'phase-1',
                  stages: [
                    {
                      __typename: 'Stage',
                      id: 'stage-1',
                      type: 'CROP_EVENTS',
                      field: {
                        __typename: 'Field',
                        id: fieldId,
                        name: fieldName,
                        cultivation_cycles: hasEvents
                          ? [
                              {
                                __typename: 'CultivationCycle',
                                id: `cycle-${fieldId}-1`,
                                start_date: '2023-01-01',
                                end_date: '2023-12-31',
                                crop_type: 'corn',
                                crop_event_is_locked: false,
                                contains_prefilled_monitoring_phase_events: false,
                                no_practice_observations: {
                                  __typename: 'NoPracticeObservations',
                                  tillage_event: false,
                                  irrigation_event: false,
                                  application_event: false,
                                },
                                events,
                              },
                            ]
                          : [],
                      },
                    },
                  ],
                },
              ],
            },
          },
        },
      },
    },
  };
};

const renderTestComponent = (mocks: MockedProviderProps['mocks'], children: ReactNode) => {
  return (
    <SimpleProviders>
      <MockedProvider mocks={mocks}>{children}</MockedProvider>
    </SimpleProviders>
  );
};

const renderHookWithProviders = (mocks: Array<any> = []) => {
  return renderHook(
    () =>
      useCopyFieldEvents({
        selectedFieldIds: [mockFieldId],
      }),
    {
      wrapper: ({children}) => renderTestComponent(mocks, children),
    }
  );
};

describe('useCopyFieldEvents', () => {
  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
  const mockUseSelectedFieldContext = useSelectedFieldContext as jest.MockedFunction<
    typeof useSelectedFieldContext
  >;
  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
  const mockRefetchFieldEvents = refetchFieldEvents as jest.MockedFunction<
    typeof refetchFieldEvents
  >;

  beforeEach(() => {
    mockUseSelectedFieldContext.mockReturnValue({
      ...mockSelectedFieldContext,
      selectedFieldEvents: mockSelectedFieldEvents,
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should return copyFieldEvents function and loading state', () => {
    const {result} = renderHookWithProviders();

    expect(result.current.copyFieldEvents).toBeInstanceOf(Function);
    expect(result.current.copyFieldEventsLoading).toBe(false);
  });

  it('should successfully copy field events', async () => {
    renderHookWithProviders([BULK_COPY_FIELD_EVENTS_SUCCESS_MOCK]);

    const hookResult = renderHook(
      () =>
        useCopyFieldEvents({
          selectedFieldIds: [mockFieldId],
        }),
      {
        wrapper: ({children}) => (
          <MockedProvider mocks={[BULK_COPY_FIELD_EVENTS_SUCCESS_MOCK]} addTypename={false}>
            {children}
          </MockedProvider>
        ),
      }
    );

    let result;
    await act(async () => {
      result = await hookResult.result.current.copyFieldEvents(CopyFieldEventsMode.Copy);
    });

    expect(result).not.toBeNull();
    expect(result).toEqual(BULK_COPY_FIELD_EVENTS_SUCCESS_MOCK.result);
  });

  it('should handle copy field events error', async () => {
    const hookResult = renderHook(
      () =>
        useCopyFieldEvents({
          selectedFieldIds: [mockFieldId],
        }),
      {
        wrapper: ({children}) => (
          <MockedProvider mocks={[BULK_COPY_FIELD_EVENTS_ERROR_MOCK]} addTypename={false}>
            {children}
          </MockedProvider>
        ),
      }
    );

    await act(async () => {
      await hookResult.result.current.copyFieldEvents(CopyFieldEventsMode.Overwrite);
    });

    await waitFor(() => {
      expect(showNotification).toHaveBeenCalledWith({
        message: 'There was an issue copying your field events',
        type: 'error',
      });
    });
  });

  it('should not copy events when selectedFieldEvents.events is null', async () => {
    mockUseSelectedFieldContext.mockReturnValue({
      ...mockSelectedFieldContext,
      selectedFieldEvents: {
        field: {id: '108264', name: 'Test Field'},
        events: null,
      },
    });

    const hookResult = renderHook(
      () =>
        useCopyFieldEvents({
          selectedFieldIds: [mockFieldId],
        }),
      {
        wrapper: ({children}) => (
          <MockedProvider mocks={[]} addTypename={false}>
            {children}
          </MockedProvider>
        ),
      }
    );
    let result;
    await act(async () => {
      result = await hookResult.result.current.copyFieldEvents(CopyFieldEventsMode.Copy);
    });

    expect(result).toBeNull();
  });

  it('should refetch field events on success', async () => {
    const hookResult = renderHook(
      () =>
        useCopyFieldEvents({
          selectedFieldIds: [mockFieldId],
        }),
      {
        wrapper: ({children}) => (
          <MockedProvider mocks={[BULK_COPY_FIELD_EVENTS_SUCCESS_MOCK]} addTypename={false}>
            <SimpleProviders>{React.isValidElement(children) ? children : <></>}</SimpleProviders>
          </MockedProvider>
        ),
      }
    );

    await act(async () => {
      await hookResult.result.current.copyFieldEvents(CopyFieldEventsMode.Copy);
    });

    await waitFor(() => {
      expect(mockRefetchFieldEvents).toHaveBeenCalled();
    });
  });

  describe('fieldEventsCount', () => {
    const testFieldId = 'test-field-123';
    const testFieldName = 'Test Field';

    it('should populate fieldEventsCount for fields with events', async () => {
      const mockWithEvents = createGetPhaseFieldEventsMock(
        testFieldId,
        testFieldName,
        '1',
        'ENROLMENT',
        true,
        3
      );

      const hookResult = renderHook(
        () =>
          useCopyFieldEvents({
            selectedFieldIds: [testFieldId],
          }),
        {
          wrapper: ({children}) => (
            <MockedProvider mocks={[mockWithEvents]} addTypename={false}>
              <SimpleProviders>{React.isValidElement(children) ? children : <></>}</SimpleProviders>
            </MockedProvider>
          ),
        }
      );

      // Wait for the fieldEventsCount to be populated
      await waitFor(() => {
        expect(hookResult.result.current.fieldEventsCount).toEqual({
          [testFieldId]: {
            field: {id: testFieldId, name: testFieldName},
            eventTypeCounts: {
              [FieldEventType.TillageEvent]: 2,
              [FieldEventType.CroppingEvent]: 1,
            },
          },
        });
      });
    });

    it('should return null eventTypeCounts for fields with no events', async () => {
      const mockWithoutEvents = createGetPhaseFieldEventsMock(
        testFieldId,
        testFieldName,
        '1',
        'ENROLMENT',
        false
      );

      const hookResult = renderHook(
        () =>
          useCopyFieldEvents({
            selectedFieldIds: [testFieldId],
          }),
        {
          wrapper: ({children}) => (
            <MockedProvider mocks={[mockWithoutEvents]} addTypename={false}>
              <SimpleProviders>{React.isValidElement(children) ? children : <></>}</SimpleProviders>
            </MockedProvider>
          ),
        }
      );

      // Wait for the fieldEventsCount to be populated
      await waitFor(() => {
        expect(hookResult.result.current.fieldEventsCount).toEqual({
          [testFieldId]: {
            field: {id: testFieldId, name: testFieldName},
            eventTypeCounts: null,
          },
        });
      });
    });

    it('should handle multiple fields with mixed event counts', async () => {
      const fieldId1 = 'field-1';
      const fieldId2 = 'field-2';
      const mockField1 = createGetPhaseFieldEventsMock(
        fieldId1,
        'Field 1',
        '1',
        'ENROLMENT',
        true,
        2
      );
      const mockField2 = createGetPhaseFieldEventsMock(
        fieldId2,
        'Field 2',
        '1',
        'ENROLMENT',
        false
      );

      const hookResult = renderHook(
        () =>
          useCopyFieldEvents({
            selectedFieldIds: [fieldId1, fieldId2],
          }),
        {
          wrapper: ({children}) => (
            <MockedProvider mocks={[mockField1, mockField2]} addTypename={false}>
              <SimpleProviders>{React.isValidElement(children) ? children : <></>}</SimpleProviders>
            </MockedProvider>
          ),
        }
      );

      // Wait for the fieldEventsCount to be populated
      await waitFor(() => {
        expect(hookResult.result.current.fieldEventsCount).toEqual({
          [fieldId1]: {
            field: {id: fieldId1, name: 'Field 1'},
            eventTypeCounts: {
              [FieldEventType.TillageEvent]: 1,
              [FieldEventType.CroppingEvent]: 1,
            },
          },
          [fieldId2]: {
            field: {id: fieldId2, name: 'Field 2'},
            eventTypeCounts: null,
          },
        });
      });
    });

    it('should handle GraphQL errors gracefully', async () => {
      const mockWithError = createGetPhaseFieldEventsMock(
        testFieldId,
        testFieldName,
        '1',
        'ENROLMENT',
        true,
        2,
        true
      );

      const hookResult = renderHook(
        () =>
          useCopyFieldEvents({
            selectedFieldIds: [testFieldId],
          }),
        {
          wrapper: ({children}) => (
            <MockedProvider mocks={[mockWithError]} addTypename={false}>
              <SimpleProviders>{React.isValidElement(children) ? children : <></>}</SimpleProviders>
            </MockedProvider>
          ),
        }
      );

      // Wait for the hook to process the error and set fieldEventsCount to empty object
      await waitFor(() => {
        expect(hookResult.result.current.fieldEventsCount).toEqual({});
      });
    });

    it('should return null for empty field list', async () => {
      const hookResult = renderHook(
        () =>
          useCopyFieldEvents({
            selectedFieldIds: [],
          }),
        {
          wrapper: ({children}) => (
            <MockedProvider mocks={[]} addTypename={false}>
              <SimpleProviders>{React.isValidElement(children) ? children : <></>}</SimpleProviders>
            </MockedProvider>
          ),
        }
      );

      // When selectedFieldIds is empty, fieldEventsCount should remain null
      expect(hookResult.result.current.fieldEventsCount).toBeNull();

      // Wait a bit to ensure it doesn't change
      await new Promise(resolve => setTimeout(resolve, 100));
      expect(hookResult.result.current.fieldEventsCount).toBeNull();
    });

    it('should refetch fieldEventsCount when selectedFieldIds changes', async () => {
      const fieldId1 = 'field-1';
      const fieldId2 = 'field-2';
      const mockField1 = createGetPhaseFieldEventsMock(
        fieldId1,
        'Field 1',
        '1',
        'ENROLMENT',
        true,
        1
      );
      const mockField2 = createGetPhaseFieldEventsMock(
        fieldId2,
        'Field 2',
        '1',
        'ENROLMENT',
        true,
        2
      );

      const hookResult = renderHook(
        ({selectedFieldIds}) =>
          useCopyFieldEvents({
            selectedFieldIds,
          }),
        {
          initialProps: {selectedFieldIds: [fieldId1]},
          wrapper: ({children}) => (
            <MockedProvider mocks={[mockField1, mockField2]} addTypename={false}>
              <SimpleProviders>{React.isValidElement(children) ? children : <></>}</SimpleProviders>
            </MockedProvider>
          ),
        }
      );

      // Wait for initial fieldEventsCount to be populated
      await waitFor(() => {
        expect(hookResult.result.current.fieldEventsCount).toEqual({
          [fieldId1]: {
            field: {id: fieldId1, name: 'Field 1'},
            eventTypeCounts: {
              [FieldEventType.TillageEvent]: 1,
            },
          },
        });
      });

      // Change selectedFieldIds
      hookResult.rerender({selectedFieldIds: [fieldId2]});

      // Wait for fieldEventsCount to be updated with new field
      await waitFor(() => {
        expect(hookResult.result.current.fieldEventsCount).toEqual({
          [fieldId2]: {
            field: {id: fieldId2, name: 'Field 2'},
            eventTypeCounts: {
              [FieldEventType.TillageEvent]: 1,
              [FieldEventType.CroppingEvent]: 1,
            },
          },
        });
      });
    });
  });
});
