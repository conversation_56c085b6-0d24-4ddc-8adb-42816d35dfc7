import { useLazyQuery, useMutation } from '@apollo/client';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useIntl } from 'react-intl';



import { useAppDispatch } from 'store/useRedux';



import type { CopyFieldEventsMode } from '__generated__/gql/graphql';
import { showNotification } from '_common/components/NotificationSnackbar';
import { useParsedMatchParams } from '_common/hooks/use-parsed-match-params';



import { BULK_COPY_FIELD_EVENTS } from 'views/mrv/graphql/mutations/field-events';
import { GET_PHASE_CULTIVATION_CYCLE_EVENTS } from 'views/mrv/graphql/queries/field-events';
import { usePhaseContext } from 'views/mrv/project/phases/PhaseContext';
import { refetchFieldEvents } from 'views/mrv/project/phases/stages/field-events/utils/refetchFieldEvents';
import { useSelectedFieldContext } from 'views/mrv/project/phases/stages/SelectedFieldContext';
import { useStageContext } from 'views/mrv/project/phases/stages/StageContext';



import {getFieldEventCount, initialFieldHasEventsState} from './utils/getFieldEventCount';

export type CopyFieldEvents = (copyMode: CopyFieldEventsMode) => Promise<void>;

export type FieldHasEvents = {
  field: {
    id: string;
    name: string | null;
  } | null;
  fieldHasEvents: Record<string, boolean> | null;
};
export type FieldHasEventsLookup = Record<string, FieldHasEvents>;

type UseCopyFieldEvents = (props: {selectedFieldIds: Array<string>; onComplete?: () => void}) => {
  copyFieldEvents: CopyFieldEvents;
  copyFieldEventsLoading: boolean;
  fieldHasEventsLookup: FieldHasEventsLookup | null;
  fieldHasEventsLoading: boolean;
};

export const useCopyFieldEvents: UseCopyFieldEvents = ({selectedFieldIds, onComplete}) => {
  const {formatMessage} = useIntl();
  const dispatch = useAppDispatch();
  const {projectId} = useParsedMatchParams<{projectId: string}>();
  const {currentPhase} = usePhaseContext();
  const {currentStage} = useStageContext();
  const {selectedFieldEvents} = useSelectedFieldContext();
  const [fieldHasEventsLookup, setFieldHasEvents] = useState<FieldHasEventsLookup | null>(null);

  const stageId = String(currentStage?.id);

  const refetchEvents = useMemo(
    () => refetchFieldEvents(dispatch, projectId, stageId),
    [dispatch, projectId, stageId]
  );

  const [fetchFieldCultivationCycleEvents, {loading: fieldHasEventsLoading}] = useLazyQuery(
    GET_PHASE_CULTIVATION_CYCLE_EVENTS,
    {
      fetchPolicy: 'network-only',
      errorPolicy: 'all',
    }
  );

  const fetchFieldEventCounts = useCallback(async () => {
    if (!currentPhase || selectedFieldIds.length === 0) {
      return;
    }

    const fieldEventPromises = selectedFieldIds.map(async fieldId => {
      const result = await fetchFieldCultivationCycleEvents({
        variables: {
          projectId,
          fieldId,
          phaseType: String(currentPhase.type),
          prefill_monitoring_phase: false,
        },
      });

      const phases = result.data?.mrv?.project?.program?.phases;

      if (result.error || !phases?.length) {
        return initialFieldHasEventsState;
      }

      return getFieldEventCount(phases);
    });

    const results = await Promise.all(fieldEventPromises);

    setFieldHasEvents(
      results.reduce((acc: FieldHasEventsLookup, result) => {
        if (!result.field) return acc;
        acc[result.field.id] = result;
        return acc;
      }, {})
    );
  }, [currentPhase, selectedFieldIds, fetchFieldCultivationCycleEvents, projectId]);

  useEffect(() => {
    const lookupHasAllSelectedFields = selectedFieldIds.every(fieldId =>
      Object.keys(fieldHasEventsLookup || {}).includes(fieldId)
    );

    if (!lookupHasAllSelectedFields) {
      void fetchFieldEventCounts();
    }
  }, [fetchFieldEventCounts, fieldHasEventsLookup, selectedFieldIds]);

  const [bulkCopyFieldEvents, {loading: copyFieldEventsLoading}] = useMutation(
    BULK_COPY_FIELD_EVENTS,
    {
      onError: () => {
        showNotification({
          message: formatMessage({
            id: 'copyFieldEvents.confirm.saveError',
            defaultMessage: 'There was an issue copying your field events',
          }),
          type: 'error',
        });
      },
      ...refetchEvents,
    }
  );

  const copyFieldEvents = async (copyMode: CopyFieldEventsMode) => {
    if (!selectedFieldEvents.events || !currentPhase || !currentStage) {
      return;
    }

    const allEventIds = Object.values(selectedFieldEvents.events).flat();

    const events = selectedFieldIds.flatMap(targetFieldId =>
      allEventIds.map(sourceEventId => ({
        sourceEventId: String(sourceEventId),
        targetFieldId: parseInt(targetFieldId, 10),
        targetProjectId: parseInt(projectId, 10),
        targetPhaseId: parseInt(currentPhase.id, 10),
        targetStageId: parseInt(currentStage.id, 10),
      }))
    );

    const result = await bulkCopyFieldEvents({
      variables: {
        events,
        copyMode,
      },
    });

    // Close the dialog on success
    if (result.data?.bulkCopyFieldEvents.length && onComplete) {
      onComplete();
    }
  };

  return {
    copyFieldEvents,
    copyFieldEventsLoading,
    fieldHasEventsLookup,
    fieldHasEventsLoading,
  };
};