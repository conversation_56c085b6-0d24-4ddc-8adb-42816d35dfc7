import {fireEvent, screen, waitFor} from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import React from 'react';

import {renderWithSimpleProviders} from '_common/test_utils/renderWithProviders';

import {mockSelectedFieldContext} from 'views/mrv/project/phases/stages/__mocks__/SelectedFieldContext';
import {
  mockFieldId,
  mockProjectFarms,
} from 'views/mrv/project/phases/stages/__tests__/mock-data/mocks';
import {SelectFieldsToCopy} from 'views/mrv/project/phases/stages/field-events/update-events/copy-field-events/SelectFieldsToCopyStep';
import {useSelectedFieldContext} from 'views/mrv/project/phases/stages/SelectedFieldContext';

jest.mock('views/mrv/project/phases/stages/SelectedFieldContext');
jest.mock('store/useRedux');

describe('SelectFieldsToCopy', () => {
  const mockUseSelectedFieldContext = useSelectedFieldContext as jest.MockedFunction<
    typeof useSelectedFieldContext
  >;
  const mockProps = {
    selectedFieldIds: [],
    setSelectedFieldIds: jest.fn(),
    handleClose: jest.fn(),
    handleNextStep: jest.fn(),
    isLoading: false,
    copyEventsLoading: false,
  };

  beforeEach(() => {
    mockUseSelectedFieldContext.mockReturnValue({
      ...mockSelectedFieldContext,
      farms: mockProjectFarms,
      selectedFieldEvents: {
        field: {id: '108264', name: 'Current Field'},
        events: null,
      },
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should render cancel and next buttons', () => {
    renderWithSimpleProviders(<SelectFieldsToCopy {...mockProps} />);

    expect(screen.getByRole('button', {name: 'Cancel'})).toBeInTheDocument();
    expect(screen.getByRole('button', {name: 'Copy to fields'})).toBeInTheDocument();
  });

  it('should disable next button when no fields are selected', () => {
    renderWithSimpleProviders(<SelectFieldsToCopy {...mockProps} />);

    const nextButton = screen.getByRole('button', {name: 'Copy to fields'});
    expect(nextButton).toBeDisabled();
    fireEvent.click(nextButton);

    expect(mockProps.handleNextStep).not.toHaveBeenCalled();
  });

  it('should enable next button when fields are selected', () => {
    const propsWithSelection = {
      ...mockProps,
      selectedFieldIds: ['field1', 'field2'],
    };

    renderWithSimpleProviders(<SelectFieldsToCopy {...propsWithSelection} />);

    const nextButton = screen.getByRole('button', {name: 'Copy to fields'});
    expect(nextButton).toBeEnabled();
    fireEvent.click(nextButton);

    expect(mockProps.handleNextStep).toHaveBeenCalled();
  });

  it('should call handleClose when cancel button is clicked', async () => {
    const user = userEvent.setup();
    renderWithSimpleProviders(<SelectFieldsToCopy {...mockProps} />);

    await user.click(screen.getByRole('button', {name: 'Cancel'}));

    expect(mockProps.handleClose).toHaveBeenCalledTimes(1);
  });

  it('should show no fields available message when farms is empty', () => {
    mockUseSelectedFieldContext.mockReturnValue({
      ...mockSelectedFieldContext,
      farms: [],
      selectedFieldEvents: {field: null, events: null},
    });

    renderWithSimpleProviders(<SelectFieldsToCopy {...mockProps} />);

    expect(screen.getByText('No fields available for copying events.')).toBeInTheDocument();
  });

  it('should show no fields available message when all fields are filtered out', () => {
    const singleFieldFarm = [
      {
        id: '1',
        name: 'Test Farm',
        fields: [{id: '108264', name: 'Current Field'}], // Only the current field
      },
    ];

    mockUseSelectedFieldContext.mockReturnValue({
      ...mockSelectedFieldContext,
      farms: singleFieldFarm,
      selectedFieldEvents: {
        field: {id: '108264', name: 'Current Field'},
        events: null,
      },
    });

    renderWithSimpleProviders(<SelectFieldsToCopy {...mockProps} />);

    expect(screen.getByText('No fields available for copying events.')).toBeInTheDocument();
  });

  it('should select field option and call setSelectedFieldIds', async () => {
    const user = userEvent.setup();
    renderWithSimpleProviders(<SelectFieldsToCopy {...mockProps} />);

    await user.click(screen.getByRole('button', {name: 'Open'}));

    await waitFor(() => {
      expect(screen.getByRole('listbox')).toBeInTheDocument();
    });

    const farm = mockProjectFarms[0];
    await user.click(screen.getByText(String(farm?.name)));
    const fieldOption = screen.getByRole('option', {
      name: 'Field 2 (#mock-field-id)',
    });
    await user.click(fieldOption);

    expect(mockProps.setSelectedFieldIds).toHaveBeenCalledWith([mockFieldId]);
  });
});
