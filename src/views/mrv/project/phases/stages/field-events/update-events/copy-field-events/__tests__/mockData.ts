import {CopyFieldEventsMode} from '__generated__/gql/graphql';

import {BULK_COPY_FIELD_EVENTS} from 'views/mrv/graphql/mutations/field-events';
import {GET_PHASE_CULTIVATION_CYCLE_EVENTS} from 'views/mrv/graphql/queries/field-events';
import {mockFieldId} from 'views/mrv/project/phases/stages/__tests__/mock-data/mocks';
import {FieldEventType} from 'views/mrv/project/phases/stages/field-events/types';

// Mock context values that match the test setup
export const MOCK_PROJECT_ID = 1;
export const MOCK_PHASE_ID = 6190;
export const MOCK_STAGE_ID = 14989;

export const mockSelectedFieldEvents = {
  field: {id: '108264', name: 'Test Field'},
  events: {
    'cultivation-cycle-1': ['event-1', 'event-2'],
    'cultivation-cycle-2': ['event-3'],
  },
};

// Helper function to create mock events for given field IDs
export const createMockEvents = (targetFieldIds: Array<string>) => {
  const allEventIds = Object.values(mockSelectedFieldEvents.events).flat();

  return targetFieldIds.flatMap(targetFieldId =>
    allEventIds.map(sourceEventId => ({
      sourceEventId: String(sourceEventId),
      targetFieldId: parseInt(targetFieldId, 10),
      targetProjectId: MOCK_PROJECT_ID,
      targetPhaseId: MOCK_PHASE_ID,
      targetStageId: MOCK_STAGE_ID,
    }))
  );
};

// Helper function to create Apollo mock for BULK_COPY_FIELD_EVENTS
export const createBulkCopyFieldEventsMock = (
  targetFieldIds: Array<string>,
  copyMode: CopyFieldEventsMode,
  isError = false
) => {
  const events = createMockEvents(targetFieldIds);

  if (isError) {
    return {
      request: {
        query: BULK_COPY_FIELD_EVENTS,
        variables: {events, copyMode},
      },
      error: new Error('Failed to copy field events'),
    };
  }

  return {
    request: {
      query: BULK_COPY_FIELD_EVENTS,
      variables: {events, copyMode},
    },
    result: {
      data: {
        bulkCopyFieldEvents: events.map((_, index) => ({
          id: `new-event-${index + 1}`,
          type: 'IrrigationEvent',
        })),
      },
    },
  };
};

// Pre-configured mocks for common test scenarios
export const BULK_COPY_FIELD_EVENTS_SUCCESS_MOCK = createBulkCopyFieldEventsMock(
  [mockFieldId],
  CopyFieldEventsMode.Copy
);

export const BULK_COPY_FIELD_EVENTS_OVERWRITE_MOCK = createBulkCopyFieldEventsMock(
  [mockFieldId],
  CopyFieldEventsMode.Overwrite
);

export const BULK_COPY_FIELD_EVENTS_ERROR_MOCK = createBulkCopyFieldEventsMock(
  [mockFieldId],
  CopyFieldEventsMode.Overwrite,
  true
);

// Helper function to create mock field events data
const createMockFieldEvents = (fieldId: string, _fieldName: string, eventCount = 2) => {
  const events = [];
  for (let i = 1; i <= eventCount; i++) {
    events.push({
      id: `event-${fieldId}-${i}`,
      type: i % 2 === 0 ? FieldEventType.CroppingEvent : FieldEventType.TillageEvent,
      is_locked: false,
      event_values: {
        __typename: i % 2 === 0 ? FieldEventType.CroppingEvent : FieldEventType.TillageEvent,
        ...(i % 2 === 0
          ? {planting_date: '2023-05-01', harvest_date: '2023-10-01', crop_type: 'corn'}
          : {tillage_date: '2023-04-15', tillage_practice: 'conventional_till'}),
      },
    });
  }
  return events;
};

// Helper function to create Apollo mock for GET_PHASE_CULTIVATION_CYCLE_EVENTS
export const createGetPhaseFieldEventsMock = (
  fieldId: string,
  fieldName: string,
  projectId = '1',
  phaseType = 'ENROLMENT',
  hasEvents = true,
  eventCount = 2,
  isError = false
) => {
  if (isError) {
    return {
      request: {
        query: GET_PHASE_CULTIVATION_CYCLE_EVENTS,
        variables: {
          fieldId,
          projectId,
          phaseType,
          prefill_monitoring_phase: false,
        },
      },
      error: new Error('Failed to fetch field events'),
    };
  }

  const events = hasEvents ? createMockFieldEvents(fieldId, fieldName, eventCount) : [];

  return {
    request: {
      query: GET_PHASE_CULTIVATION_CYCLE_EVENTS,
      variables: {
        fieldId,
        projectId,
        phaseType,
        prefill_monitoring_phase: false,
      },
    },
    result: {
      data: {
        mrv: {
          project: {
            id: projectId,
            program: {
              id: 'program-1',
              phases: [
                {
                  id: 'phase-1',
                  stages: [
                    {
                      id: 'stage-1',
                      type: 'CROP_EVENTS',
                      field: {
                        id: fieldId,
                        name: fieldName,
                        cultivation_cycles: [
                          {
                            id: `cycle-${fieldId}-1`,
                            start_date: '2023-01-01',
                            end_date: '2023-12-31',
                            crop_type: 'corn',
                            crop_event_is_locked: false,
                            contains_prefilled_monitoring_phase_events: false,
                            no_practice_observations: {
                              tillage_event: false,
                              irrigation_event: false,
                              application_event: false,
                            },
                            events,
                          },
                        ],
                      },
                    },
                  ],
                },
              ],
            },
          },
        },
      },
    },
  };
};

// Pre-configured mocks for common test scenarios
export const GET_PHASE_FIELD_EVENTS_WITH_EVENTS_MOCK = createGetPhaseFieldEventsMock(
  mockFieldId,
  'Test Field',
  '1',
  'ENROLMENT',
  true,
  3
);

export const GET_PHASE_FIELD_EVENTS_NO_EVENTS_MOCK = createGetPhaseFieldEventsMock(
  mockFieldId,
  'Test Field',
  '1',
  'ENROLMENT',
  false,
  0
);

export const GET_PHASE_FIELD_EVENTS_ERROR_MOCK = createGetPhaseFieldEventsMock(
  mockFieldId,
  'Test Field',
  '1',
  'ENROLMENT',
  false,
  0,
  true
);

// Mock with loading delay for testing loading states
export const GET_PHASE_FIELD_EVENTS_LOADING_MOCK = {
  ...GET_PHASE_FIELD_EVENTS_WITH_EVENTS_MOCK,
  delay: 300,
};
