import React, {useState} from 'react';
import {useIntl} from 'react-intl';

import {
  Box,
  Button,
  DialogActions,
  DialogContent,
  FormControlLabel,
  LoadingButton,
  Radio,
  RadioGroup,
  Typography,
} from '@regrow-internal/design-system';

import {CopyFieldEventsMode} from '__generated__/gql/graphql';

import type {CopyFieldEvents, FieldsEventCount} from './useCopyFieldEvents';

export const ConfirmCopy = ({
  selectedFieldIds,
  isLoading,
  fieldEventsCount,
  handlePreviousStep,
  copyFieldEvents,
}: {
  selectedFieldIds: Array<string>;
  isLoading: boolean;
  fieldEventsCount: FieldsEventCount | null;
  handlePreviousStep: () => void;
  copyFieldEvents: CopyFieldEvents;
}) => {
  const {formatMessage} = useIntl();

  const [copyMode, setCopyMode] = useState<CopyFieldEventsMode>(CopyFieldEventsMode.Copy);

  const handleConfirm = () => {
    void copyFieldEvents(copyMode);
  };

  const totalFieldsWithEventsCount = fieldEventsCount ? Object.values(fieldEventsCount).length : 0;

  return (
    <>
      <DialogContent>
        <Typography variant="h6" sx={{mb: 2}}>
          {formatMessage(
            {
              id: 'copyFieldEvents.confirm.fieldsWithEvents',
              defaultMessage: '{count} of your selected fields have existing events.',
            },
            {count: totalFieldsWithEventsCount}
          )}
        </Typography>

        <RadioGroup
          value={copyMode}
          onChange={({target}) => {
            // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
            setCopyMode(target.value as CopyFieldEventsMode);
          }}
        >
          <FormControlLabel
            value={CopyFieldEventsMode.Copy}
            control={<Radio />}
            label={
              <Box>
                <Typography variant="body2" sx={{fontWeight: 'medium'}}>
                  {formatMessage({
                    id: 'copyFieldEvents.confirm.copySelected',
                    defaultMessage: 'Copy selected events to fields',
                  })}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {formatMessage({
                    id: 'copyFieldEvents.confirm.copySelectedDescription',
                    defaultMessage: 'Existing events will not be overwritten.',
                  })}
                </Typography>
              </Box>
            }
          />
          <FormControlLabel
            value={CopyFieldEventsMode.Overwrite}
            control={<Radio />}
            label={
              <Box>
                <Typography variant="body2" sx={{fontWeight: 'medium'}}>
                  {formatMessage({
                    id: 'copyFieldEvents.confirm.overwriteExisting',
                    defaultMessage: 'Overwrite existing events with selected events',
                  })}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {formatMessage({
                    id: 'copyFieldEvents.confirm.overwriteExistingDescription',
                    defaultMessage: 'Locked events will not be overwritten.',
                  })}
                </Typography>
              </Box>
            }
          />
        </RadioGroup>
        <Box
          sx={{
            maxHeight: 200,
            overflowY: 'auto',
            border: '1px solid',
            borderColor: 'divider',
            borderRadius: 1,
            p: 2,
            mb: 2,
          }}
        >
          {fieldEventsCount &&
            Object.values(fieldEventsCount).map(fieldCount => {
              if (
                !fieldCount.eventTypeCounts ||
                !Object.values(fieldCount.eventTypeCounts).some(Boolean)
              ) {
                return null;
              }

              return (
                <Box key={fieldCount.field?.id} sx={{mb: 1}}>
                  <Typography variant="body2" sx={{fontWeight: 'medium', color: 'primary.main'}}>
                    {fieldCount.field?.name || `Field ${fieldCount.field?.id}`}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {Object.entries(fieldCount.eventTypeCounts)
                      .filter(([, count]) => count > 0)
                      .map(([eventType, count]) => `${eventType}: ${count}`)
                      .join(', ')}
                  </Typography>
                </Box>
              );
            })}
        </Box>
      </DialogContent>

      <DialogActions sx={{justifyContent: 'space-between'}}>
        <Button onClick={handlePreviousStep} color="secondary" variant="outlined">
          {formatMessage({
            id: 'Back',
            defaultMessage: 'Back',
          })}
        </Button>
        <LoadingButton
          onClick={handleConfirm}
          variant="contained"
          disabled={selectedFieldIds.length === 0}
          loading={isLoading}
        >
          {formatMessage({
            id: 'Confirm',
            defaultMessage: 'Confirm',
          })}
        </LoadingButton>
      </DialogActions>
    </>
  );
};
