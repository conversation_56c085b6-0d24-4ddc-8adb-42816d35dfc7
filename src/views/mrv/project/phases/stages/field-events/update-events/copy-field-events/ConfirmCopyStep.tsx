import React, { useState } from 'react';
import { useIntl } from 'react-intl';



import { Box, Button, DialogActions, DialogContent, FormControlLabel, LoadingButton, Radio, RadioGroup, Typography, useTheme } from '@regrow-internal/design-system';



import { CopyFieldEventsMode } from '__generated__/gql/graphql';



import type { CopyFieldEvents, FieldsEventCount } from './useCopyFieldEvents';





export const ConfirmCopy = ({
  selectedFieldIds,
  isLoading,
  fieldEventsCount,
  fieldsWithEventsCount,
  handlePreviousStep,
  copyFieldEvents,
}: {
  selectedFieldIds: Array<string>;
  isLoading: boolean;
  fieldEventsCount: FieldsEventCount | null;
  fieldsWithEventsCount: number;
  handlePreviousStep: () => void;
  copyFieldEvents: CopyFieldEvents;
}) => {
  const {formatMessage} = useIntl();

  const [copyMode, setCopyMode] = useState<CopyFieldEventsMode>(CopyFieldEventsMode.Copy);

  const handleConfirm = () => {
    void copyFieldEvents(copyMode);
  };

  return (
    <>
      <DialogContent>
        <Box display="flex" gap={3}>
          <Box>
            <Typography variant="h4">
              {formatMessage(
                {
                  id: 'copyFieldEvents.confirm.fieldsWithEvents',
                  defaultMessage: '{count} of your selected fields have existing events.',
                },
                {count: fieldsWithEventsCount}
              )}
            </Typography>
            <Box display="flex" flexDirection="column" gap={5}>
              <RadioGroup
                value={copyMode}
                onChange={({target}) => {
                  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
                  setCopyMode(target.value as CopyFieldEventsMode);
                }}
              >
                <FormControlLabel
                  value={CopyFieldEventsMode.Copy}
                  control={<Radio />}
                  label={
                    <Box mt={2}>
                      <Typography variant="body1">
                        {formatMessage({
                          id: 'copyFieldEvents.confirm.copySelected',
                          defaultMessage: 'Copy selected events to fields',
                        })}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {formatMessage({
                          id: 'copyFieldEvents.confirm.copySelectedDescription',
                          defaultMessage: 'Existing events will not be overwritten.',
                        })}
                      </Typography>
                    </Box>
                  }
                />
                <FormControlLabel
                  value={CopyFieldEventsMode.Overwrite}
                  control={<Radio />}
                  label={
                    <Box mt={2}>
                      <Typography variant="body1">
                        {formatMessage({
                          id: 'copyFieldEvents.confirm.overwriteExisting',
                          defaultMessage: 'Overwrite existing events with selected events',
                        })}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {formatMessage({
                          id: 'copyFieldEvents.confirm.overwriteExistingDescription',
                          defaultMessage: 'Locked events will not be overwritten.',
                        })}
                      </Typography>
                    </Box>
                  }
                />
              </RadioGroup>
            </Box>
          </Box>
          <FieldEventsListItem fieldEventsCount={fieldEventsCount} />
        </Box>
      </DialogContent>

      <DialogActions sx={{justifyContent: 'space-between'}}>
        <Button onClick={handlePreviousStep} color="secondary" variant="outlined">
          {formatMessage({
            id: 'Back',
            defaultMessage: 'Back',
          })}
        </Button>
        <LoadingButton
          onClick={handleConfirm}
          variant="contained"
          disabled={selectedFieldIds.length === 0}
          loading={isLoading}
        >
          {formatMessage({
            id: 'Confirm',
            defaultMessage: 'Confirm',
          })}
        </LoadingButton>
      </DialogActions>
    </>
  );
};

const FieldEventsListItem = ({fieldEventsCount}: {fieldEventsCount: FieldsEventCount | null}) => {
  const {formatMessage} = useIntl();
  const theme = useTheme();

  return (
    <Box
      component="ul"
      overflow={'auto'}
      border={1}
      borderColor={'divider'}
      borderRadius={1}
      maxHeight="160px"
      width="50%"
      margin="0"
      paddingLeft={6}
      paddingTop={2}
      sx={{
        scrollbarWidth: 'thin',
        '> li::marker': {
          color: theme.palette.semanticPalette.text.warning,
        },
      }}
    >
      {fieldEventsCount &&
        Object.values(fieldEventsCount).map(fieldCount => {
          if (
            !fieldCount.eventTypeCounts ||
            !Object.values(fieldCount.eventTypeCounts).some(Boolean)
          ) {
            return null;
          }

          return (
            <Box key={fieldCount.field?.id} component="li">
              <Typography color="semanticPalette.text.warning">
                <b>{fieldCount.field?.name || `Field ${fieldCount.field?.id}`}:</b>&nbsp;
                {Object.entries(fieldCount.eventTypeCounts)
                  .map(([eventType]) =>
                    formatMessage({
                      id: `copyFieldEvents.confirm.${eventType}`,
                      defaultMessage: eventType,
                    })
                  )
                  .join(', ')}
              </Typography>
            </Box>
          );
        })}
    </Box>
  );
};