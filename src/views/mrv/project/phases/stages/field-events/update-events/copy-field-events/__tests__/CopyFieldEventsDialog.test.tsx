import {screen, waitFor} from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import React from 'react';

import {renderWithSimpleProviders} from '_common/test_utils/renderWithProviders';

import {mockSelectedFieldContext} from 'views/mrv/project/phases/stages/__mocks__/SelectedFieldContext';
import {mockProjectFarms} from 'views/mrv/project/phases/stages/__tests__/mock-data/mocks';
import {CopyFieldEvents} from 'views/mrv/project/phases/stages/field-events/update-events/copy-field-events/CopyFieldEventsDialog';
import {useCopyFieldEvents} from 'views/mrv/project/phases/stages/field-events/update-events/copy-field-events/useCopyFieldEvents';
import {useSelectedFieldContext} from 'views/mrv/project/phases/stages/SelectedFieldContext';

import {mockSelectedFieldEvents} from './mockData';

jest.mock('views/mrv/project/phases/stages/SelectedFieldContext');
jest.mock('views/mrv/project/phases/PhaseContext');
jest.mock('views/mrv/project/phases/stages/StageContext');
jest.mock('store/useRedux');
jest.mock(
  'views/mrv/project/phases/stages/field-events/update-events/copy-field-events/useCopyFieldEvents'
);
jest.mock('_common/hooks/use-parsed-match-params', () => ({
  useParsedMatchParams: jest.fn().mockReturnValue({
    projectId: '1',
  }),
}));
jest.mock('views/mrv/project/phases/stages/field-events/utils/refetchFieldEvents');
jest.mock('_common/components/NotificationSnackbar', () => ({
  showNotification: jest.fn(),
}));

describe('CopyFieldEventsDialog', () => {
  jest.useRealTimers();
  const mockUseSelectedFieldContext = useSelectedFieldContext as jest.MockedFunction<
    typeof useSelectedFieldContext
  >;

  const mockUseCopyFieldEvents = useCopyFieldEvents as jest.MockedFunction<
    typeof useCopyFieldEvents
  >;

  // Default mock implementation for useCopyFieldEvents
  const defaultCopyFieldEventsMock = {
    copyFieldEvents: jest.fn(),
    copyFieldEventsLoading: false,
    fieldEventsCount: null,
    fieldEventsCountLoading: false,
  };

  beforeEach(() => {
    mockUseSelectedFieldContext.mockReturnValue({
      ...mockSelectedFieldContext,
      farms: mockProjectFarms,
      selectedFieldEvents: mockSelectedFieldEvents,
    });

    mockUseCopyFieldEvents.mockReturnValue(defaultCopyFieldEventsMock);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should render the trigger button', () => {
    renderWithSimpleProviders(<CopyFieldEvents />);

    expect(screen.getByRole('button', {name: /copy to/i})).toBeInTheDocument();
  });

  it('should open dialog when trigger button is clicked', async () => {
    const user = userEvent.setup();
    renderWithSimpleProviders(<CopyFieldEvents />);

    await user.click(screen.getByRole('button', {name: /copy to/i}));

    expect(screen.getByRole('dialog')).toBeInTheDocument();
    expect(screen.getByText('Copy events to fields')).toBeInTheDocument();
  });

  it('should show SelectFieldsToCopy component by default', async () => {
    const user = userEvent.setup();
    renderWithSimpleProviders(<CopyFieldEvents />);

    await user.click(screen.getByRole('button', {name: /copy to/i}));

    expect(screen.getByText('Select fields to copy events to.')).toBeInTheDocument();
  });

  it('should navigate to confirm step when fields are selected next is clicked and then back on click', async () => {
    // Mock fieldEventsCount to simulate fields with existing events
    const mockFieldEventsCount = {
      'mock-field-id': {
        field: {id: 'mock-field-id', name: 'Field 2'},
        eventTypeCounts: {CroppingEvent: 2, TillageEvent: 1},
      },
    };

    mockUseCopyFieldEvents.mockReturnValue({
      ...defaultCopyFieldEventsMock,
      fieldEventsCount: mockFieldEventsCount,
    });

    const user = userEvent.setup();
    renderWithSimpleProviders(<CopyFieldEvents />);

    await user.click(screen.getByRole('button', {name: /copy to/i}));
    await user.click(screen.getByRole('button', {name: 'Open'}));
    await waitFor(() => {
      expect(screen.getByRole('listbox')).toBeInTheDocument();
    });

    const farm = mockProjectFarms[0];
    await user.click(screen.getByText(String(farm?.name)));
    const fieldOption = screen.getByRole('option', {
      name: 'Field 2 (#mock-field-id)',
    });
    await user.click(fieldOption);

    const nextButton = screen.getByRole('button', {name: 'Copy to fields'});
    expect(nextButton).toBeEnabled();
    await waitFor(() => {
      expect(nextButton).toBeEnabled();
    });
    await user.click(nextButton);

    await waitFor(() => {
      expect(screen.queryByText('Select fields to copy events to.')).not.toBeInTheDocument();
    });

    expect(screen.getByText('Confirm')).toBeInTheDocument();

    await user.click(screen.getByRole('button', {name: 'Back'}));

    expect(screen.getByText('Select fields to copy events to.')).toBeInTheDocument();
    expect(screen.queryByText('Confirm')).not.toBeInTheDocument();
  });

  it('should close dialog and reset state when close is called', async () => {
    const user = userEvent.setup();
    renderWithSimpleProviders(<CopyFieldEvents />);

    await user.click(screen.getByRole('button', {name: /copy to/i}));
    expect(screen.getByText('Select fields to copy events to.')).toBeInTheDocument();
    expect(screen.getByRole('button', {name: 'Copy to fields'})).toBeDisabled();

    await user.click(screen.getByRole('button', {name: 'Open'}));

    await waitFor(() => {
      expect(screen.getByRole('listbox')).toBeInTheDocument();
    });

    const farm = mockProjectFarms[0];
    await user.click(screen.getByText(String(farm?.name)));
    const fieldOption = screen.getByRole('option', {
      name: 'Field 2 (#mock-field-id)',
    });
    await user.click(fieldOption);

    expect(screen.getByRole('button', {name: 'Copy to fields'})).toBeEnabled();
    await user.click(screen.getByRole('button', {name: 'Cancel'}));

    await waitFor(() => {
      expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
    });

    await user.click(screen.getByRole('button', {name: /copy to/i}));
    expect(screen.getByText('Select fields to copy events to.')).toBeInTheDocument();
    expect(screen.getByRole('button', {name: 'Copy to fields'})).toBeDisabled();
  });

  it('should successfully copy field events and close dialog if select fields have no existing events', async () => {
    const user = userEvent.setup();

    // Create a spy to track copy calls
    const copyFieldEventsSpy = jest.fn().mockResolvedValue(undefined);

    // Mock fieldEventsCount with no existing events (null eventTypeCounts)
    const mockFieldEventsCount = {
      'mock-field-id': {
        field: {id: 'mock-field-id', name: 'Field 2'},
        eventTypeCounts: null,
      },
    };

    mockUseCopyFieldEvents.mockReturnValue({
      ...defaultCopyFieldEventsMock,
      copyFieldEvents: copyFieldEventsSpy,
      fieldEventsCount: mockFieldEventsCount,
    });

    renderWithSimpleProviders(<CopyFieldEvents />);

    // Open dialog
    await user.click(screen.getByRole('button', {name: /copy to/i}));

    // Select a field
    await user.click(screen.getByRole('button', {name: 'Open'}));
    await waitFor(() => {
      expect(screen.getByRole('listbox')).toBeInTheDocument();
    });

    const farm = mockProjectFarms[0];
    await user.click(screen.getByText(String(farm?.name)));
    const fieldOption = screen.getByRole('option', {
      name: 'Field 2 (#mock-field-id)',
    });
    await user.click(fieldOption);

    expect(screen.getByRole('button', {name: 'Copy to fields'})).toBeEnabled();
    await user.click(screen.getByRole('button', {name: 'Copy to fields'}));

    // Assert that the dialog closes after successful copy
    await waitFor(() => {
      expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
    });

    // Verify that copyFieldEvents was called
    expect(copyFieldEventsSpy).toHaveBeenCalled();
  });
});
