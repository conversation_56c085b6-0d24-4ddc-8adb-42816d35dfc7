/* eslint-disable-next-line @typescript-eslint/no-restricted-imports */
import {Snackbar} from '@mui/material';
import type {SyntheticEvent} from 'react';
import React, {useMemo} from 'react';
import {FormattedMessage, useIntl} from 'react-intl';

import {Box, IconButton, SvgIcon, Typography, useTheme} from '@regrow-internal/design-system';

import {StageTypes} from '__generated__/mrv/mrvApi.types';
import {isDefined, isNumber} from '_common/utils/typeGuards';

import {useNavigateToStage} from 'containers/mrv/_hooks/useNavigateToStage';
import {usePhaseContext} from 'views/mrv/project/phases/PhaseContext';
import {getEventTypeFromStageType} from 'views/mrv/project/phases/stages/field-events/utils/fieldEventUtils';
import {useSelectedFieldContext} from 'views/mrv/project/phases/stages/SelectedFieldContext';
import {useStageContext} from 'views/mrv/project/phases/stages/StageContext';

import {CopyFieldEvents} from './copy-field-events/CopyFieldEventsDialog';

export const SELECTED_EVENT_TYPES = [
  {
    stageType: StageTypes.CROP_EVENTS,
    eventType: getEventTypeFromStageType(StageTypes.CROP_EVENTS),
  },
  {
    stageType: StageTypes.TILLAGE_EVENTS,
    eventType: getEventTypeFromStageType(StageTypes.TILLAGE_EVENTS),
  },
  {
    stageType: StageTypes.NUTRIENT_EVENTS,
    eventType: getEventTypeFromStageType(StageTypes.NUTRIENT_EVENTS),
  },
  {
    stageType: StageTypes.IRRIGATION_EVENTS,
    eventType: getEventTypeFromStageType(StageTypes.IRRIGATION_EVENTS),
  },
];

const SNACKBAR_MIN_WIDTH = '578px';
const SNACKBAR_MAX_WIDTH = '860px';

export const SelectedEventsSnackbar = () => {
  const {selectedFieldEvents, selectedField, setSelectedField, clearSelectedFieldEvents} =
    useSelectedFieldContext();
  const {formatMessage} = useIntl();
  const theme = useTheme();
  const {navigateToStage} = useNavigateToStage();
  const {currentPhase} = usePhaseContext();
  const {currentStage} = useStageContext();
  const {events, field} = selectedFieldEvents;

  const eventTypes = useMemo(() => {
    if (!currentPhase?.stages) return [];

    // Create stage type to ID mapping and filter/map event types in a single pass
    const stageMap = currentPhase.stages.reduce<Record<string, number | string>>((acc, stage) => {
      if (stage.type && isDefined(stage.id)) {
        acc[stage.type] = stage.id;
      }
      return acc;
    }, {});

    return SELECTED_EVENT_TYPES.filter(({stageType}) => stageType in stageMap).map(
      ({stageType, eventType}) => {
        let eventCount = 0;
        if (events && eventType) {
          const selectedEvents = events[eventType]?.length;
          if (isNumber(selectedEvents)) {
            eventCount = selectedEvents;
          }
        }

        const stageId = stageMap[stageType];

        return {
          stageType: stageType,
          count: eventCount,
          navigate: () => {
            navigateToStage(stageType, stageId);
          },
          label: formatMessage({
            id: `stage.${stageType}.name`,
            defaultMessage: stageType,
          }),
        };
      }
    );
  }, [currentPhase?.stages, events, formatMessage, navigateToStage]);

  const totalSelectedEvents = useMemo(() => {
    if (!events) return 0;
    return Object.values(events).reduce((acc, curr) => acc + curr.length, 0);
  }, [events]);

  const handleClose = (_: SyntheticEvent | Event, reason?: string) => {
    if (reason === 'clickaway') {
      return;
    }
    clearSelectedFieldEvents();
  };

  const navigateToSelectedField = () => {
    if (field?.id && selectedField?.id !== field.id) {
      setSelectedField(field.id);
    }
  };
  const isOpen = isDefined(selectedFieldEvents) && totalSelectedEvents > 0 && isDefined(field);

  const linkHoverStyles = {
    cursor: 'pointer',
    '&:hover': {
      textDecoration: 'underline',
      color: theme.palette.semanticPalette.textInverted.main,
    },
  };

  return (
    <Snackbar
      aria-label="selected events panel"
      anchorOrigin={{vertical: 'bottom', horizontal: 'center'}}
      open={isOpen}
      transitionDuration={0}
      onClose={handleClose}
      sx={{
        '& .MuiSnackbarContent-root': {
          backgroundColor: theme.palette.semanticPalette.surfaceInverted.main,
          paddingLeft: theme.spacing(3),
          paddingRight: theme.spacing(2),
          width: '100%',
          '> .MuiSnackbarContent-message': {
            padding: 0,
            width: '100%',
          },
        },
        '&.MuiSnackbar-root': {
          bottom: theme.spacing(2),
          minWidth: SNACKBAR_MIN_WIDTH,
          maxWidth: SNACKBAR_MAX_WIDTH,
          width: '80%',
        },
        zIndex: theme.zIndex.modal - 1,
      }}
      message={
        <Box display="flex" alignItems="center" gap={theme.spacing(3)} width="100%">
          <Typography color="inverted" fontSize="small" noWrap sx={{flexShrink: 0}}>
            <FormattedMessage
              id="selectedEventsSnackbar.eventsInCount"
              defaultMessage="{count} events in"
              values={{count: totalSelectedEvents}}
            />
            &nbsp;
            <Box
              role="button"
              component="span"
              sx={linkHoverStyles}
              onClick={navigateToSelectedField}
            >
              {field?.name}
            </Box>
            &nbsp;
            <FormattedMessage id="selected" defaultMessage="selected" />
          </Typography>
          <Typography color={theme.palette.semanticPalette.textInverted.secondary}>|</Typography>
          <Box display="flex" flexWrap="wrap" flexDirection="row" width="100%">
            {eventTypes.map(({stageType, label, count, navigate}) => {
              const isCurrentStage = currentStage?.type === stageType;

              return (
                <Box key={stageType} mr={theme.spacing(2.5)}>
                  <Typography
                    role="button"
                    color={
                      theme.palette.semanticPalette.textInverted[
                        isCurrentStage ? 'main' : 'secondary'
                      ]
                    }
                    fontSize="small"
                    fontWeight={isCurrentStage ? theme.typography.fontWeightBold : undefined}
                    onClick={isCurrentStage ? undefined : navigate}
                    sx={isCurrentStage ? undefined : linkHoverStyles}
                  >
                    {label}
                    &nbsp;({count})
                  </Typography>
                </Box>
              );
            })}
          </Box>
          <Box
            display="flex"
            alignItems="center"
            gap={theme.spacing(1)}
            ml="auto"
            flexWrap="nowrap"
            flexShrink={0}
          >
            <CopyFieldEvents />
            <IconButton
              aria-label="close selected events panel"
              onClick={handleClose}
              size="small"
              color="secondary"
              sx={{
                '&:hover': {
                  backgroundColor: theme.palette.semanticPalette.surfaceInverted.secondary,
                },
              }}
            >
              <SvgIcon type="cross" sx={{color: theme.palette.semanticPalette.textInverted.main}} />
            </IconButton>
          </Box>
        </Box>
      }
    />
  );
};
