import debounce from 'lodash/debounce';
import React, {useMemo, useState} from 'react';
import {useIntl} from 'react-intl';

import {Button, SimpleDialog, SvgIcon, useTheme} from '@regrow-internal/design-system';

import {CopyFieldEventsMode} from '__generated__/gql/graphql';

import {ConfirmCopy} from './ConfirmCopyStep';
import {SelectFieldsToCopy} from './SelectFieldsToCopyStep';
import {useCopyFieldEvents} from './useCopyFieldEvents';

enum DialogSteps {
  SelectFields = 'select_fields',
  Confirm = 'confirm',
}
export const CopyFieldEvents = () => {
  const {formatMessage} = useIntl();
  const theme = useTheme();

  const [isDialogOpen, setDialogOpen] = useState(false);
  const [dialogStep, setDialogStep] = useState(DialogSteps.SelectFields);

  const [selectedFieldIds, setSelectedFieldIds] = useState<Array<string>>([]);

  const handleClose = () => {
    setDialogOpen(false);
    setSelectedFieldIds([]);
    setDialogStep(DialogSteps.SelectFields);
  };

  const {copyFieldEvents, copyFieldEventsLoading, fieldHasEventsLookup, fieldHasEventsLoading} =
    useCopyFieldEvents({
      selectedFieldIds,
      onComplete: handleClose,
    });

  const fieldsWithEventsCount = useMemo(() => {
    if (!fieldHasEventsLookup) return 0;

    return Object.values(fieldHasEventsLookup).filter(field =>
      field.fieldHasEvents ? Object.values(field.fieldHasEvents).some(Boolean) : false
    ).length;
  }, [fieldHasEventsLookup]);

  const handleNextStep = async () => {
    if (selectedFieldIds.length === 0 || !fieldHasEventsLookup) return;
    if (fieldsWithEventsCount > 0) {
      setDialogStep(DialogSteps.Confirm);
    } else {
      await copyFieldEvents(CopyFieldEventsMode.Overwrite);
    }
  };

  const handleSetSelectedFields = debounce((fieldIds: Array<string>) => {
    setSelectedFieldIds(fieldIds);
  }, 400);

  return (
    <>
      <SimpleDialog
        open={isDialogOpen}
        onClose={handleClose}
        maxWidth={dialogStep === DialogSteps.SelectFields ? 'sm' : 'md'}
        aria-labelledby="copy-field-events-dialog-title"
        title={formatMessage({
          id: 'copyFieldEvents.dialog.title',
          defaultMessage: 'Copy events to fields',
        })}
      >
        {dialogStep === DialogSteps.Confirm ? (
          <ConfirmCopy
            selectedFieldIds={selectedFieldIds}
            handlePreviousStep={() => {
              setDialogStep(DialogSteps.SelectFields);
            }}
            copyFieldEvents={copyFieldEvents}
            fieldHasEventsLookup={fieldHasEventsLookup}
            fieldsWithEventsCount={fieldsWithEventsCount}
            isLoading={copyFieldEventsLoading}
          />
        ) : (
          <SelectFieldsToCopy
            selectedFieldIds={selectedFieldIds}
            setSelectedFieldIds={handleSetSelectedFields}
            handleNextStep={handleNextStep}
            handleClose={handleClose}
            isLoading={fieldHasEventsLoading && !fieldHasEventsLookup}
            copyEventsLoading={copyFieldEventsLoading}
          />
        )}
      </SimpleDialog>
      <Button
        onClick={() => {
          setDialogOpen(true);
        }}
        color="secondary"
        variant="text"
        size="medium"
        startIcon={<SvgIcon type="copy" />}
        sx={{
          color: theme.palette.semanticPalette.textInverted.main,
          backgroundColor: 'transparent',
          '&:hover': {
            backgroundColor: theme.palette.semanticPalette.surfaceInverted.secondary,
          },
        }}
      >
        {formatMessage({
          id: 'copyFieldEvents.trigger.button',
          defaultMessage: 'Copy to...',
        })}
      </Button>
    </>
  );
};
