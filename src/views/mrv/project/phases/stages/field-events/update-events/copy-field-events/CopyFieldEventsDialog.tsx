import React, { useState } from 'react';
import { useIntl } from 'react-intl';



import { Button, SimpleDialog, SvgIcon, useTheme } from '@regrow-internal/design-system';



import { CopyFieldEventsMode } from '__generated__/gql/graphql';



import { ConfirmCopy } from './ConfirmCopyStep';
import { SelectFieldsToCopy } from './SelectFieldsToCopyStep';
import {useCopyFieldEvents} from './useCopyFieldEvents';

interface FieldWithExistingEvents {
  id: string;
  name: string;
  eventTypes: Array<string>;
}

enum DialogSteps {
  SelectFields = 'select_fields',
  Confirm = 'confirm',
}
export const CopyFieldEvents = () => {
  const {formatMessage} = useIntl();
  const theme = useTheme();

  const [isDialogOpen, setDialogOpen] = useState(false);
  const [dialogStep, setDialogStep] = useState(DialogSteps.SelectFields);

  const [selectedFieldIds, setSelectedFieldIds] = useState<Array<string>>([]);

  const handleClose = () => {
    setDialogOpen(false);
    setSelectedFieldIds([]);
    setDialogStep(DialogSteps.SelectFields);
  };

  const {copyFieldEvents, copyFieldEventsLoading, fieldEventsCount, fieldEventsCountLoading} =
    useCopyFieldEvents({
      selectedFieldIds,
      onComplete: handleClose,
    });

  const selectedFieldsExistingEvents: Array<FieldWithExistingEvents> = fieldEventsCount
    ? Object.values(fieldEventsCount)
        .filter(fieldCount =>
          fieldCount.eventTypeCounts
            ? Object.values(fieldCount.eventTypeCounts).some(Boolean)
            : false
        )
        .map(fieldCount => ({
          id: fieldCount.fieldId,
          name: fieldCount.fieldName || '',
          eventTypes: fieldCount.eventTypeCounts ? Object.keys(fieldCount.eventTypeCounts) : [],
        }))
    : [];

  const handleNextStep = async () => {
    if (selectedFieldIds.length === 0 || !fieldEventsCount) return;
    if (selectedFieldsExistingEvents) {
      setDialogStep(DialogSteps.Confirm);
    } else {
      await copyFieldEvents(CopyFieldEventsMode.Overwrite);
    }
  };

  return (
    <>
      <SimpleDialog
        open={isDialogOpen}
        onClose={handleClose}
        maxWidth="sm"
        aria-labelledby="copy-field-events-dialog-title"
        title={formatMessage({
          id: 'copyFieldEvents.dialog.title',
          defaultMessage: 'Copy events to fields',
        })}
      >
        {dialogStep === DialogSteps.Confirm ? (
          <ConfirmCopy
            selectedFieldIds={selectedFieldIds}
            handlePreviousStep={() => {
              setDialogStep(DialogSteps.SelectFields);
            }}
            copyFieldEvents={copyFieldEvents}
            fieldsWithExistingEvents={selectedFieldsExistingEvents}
            isLoading={copyFieldEventsLoading}
          />
        ) : (
          <SelectFieldsToCopy
            selectedFieldIds={selectedFieldIds}
            setSelectedFieldIds={setSelectedFieldIds}
            handleNextStep={handleNextStep}
            handleClose={handleClose}
            disabled={fieldEventsCountLoading && !fieldEventsCount}
            isLoading={copyFieldEventsLoading}
          />
        )}
      </SimpleDialog>
      <Button
        onClick={() => {
          setDialogOpen(true);
        }}
        color="secondary"
        variant="text"
        size="small"
        startIcon={<SvgIcon type="copy" />}
        sx={{
          color: theme.palette.semanticPalette.textInverted.main,
          backgroundColor: 'transparent',
          '&:hover': {
            backgroundColor: theme.palette.semanticPalette.surfaceInverted.secondary,
          },
        }}
      >
        {formatMessage({
          id: 'copyFieldEvents.trigger.button',
          defaultMessage: 'Copy to...',
        })}
      </Button>
    </>
  );
};