import React, {useMemo, useState} from 'react';
import {useIntl} from 'react-intl';

import {Button, SimpleDialog, SvgIcon, useTheme} from '@regrow-internal/design-system';

import {CopyFieldEventsMode} from '__generated__/gql/graphql';

import {ConfirmCopy} from './ConfirmCopyStep';
import {SelectFieldsToCopy} from './SelectFieldsToCopyStep';
import {useCopyFieldEvents} from './useCopyFieldEvents';

enum DialogSteps {
  SelectFields = 'select_fields',
  Confirm = 'confirm',
}
export const CopyFieldEvents = () => {
  const {formatMessage} = useIntl();
  const theme = useTheme();

  const [isDialogOpen, setDialogOpen] = useState(false);
  const [dialogStep, setDialogStep] = useState(DialogSteps.SelectFields);

  const [selectedFieldIds, setSelectedFieldIds] = useState<Array<string>>([]);

  const handleClose = () => {
    setDialogOpen(false);
    setSelectedFieldIds([]);
    setDialogStep(DialogSteps.SelectFields);
  };

  const {copyFieldEvents, copyFieldEventsLoading, fieldEventsCount, fieldEventsCountLoading} =
    useCopyFieldEvents({
      selectedFieldIds,
    });

  const fieldsWithEventsCount = useMemo(() => {
    if (!fieldEventsCount) return 0;

    return Object.values(fieldEventsCount).filter(field =>
      field.eventTypeCounts ? Object.values(field.eventTypeCounts).some(Boolean) : false
    ).length;
  }, [fieldEventsCount]);

  const handleCopyFieldEvents = async (copyMode: CopyFieldEventsMode) => {
    try {
      const result = await copyFieldEvents(copyMode);
      handleClose();
      return result;
    } catch (error) {
      return null;
    }
  };

  const handleNextStep = async () => {
    if (selectedFieldIds.length === 0 || !fieldEventsCount) return;
    if (fieldsWithEventsCount > 0) {
      setDialogStep(DialogSteps.Confirm);
    } else {
      await handleCopyFieldEvents(CopyFieldEventsMode.Overwrite);
    }
  };

  return (
    <>
      <SimpleDialog
        open={isDialogOpen}
        onClose={handleClose}
        maxWidth={dialogStep === DialogSteps.SelectFields ? 'sm' : 'md'}
        aria-labelledby="copy-field-events-dialog-title"
        title={formatMessage({
          id: 'copyFieldEvents.dialog.title',
          defaultMessage: 'Copy events to fields',
        })}
      >
        {dialogStep === DialogSteps.Confirm ? (
          <ConfirmCopy
            selectedFieldIds={selectedFieldIds}
            handlePreviousStep={() => {
              setDialogStep(DialogSteps.SelectFields);
            }}
            copyFieldEvents={handleCopyFieldEvents}
            fieldEventsCount={fieldEventsCount}
            fieldsWithEventsCount={fieldsWithEventsCount}
            isLoading={copyFieldEventsLoading}
          />
        ) : (
          <SelectFieldsToCopy
            selectedFieldIds={selectedFieldIds}
            setSelectedFieldIds={setSelectedFieldIds}
            handleNextStep={handleNextStep}
            handleClose={handleClose}
            isLoading={fieldEventsCountLoading}
            copyEventsLoading={copyFieldEventsLoading}
          />
        )}
      </SimpleDialog>
      <Button
        onClick={() => {
          setDialogOpen(true);
        }}
        color="secondary"
        variant="text"
        size="medium"
        startIcon={<SvgIcon type="copy" />}
        sx={{
          color: theme.palette.semanticPalette.textInverted.main,
          backgroundColor: 'transparent',
          '&:hover': {
            backgroundColor: theme.palette.semanticPalette.surfaceInverted.secondary,
          },
        }}
      >
        {formatMessage({
          id: 'copyFieldEvents.trigger.button',
          defaultMessage: 'Copy to...',
        })}
      </Button>
    </>
  );
};
