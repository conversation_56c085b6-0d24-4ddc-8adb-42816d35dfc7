import {getFragmentData} from '__generated__/gql/fragment-masking';
import {
  FieldEventAttributesFragmentDoc,
  FieldWithEventsFragmentDoc,
} from '__generated__/gql/graphql';
import type {GetPhaseFieldCultivationCycleEventsQuery} from '__generated__/gql/graphql';

import type {FieldHasEvents} from 'views/mrv/project/phases/stages/field-events/update-events/copy-field-events/useCopyFieldEvents';

/**
 * Processes field event data from all phases and stages to count event types
 * @param phases - a list of MRV phases with stage and event data
 * @param fieldId - the ID of the field being processed
 * @returns FieldEventCount object with fieldId, fieldName, and eventTypeCounts
 */
export const initialFieldHasEventsState: FieldHasEvents = {
  field: null,
  fieldHasEvents: null,
};

export const getFieldEventCount = (
  phases:
    | NonNullable<
        NonNullable<GetPhaseFieldCultivationCycleEventsQuery['mrv']['project']>['program']
      >['phases']
    | undefined
): FieldHasEvents => {
  const initialState: FieldHasEvents = {...initialFieldHasEventsState};

  const eventTypeCounts =
    phases?.reduce((phaseAcc, phase) => {
      const stages = phase?.stages || [];

      return stages.reduce((stageAcc, stage) => {
        const fieldData = stage?.field;
        if (!fieldData) return stageAcc;

        const fieldWithEvents = getFragmentData(FieldWithEventsFragmentDoc, fieldData);
        if (!initialState.field) {
          initialState.field = {
            id: fieldWithEvents?.id,
            name: fieldData?.name,
          };
        }
        if (!fieldWithEvents?.cultivation_cycles) return stageAcc;

        return fieldWithEvents.cultivation_cycles.reduce((cycleAcc, cycle) => {
          if (!cycle?.events) return cycleAcc;

          return cycle.events.reduce((eventAcc, event) => {
            const eventData = getFragmentData(FieldEventAttributesFragmentDoc, event);
            const eventType = eventData?.type;

            if (eventType && !eventAcc.fieldHasEvents?.[eventType]) {
              return {
                ...eventAcc,
                fieldHasEvents: {
                  ...eventAcc.fieldHasEvents,
                  [eventType]: true,
                },
              };
            }

            return eventAcc;
          }, cycleAcc);
        }, stageAcc);
      }, phaseAcc);
    }, initialState) || initialState;

  return eventTypeCounts;
};
