import React, {useMemo} from 'react';
import {useIntl} from 'react-intl';

import {
  Autocomplete,
  Box,
  Button,
  DialogActions,
  DialogContent,
  LoadingButton,
  Skeleton,
  Typography,
  useTheme,
} from '@regrow-internal/design-system';

import {isString} from '_common/utils/typeGuards';

import type {FieldQueryResponse} from 'views/mrv/project/phases/stages/SelectedFieldContext';
import {useSelectedFieldContext} from 'views/mrv/project/phases/stages/SelectedFieldContext';

interface FieldOption {
  value: string;
  label: string;
  group: string;
  fieldData: FieldQueryResponse;
}

export const SelectFieldsToCopy = ({
  selectedFieldIds = [],
  setSelectedFieldIds,
  handleClose,
  handleNextStep,
  isLoading,
  disabled = false,
}: {
  selectedFieldIds: Array<string>;
  setSelectedFieldIds: (fieldIds: Array<string>) => void;
  handleClose: () => void;
  handleNextStep: () => Promise<void>;
  isLoading: boolean;
  disabled: boolean;
}) => {
  const theme = useTheme();
  const {formatMessage} = useIntl();

  const {farms, selectedFieldEvents} = useSelectedFieldContext();
  const {field} = selectedFieldEvents;

  const allFieldOptions = useMemo(() => {
    if (!farms) return [];

    return farms.flatMap(farm =>
      (farm.fields || [])
        .filter(farmField => farmField.id !== field?.id)
        .map(farmField => ({
          value: farmField.id,
          label: `${farmField.name} (#${farmField.id})`,
          group: farm.name,
          fieldData: farmField,
        }))
    );
  }, [farms, field?.id]);

  const selectedFields = useMemo(() => {
    return allFieldOptions.filter(fieldOption => selectedFieldIds.includes(fieldOption.value));
  }, [allFieldOptions, selectedFieldIds]);

  const handleAutocompleteChange = (_event: unknown, newValue: Array<FieldOption>) => {
    setSelectedFieldIds(newValue.map(fieldOption => fieldOption.value));
  };

  return (
    <>
      <DialogContent>
        <Box sx={{mb: 2}}>
          <Typography variant="body2" color="text.secondary">
            {formatMessage({
              id: 'copyFieldEvents.selectFields.description',
              defaultMessage: 'Select fields to copy events to.',
            })}
          </Typography>
        </Box>

        <Autocomplete
          multiple
          options={allFieldOptions}
          value={selectedFields}
          onChange={handleAutocompleteChange}
          groupBy={option => option.group}
          getOptionLabel={option => (isString(option) ? option : option.label)}
          isOptionEqualToValue={(option, value) => option.value === value.value}
          placeholder={formatMessage({
            id: 'copyFieldEvents.selectFields.placeholder',
            defaultMessage: 'No fields selected',
          })}
          localeText={{
            selectAll: formatMessage({
              id: 'copyFieldEvents.selectFields.selectAll',
              defaultMessage: 'Select all',
            }),
            deselectAll: formatMessage({
              id: 'copyFieldEvents.selectFields.deselectAll',
              defaultMessage: 'Deselect all',
            }),
            getLimitTagsText: () =>
              formatMessage(
                {
                  id: 'copyFieldEvents.selectFields.fieldsSelectedCount',
                  defaultMessage: '{count} fields selected',
                },
                {count: selectedFields.length}
              ),
            getLimitTagsTextFocused: () =>
              formatMessage(
                {
                  id: 'copyFieldEvents.selectFields.fieldsSelectedCount',
                  defaultMessage: '{count} fields selected',
                },
                {count: selectedFields.length}
              ),
          }}
          disableCloseOnSelect
          hasSelectAll
          sx={{mb: 2}}
        />

        {allFieldOptions.length === 0 && (
          <Box sx={{textAlign: 'center', py: 4}}>
            <Typography variant="body2" color="text.secondary">
              {formatMessage({
                id: 'copyFieldEvents.noFieldsAvailable',
                defaultMessage: 'No fields available for copying events.',
              })}
            </Typography>
          </Box>
        )}
      </DialogContent>

      <DialogActions>
        <Button onClick={handleClose} color="secondary" variant="outlined">
          {formatMessage({
            id: 'Cancel',
            defaultMessage: 'Cancel',
          })}
        </Button>

        <LoadingButton
          onClick={() => void handleNextStep()}
          variant="contained"
          disabled={selectedFieldIds.length === 0 || disabled}
          loading={isLoading}
        >
          {formatMessage({
            id: 'copyFieldEvents.buttons.copyToFields',
            defaultMessage: 'Copy to fields',
          })}
        </LoadingButton>
      </DialogActions>
    </>
  );
};
