import {getFragmentData} from '__generated__/gql/fragment-masking';
import {
  FieldEventAttributesFragmentDoc,
  FieldWithEventsFragmentDoc,
} from '__generated__/gql/graphql';
import type {GetPhaseFieldCultivationCycleEventsQuery} from '__generated__/gql/graphql';

import {FieldEventCount} from '../useCopyFieldEvents';

/**
 * Processes field event data from all phases and stages to count event types
 * @param phases - a list of MRV phases with stage and event data
 * @returns Record of event type counts for a single field
 */

export const processFieldEventCounts = (
  phases:
    | NonNullable<
        NonNullable<GetPhaseFieldCultivationCycleEventsQuery['mrv']['project']>['program']
      >['phases']
    | undefined
): FieldEventCount => {
  const initialCounts: Record<string, number> = {};

  const eventTypeCounts =
    phases?.reduce((phaseAcc, phase) => {
      const stages = phase?.stages || [];

      return stages.reduce((stageAcc, stage) => {
        const fieldData = stage?.field;
        if (!fieldData) return stageAcc;

        const fieldWithEvents = getFragmentData(FieldWithEventsFragmentDoc, fieldData);
        if (!fieldWithEvents?.cultivation_cycles) return stageAcc;

        return fieldWithEvents.cultivation_cycles.reduce((cycleAcc, cycle) => {
          if (!cycle?.events) return cycleAcc;

          return cycle.events.reduce((eventAcc, event) => {
            const eventData = getFragmentData(FieldEventAttributesFragmentDoc, event);
            const eventType = eventData?.type;

            if (eventType) {
              return {
                ...eventAcc,
                [eventType]: (eventAcc[eventType] || 0) + 1,
              };
            }

            return eventAcc;
          }, cycleAcc);
        }, stageAcc);
      }, phaseAcc);
    }, initialCounts) || initialCounts;

  return eventTypeCounts;
};
