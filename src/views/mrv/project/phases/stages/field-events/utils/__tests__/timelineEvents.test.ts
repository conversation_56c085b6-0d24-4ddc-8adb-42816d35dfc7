import {render, screen} from '@testing-library/react';

import {TimeScale} from '@regrow-internal/design-system';

import type {CroppingEvent, FieldWithEventsFragment, TillageEvent} from '__generated__/gql/graphql';
import {type FieldEvent} from '__generated__/gql/graphql';
import {AttributeTypes, StageTypes} from '__generated__/mrv/mrvApi.types';
import type {FormatMessage} from '_translations/types';

import {
  croppingEvents,
  tillageEvents,
} from 'views/mrv/project/phases/stages/__tests__/mock-data/mocks';
import type {TimelineRows} from 'views/mrv/project/phases/stages/field-events/FieldEventsTimeline';
import {FieldEventType} from 'views/mrv/project/phases/stages/field-events/types';
import type {StageWithFieldEventsFragment} from 'views/mrv/project/phases/stages/field-events/utils/timelineEvents';
import {
  createToolTip,
  getEarliestAndLatestEventDate,
  getEventColor,
  getEventEndDate,
  getEventStartDate,
  getTimelineEvent,
  getTimelineEventsForCultivationCycles,
  getTimelineRowForStage,
  getTimelineScale,
  sortAttributeEntriesByTooltipOrder,
} from 'views/mrv/project/phases/stages/field-events/utils/timelineEvents';

// eslint-disable-next-line @typescript-eslint/consistent-type-assertions
const mockFieldEvent = {
  type: FieldEventType.CroppingEvent,
  event_values: {
    __typename: FieldEventType.CroppingEvent,
    harvest_date: '2023-12-31',
  },
} as FieldEvent;

const mockFormatMessage: FormatMessage = ({defaultMessage}) =>
  typeof defaultMessage === 'string' ? defaultMessage : '';

describe('timelineEvents', () => {
  describe('getEventStartDate', () => {
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    const mockEvent = {
      ...mockFieldEvent,
      id: '1',
      event_values: {
        planting_date: '2023-01-01',
        __typename: FieldEventType.CroppingEvent,
      } as CroppingEvent,
    };

    it('should return the correct start date for cropping event', () => {
      expect(getEventStartDate(mockEvent.event_values)).toBe('2023-01-01');
    });

    it('should return null for events without a valid start date', () => {
      const unknownEvent = {id: '1', event_values: {}};
      expect(getEventStartDate(unknownEvent.event_values)).toBeNull();
    });
  });

  describe('getEventEndDate', () => {
    it('should return the correct end date for cropping event', () => {
      expect(getEventEndDate(mockFieldEvent.event_values)).toBe('2023-12-31');
    });

    it('should return null for events without an end date', () => {
      const noEndDateEvent = {
        ...mockFieldEvent,
        event_values: {__typename: undefined, harvest_date: undefined},
      };
      expect(getEventEndDate(noEndDateEvent.event_values)).toBeNull();
    });
  });

  describe('getEventColor', () => {
    it('should return the correct color for each event type', () => {
      expect(getEventColor({...mockFieldEvent, __typename: FieldEventType.CroppingEvent})).toBe(
        '1'
      );
      expect(getEventColor({...mockFieldEvent, __typename: FieldEventType.TillageEvent})).toBe('4');
      expect(getEventColor({...mockFieldEvent, __typename: FieldEventType.ApplicationEvent})).toBe(
        '5'
      );
      expect(getEventColor({...mockFieldEvent, __typename: FieldEventType.IrrigationEvent})).toBe(
        '2'
      );
    });

    it('should return green for unknown event types', () => {
      expect(getEventColor({...mockFieldEvent, __typename: undefined})).toBe('1');
    });
  });

  describe('getTimelineScale', () => {
    it('should return Biannual if the date difference is greater than 18 months', () => {
      const earliest = new Date(2020, 0);
      const latest = new Date(2022, 6);
      expect(getTimelineScale(earliest, latest)).toBe(TimeScale.Biannual);
    });

    it('should return Monthly if the date difference is 18 months or less', () => {
      const earliest = new Date(2023, 0);
      const latest = new Date(2024, 4);
      expect(getTimelineScale(earliest, latest)).toBe(TimeScale.Monthly);
    });

    it('should return Monthly when event dates are not set', () => {
      expect(getTimelineScale(null, null)).toBe(TimeScale.Monthly);
    });
  });

  describe('createToolTip', () => {
    it('should create a tooltip for the given event with all event attributes', () => {
      const mockCroppingEvent = {
        ...mockFieldEvent,
        type: FieldEventType.CroppingEvent,
        event_values: {
          crop_type: 'Wheat',
          planting_date: '2023-03-15',
          harvest_date: '2023-08-20',
        },
      };

      const mockTillageEvent = {
        ...mockFieldEvent,
        id: '1',
        type: FieldEventType.TillageEvent,
        event_values: {
          tillage_date: '2023-03-15',
          soil_inversion: true,
          __typename: FieldEventType.TillageEvent,
        } as TillageEvent,
      };

      const {container: croppingEventContainer} = render(
        createToolTip(mockCroppingEvent.event_values, mockFormatMessage)
      );

      screen.debug(croppingEventContainer);

      expect(croppingEventContainer).toHaveTextContent('crop_type: Wheat');
      expect(croppingEventContainer).toHaveTextContent('harvest_date: 2023-08-20');
      expect(croppingEventContainer).toHaveTextContent('planting_date: 2023-03-15');

      expect(croppingEventContainer).not.toHaveTextContent('Id');
      expect(croppingEventContainer).not.toHaveTextContent('__typename');

      const {container: tillageEventContainer} = render(
        createToolTip(mockTillageEvent.event_values, mockFormatMessage)
      );

      expect(tillageEventContainer).toHaveTextContent('tillage_date: 2023-03-15');
      expect(tillageEventContainer).toHaveTextContent('soil_inversion: true');

      expect(tillageEventContainer).not.toHaveTextContent('Id');
      expect(tillageEventContainer).not.toHaveTextContent('__typename');
    });
  });

  describe('sortAttributeEntriesByTooltipOrder', () => {
    it('should sort attributes based on the order defined for the event type', () => {
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
      const entries = [
        [AttributeTypes.HarvestDate, '2023-10-01'],
        [AttributeTypes.CropType, 'Wheat'],
        [AttributeTypes.PlantingDate, '2023-03-01'],
      ] as Array<[string, string | number | boolean | null]>;

      const result = sortAttributeEntriesByTooltipOrder(entries, FieldEventType.CroppingEvent);

      expect(result).toEqual([
        [AttributeTypes.CropType, 'Wheat'],
        [AttributeTypes.PlantingDate, '2023-03-01'],
        [AttributeTypes.HarvestDate, '2023-10-01'],
      ]);
    });

    it('should leave attributes unsorted if the event type is not valid', () => {
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
      const entries = [
        ['SomeAttribute', 'Value1'],
        ['AnotherAttribute', 'Value2'],
      ] as Array<[string, string | number | boolean | null]>;

      const result = sortAttributeEntriesByTooltipOrder(entries, 'InvalidEventType');

      expect(result).toEqual(entries);
    });

    it('should return an empty array if entries are empty', () => {
      const entries: Array<[string, string | number | boolean | null]> = [];

      const result = sortAttributeEntriesByTooltipOrder(entries, FieldEventType.CroppingEvent);

      expect(result).toEqual([]);
    });

    it('should handle null values in entries gracefully', () => {
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
      const entries = [
        [AttributeTypes.CropType, null],
        [AttributeTypes.HarvestDate, '2023-10-01'],
      ] as Array<[string, string | number | boolean | null]>;

      const result = sortAttributeEntriesByTooltipOrder(entries, FieldEventType.CroppingEvent);

      expect(result).toEqual([
        [AttributeTypes.CropType, null],
        [AttributeTypes.HarvestDate, '2023-10-01'],
      ]);
    });
  });

  describe('getTimelineEvent', () => {
    it('should return a TimelineEvent when startDate is defined', () => {
      const mockEvent: FieldEvent = {
        ...mockFieldEvent,
        event_values: {
          __typename: FieldEventType.CroppingEvent,
          crop_type: 'Wheat',
          planting_date: '2023-03-15',
          harvest_date: '2023-08-20',
        },
      };
      const result = getTimelineEvent(mockEvent, mockFormatMessage, []);

      expect(result).toEqual(
        expect.objectContaining({
          id: mockFieldEvent.id,
          title: 'Wheat',
          icon: 'generic',
          startDate: new Date('2023-03-15'),
          endDate: new Date('2023-08-20'),
          color: '1',
        })
      );
    });

    it('should return null for end date when it is not a valid date', () => {
      const mockEvent: FieldEvent = {
        ...mockFieldEvent,
        event_values: {
          __typename: FieldEventType.CroppingEvent,
          crop_type: 'Wheat',
          planting_date: '2023-03-15',
          harvest_date: 'asfasdf',
        },
      };
      const result = getTimelineEvent(mockEvent, mockFormatMessage, []);

      expect(result).toEqual(
        expect.objectContaining({
          id: mockFieldEvent.id,
          title: 'Wheat',
          icon: 'generic',
          startDate: new Date('2023-03-15'),
          endDate: null,
          color: '1',
        })
      );
    });

    it('should return null when startDate is not defined', () => {
      const mockEvent2 = {
        ...mockFieldEvent,
        event_values: {
          ...mockFieldEvent.event_values,
          planting_date: undefined,
        },
      };
      const result = getTimelineEvent(mockEvent2, mockFormatMessage, []);

      expect(result).toBeNull();
    });

    it('should return null when startDate is not a valid date', () => {
      const mockEvent2 = {
        ...mockFieldEvent,
        event_values: {
          ...mockFieldEvent.event_values,
          planting_date: '',
        },
      };
      const result = getTimelineEvent(mockEvent2, mockFormatMessage, []);
      expect(result).toBeNull();
    });

    it('should return a TimelineEvent with null endDate when endDate is not defined', () => {
      const mockEvent3: FieldEvent = {
        ...mockFieldEvent,
        event_values: {
          __typename: FieldEventType.CroppingEvent,
          crop_type: 'Wheat',
          planting_date: '2023-03-15',
          harvest_date: undefined,
        },
      };
      const result = getTimelineEvent(mockEvent3, mockFormatMessage, []);

      expect(result).toEqual(
        expect.objectContaining({
          id: mockFieldEvent.id,
          title: 'Wheat',
          icon: 'generic',
          startDate: new Date('2023-03-15'),
          endDate: null,
          color: '1',
        })
      );
    });

    it('should set the title as an empty string when the event is not a cropping event', () => {
      const tillageEvent: FieldEvent = {
        ...mockFieldEvent,
        type: FieldEventType.TillageEvent,
        event_values: {tillage_date: '2023-03-15', __typename: FieldEventType.TillageEvent},
      };

      const result = getTimelineEvent(tillageEvent, mockFormatMessage, []);

      expect(result).toEqual(
        expect.objectContaining({
          id: mockFieldEvent.id,
          title: '',
          startDate: new Date('2023-03-15'),
          endDate: null,
          color: '4',
        })
      );
    });
  });

  describe('getTimelineEventsForCultivationCycles', () => {
    const events = [...tillageEvents(), ...croppingEvents()] as Array<FieldEvent>;
    const cultivationCycles: FieldWithEventsFragment['cultivation_cycles'] = [
      {
        id: 'ccId',
        start_date: '2020-01-01',
        end_date: '2020-05-01',
        events: events,
      },
      {
        id: 'ccId2',
        start_date: '2020-03-15',
        end_date: '2020-08-20',
        events: events,
      },
    ];
    it('should return an array of TimelineEvents for each cultivation cycle', () => {
      const result = getTimelineEventsForCultivationCycles(
        cultivationCycles,
        mockFormatMessage,
        []
      );

      expect(result).toHaveLength(events.length * cultivationCycles.length);
      expect(result).toEqual([
        {
          color: '4',
          endDate: null,
          icon: undefined,
          id: expect.any(String),
          startDate: new Date('2024-01-05T00:00:00.000Z'),
          title: '',
          tooltip: expect.anything(),
          outline: false,
        },
        {
          color: '1',
          endDate: new Date('2024-09-02T00:00:00.000Z'),
          icon: 'wheat_winter',
          id: expect.any(String),
          startDate: new Date('2024-02-02T00:00:00.000Z'),
          title: 'wheat_winter',
          tooltip: expect.anything(),
          outline: false,
        },

        {
          color: '0',
          endDate: new Date('2024-04-30T00:00:00.000Z'),
          icon: 'corn',
          id: expect.any(String),
          startDate: new Date('2024-02-02T00:00:00.000Z'),
          title: 'corn',
          tooltip: expect.anything(),
          outline: false,
        },
        {
          color: '4',
          endDate: null,
          icon: undefined,
          id: expect.any(String),
          startDate: new Date('2024-01-05T00:00:00.000Z'),
          title: '',
          tooltip: expect.anything(),
          outline: false,
        },
        {
          color: '1',
          endDate: new Date('2024-09-02T00:00:00.000Z'),
          icon: 'wheat_winter',
          id: expect.any(String),
          startDate: new Date('2024-02-02T00:00:00.000Z'),
          title: 'wheat_winter',
          tooltip: expect.anything(),
          outline: false,
        },
        {
          color: '0',
          endDate: new Date('2024-04-30T00:00:00.000Z'),
          icon: 'corn',
          id: expect.any(String),
          startDate: new Date('2024-02-02T00:00:00.000Z'),
          title: 'corn',
          tooltip: expect.anything(),
          outline: false,
        },
      ]);
    });
    it('should handle empty cultivation cycles', () => {
      const result = getTimelineEventsForCultivationCycles(null, mockFormatMessage, []);
      expect(result).toEqual([]);
    });

    it('should handle empty events', () => {
      const result = getTimelineEventsForCultivationCycles(
        [
          {
            id: 'ccId',
            start_date: '2020-01-01',
            end_date: '2020-05-01',
            events: null,
          },
        ],
        mockFormatMessage,
        []
      );
      expect(result).toEqual([]);
    });
  });

  describe('getTimelineRowForStage', () => {
    const cropEvent = croppingEvents()[0];
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    const stage = {
      id: '1',
      type: StageTypes.CROP_EVENTS,
      field: {
        cultivation_cycles: [
          {
            id: 'ccId',
            start_date: '2020-01-01',
            end_date: '2020-05-01',
            events: [cropEvent],
          },
        ],
      },
    } as StageWithFieldEventsFragment;
    it('should get timeline rows for stage', () => {
      const result = getTimelineRowForStage(stage, mockFormatMessage, []);

      expect(result).toEqual({
        events: [
          {
            color: '1',
            endDate: new Date('2024-09-02T00:00:00.000Z'),
            icon: 'wheat_winter',
            id: cropEvent?.id,
            startDate: new Date('2024-02-02T00:00:00.000Z'),
            title: 'wheat_winter',
            tooltip: expect.anything(),
            outline: false,
          },
        ],
        id: '1',
        title: 'Crops',
      });
    });

    it('should return null for non-event stages', () => {
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
      const emptyStage = {
        id: '3',
        type: StageTypes.FIELD_BOUNDARIES,
        field: {
          cultivation_cycles: [],
        },
      } as StageWithFieldEventsFragment;

      const result = getTimelineRowForStage(emptyStage, mockFormatMessage, []);
      expect(result).toBeNull();
    });
  });

  describe('getEarliestAndLatestEventDate', () => {
    it('should get earliest and latest event dates within a stage', () => {
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
      const stage = {
        id: '1',
        type: StageTypes.CROP_EVENTS,
        field: {
          cultivation_cycles: [
            {
              id: 'ccId',
              start_date: '2020-01-01',
              end_date: '2020-05-01',
              events: croppingEvents(),
            },
          ],
        },
      } as StageWithFieldEventsFragment;

      const row = getTimelineRowForStage(stage, mockFormatMessage, []);
      if (!row) {
        throw Error('invalid test data for row');
      }

      const result = getEarliestAndLatestEventDate([row]);
      expect(result).toEqual({
        earliestDate: new Date('2024-02-02T00:00:00.000Z'),
        latestDate: new Date('2024-09-02T00:00:00.000Z'),
      });
    });

    it('should handle undefined end date', () => {
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
      const rows = [
        {
          id: '1',
          title: 'Crops',
          events: [
            {
              id: '198554',
              title: 'wheat_winter',
              icon: 'wheat_winter',
              startDate: new Date('2024-09-02T00:00:00.000Z'),
              endDate: null,
              color: '1',
              tooltip: '',
              outline: false,
            },
          ],
        },
      ] as TimelineRows;

      const result = getEarliestAndLatestEventDate(rows);
      expect(result).toEqual({
        earliestDate: new Date('2024-09-02T00:00:00.000Z'),
        latestDate: null,
      });
    });
  });
});
