import {isValid} from 'date-fns';
import type {ReactElement} from 'react';
import React from 'react';

import type {Lab, TimelineRow} from '@regrow-internal/design-system';
import {Box, TimeScale, Typography, type TimelineEvent} from '@regrow-internal/design-system';

import {getFragmentData} from '__generated__/gql/fragment-masking';
import type {FieldWithEventsFragment, Stage} from '__generated__/gql/graphql';
import {
  FieldEventAttributesFragmentDoc,
  FieldWithEventsFragmentDoc,
  type CroppingEvent,
  type FieldEvent,
  type FieldEventValues,
} from '__generated__/gql/graphql';
import {AttributeTypes} from '__generated__/mrv/mrvApi.types';
import {getDSCropName} from '_common/components/crop-avatar/crop-avatar-ebdc';
import {capitalizeFirstLetter} from '_common/utils/string';
import {isDefined} from '_common/utils/typeGuards';
import type {FormatMessage} from '_translations/types';

import {stageTypeNames} from 'containers/mrv/constants';
import {FALLOW_CROP_TYPE} from 'views/mrv/project/phases/stages/field-events/field-event-table/cropping-events/getCropColumns';
import type {TimelineRows} from 'views/mrv/project/phases/stages/field-events/FieldEventsTimeline';
import {FieldEventType} from 'views/mrv/project/phases/stages/field-events/types';

import {getEventTypeFromStageType} from './fieldEventUtils';
import {formatCropTypeForDisplay, formatUnknownOptionForDisplay} from './formatAttributeOptions';

const TOOLTIP_ATTRIBUTE_ORDER_BY_EVENT = {
  [FieldEventType.CroppingEvent]: [
    AttributeTypes.CropType,
    AttributeTypes.CropUsage,
    AttributeTypes.PlantingDate,
    AttributeTypes.HarvestDate,
    AttributeTypes.TerminationMethod,
    AttributeTypes.CropYield,
    AttributeTypes.YieldRateUnit,
    AttributeTypes.ResidueHarvested,
  ],
  [FieldEventType.TillageEvent]: [
    AttributeTypes.TillagePractice,
    AttributeTypes.TillageDate,
    AttributeTypes.TillageDepth,
    AttributeTypes.TillageDepthUnit,
    AttributeTypes.SoilInversion,
  ],
  [FieldEventType.ApplicationEvent]: [
    AttributeTypes.ApplicationDate,
    AttributeTypes.ApplicationProduct,
    AttributeTypes.ApplicationRateType,
    AttributeTypes.ApplicationRate,
    AttributeTypes.ApplicationRateUnit,
    AttributeTypes.ApplicationMethod,
    AttributeTypes.ApplicationDepth,
    AttributeTypes.ApplicationDepthUnit,
    AttributeTypes.WaterAmount,
    AttributeTypes.WaterAmountUnit,
    AttributeTypes.Additives,
  ],
  [FieldEventType.IrrigationEvent]: [
    AttributeTypes.StartDate,
    AttributeTypes.EndDate,
    AttributeTypes.IrrigationMethod,
    AttributeTypes.SubsurfaceDripDepth,
    AttributeTypes.SubsurfaceDripDepthUnit,
    AttributeTypes.FloodPct,
  ],
} as const;

export const getEventStartDate = (event: FieldEventValues) => {
  switch (event?.__typename) {
    case FieldEventType.FallowPeriod:
      return event.start_date;
    case FieldEventType.CroppingEvent:
      return event.planting_date;
    case FieldEventType.TillageEvent:
      return event.tillage_date;
    case FieldEventType.ApplicationEvent:
      return event.application_date;
    case FieldEventType.IrrigationEvent:
      return event.start_date;
    default:
      return null;
  }
};

export const getEventEndDate = (event: FieldEventValues) => {
  switch (event?.__typename) {
    case FieldEventType.FallowPeriod:
      return event.end_date;
    case FieldEventType.CroppingEvent:
      return event.harvest_date;
    case FieldEventType.TillageEvent:
      return null;
    case FieldEventType.ApplicationEvent:
      return null;
    case FieldEventType.IrrigationEvent:
      return event.end_date;
    default:
      return null;
  }
};

export const getEventColor = (event: FieldEventValues): TimelineEvent['color'] => {
  switch (event?.__typename) {
    case FieldEventType.FallowPeriod:
      return '9';
    case FieldEventType.CroppingEvent:
      if (event.crop_usage === 'Cover') {
        return '0';
      }
      return '1';
    case FieldEventType.TillageEvent:
      return '4';
    case FieldEventType.ApplicationEvent:
      return '5';
    case FieldEventType.IrrigationEvent:
      return '2';
    default:
      return '1';
  }
};

const monthDiff = (dateFrom: Date, dateTo: Date) => {
  const difference =
    dateTo.getMonth() - dateFrom.getMonth() + 12 * (dateTo.getFullYear() - dateFrom.getFullYear());

  return difference > 18 ? TimeScale.Biannual : TimeScale.Monthly;
};

export const getTimelineScale = (earliestEventDate: Date | null, latestEventDate: Date | null) =>
  isDefined(earliestEventDate) && isDefined(latestEventDate)
    ? monthDiff(earliestEventDate, latestEventDate)
    : TimeScale.Monthly;

const isValidEventType = (type: string): type is keyof typeof TOOLTIP_ATTRIBUTE_ORDER_BY_EVENT =>
  type in TOOLTIP_ATTRIBUTE_ORDER_BY_EVENT;

export const sortAttributeEntriesByTooltipOrder = (
  entries: Array<[string, string | number | boolean | null]>,
  eventType: string
): Array<[string, string | number | boolean | null]> => {
  const order = isValidEventType(eventType) ? TOOLTIP_ATTRIBUTE_ORDER_BY_EVENT[eventType] : [];
  const orderMap = new Map<string, number>(order.map((attr, i) => [attr, i]));

  return entries.sort(([aKey], [bKey]) => {
    const aIndex = orderMap.get(aKey) ?? Infinity;
    const bIndex = orderMap.get(bKey) ?? Infinity;

    return aIndex - bIndex;
  });
};

export const createToolTip = (
  event: FieldEventValues,
  formatMessage: FormatMessage
): ReactElement => {
  const arrayOfEvents = Object.entries(event);

  const sorted = event.__typename
    ? sortAttributeEntriesByTooltipOrder(arrayOfEvents, event.__typename)
    : arrayOfEvents;

  const toolTipData = sorted.map(([key, value]) => {
    if (key === '__typename') {
      return null;
    }
    const eventAttributeValue = value !== null && typeof value === 'object' ? value : value;

    if (isDefined(eventAttributeValue)) {
      const attributeValue =
        typeof eventAttributeValue === 'string'
          ? capitalizeFirstLetter(eventAttributeValue)
          : eventAttributeValue?.toString();

      const formattedAttributeValue = formatUnknownOptionForDisplay(
        attributeValue,
        key,
        formatMessage
      );

      const formattedAttributeKey = formatMessage({
        id: `MRVAttributeType.${key}`,
        defaultMessage: key,
      });

      return (
        <Box key={key}>
          <Typography variant="body2">
            <Typography fontWeight="bold" component="span" variant="body2">
              {formattedAttributeKey}
            </Typography>
            : {formattedAttributeValue}
          </Typography>
        </Box>
      );
    }
    return null;
  });

  return <Box>{toolTipData.filter(Boolean)}</Box>;
};

export const getTimelineEvent = (
  event: FieldEvent,
  formatMessage: FormatMessage,
  selectedRows: Lab.GridInputRowSelectionModel
): TimelineEvent | null => {
  let eventVals = event.event_values;
  const startDate = getEventStartDate(eventVals);
  const endDate = getEventEndDate(eventVals);

  if (!isDefined(startDate)) {
    return null;
  }

  const parsedStartDate = new Date(startDate);
  if (!isValid(parsedStartDate)) {
    return null;
  }

  const parsedEndDate = isDefined(endDate) ? new Date(endDate) : null;

  let cropType = null;
  let title = '';
  if (
    eventVals?.__typename === FieldEventType.CroppingEvent ||
    event.type === FieldEventType.CroppingEvent
  ) {
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    const eventValsTyped = eventVals as CroppingEvent;
    cropType = eventValsTyped.crop_type;

    if (cropType) {
      title = formatCropTypeForDisplay(cropType, formatMessage);
      eventVals = {
        ...eventValsTyped,
        crop_type: title,
      };
    }
  }

  if (eventVals?.__typename === FieldEventType.FallowPeriod) {
    title = capitalizeFirstLetter(FALLOW_CROP_TYPE);
    cropType = FALLOW_CROP_TYPE;
  }

  const isSelected =
    (Array.isArray(selectedRows) && selectedRows.includes(event.id)) || selectedRows === event.id;

  return {
    id: event.id,
    title,
    icon: eventVals && cropType ? getDSCropName(cropType) : undefined,
    startDate: parsedStartDate,
    endDate: isValid(parsedEndDate) ? parsedEndDate : null,
    color: getEventColor(eventVals),
    tooltip: createToolTip(eventVals, formatMessage),
    outline: isSelected,
  };
};

const emptyResponse: Array<TimelineEvent> = [];
export const getTimelineEventsForCultivationCycles = (
  cultivationCycles: FieldWithEventsFragment['cultivation_cycles'],
  formatMessage: FormatMessage,
  selectedRows: Lab.GridInputRowSelectionModel
): Array<TimelineEvent> => {
  if (!cultivationCycles?.length) return emptyResponse;
  return cultivationCycles.flatMap(cc => {
    const typedEvents = getFragmentData(FieldEventAttributesFragmentDoc, cc.events);
    if (!typedEvents) return emptyResponse;

    const timelineEvents = typedEvents
      .map(event => getTimelineEvent(event, formatMessage, selectedRows))
      .filter(isDefined);

    return timelineEvents;
  });
};

export type StageWithFieldEventsFragment = {
  id: Stage['id'];
  type?: Stage['type'];
  field?: {
    ' $fragmentRefs'?: {
      FieldWithEventsFragment: FieldWithEventsFragment;
    };
  } | null;
};

export const getTimelineRowForStage = (
  stage: StageWithFieldEventsFragment,
  formatMessage: FormatMessage,
  selectedRows: Lab.GridInputRowSelectionModel
): TimelineRow | null => {
  const eventType = isDefined(stage?.type) && getEventTypeFromStageType(stage.type);
  if (eventType && stage.field) {
    const typedField = getFragmentData(FieldWithEventsFragmentDoc, stage.field);
    if (!typedField?.cultivation_cycles) return null;

    const timelineEvents = getTimelineEventsForCultivationCycles(
      typedField.cultivation_cycles,
      formatMessage,
      selectedRows
    );
    const getTitle = stage.type ? stageTypeNames[stage.type] : null;

    return {
      id: stage.id,
      title: getTitle ? getTitle() : '',
      events: timelineEvents,
    };
  }
  return null;
};

export const getEarliestAndLatestEventDate = (
  rows: TimelineRows
): {earliestDate: Date | null; latestDate: Date | null} => {
  let earliestDate: Date | null = null;
  let latestDate: Date | null = null;

  rows.forEach(row => {
    row.events.forEach(event => {
      if (event.startDate && (!earliestDate || event.startDate < earliestDate)) {
        earliestDate = event.startDate;
      }

      if (event.endDate && (!latestDate || event.endDate > latestDate)) {
        latestDate = event.endDate;
      }
    });
  });

  return {earliestDate, latestDate};
};
