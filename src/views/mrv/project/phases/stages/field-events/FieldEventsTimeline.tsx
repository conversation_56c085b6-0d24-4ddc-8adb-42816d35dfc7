import {NetworkStatus, useQuery} from '@apollo/client';
import React, {useMemo} from 'react';
import {useIntl} from 'react-intl';

import type {TimelineRow} from '@regrow-internal/design-system';
import {Box, Skeleton, Timeline} from '@regrow-internal/design-system';

import {RequestStatus} from 'types';

import {PhaseTypes} from '__generated__/mrv/mrvApi.types';
import {useParsedMatchParams} from '_common/hooks/use-parsed-match-params';
import {isDefined} from '_common/utils/typeGuards';
import {useDateFnsLocale} from '_translations/utils';

import {useFeatureEnabled} from 'containers/mrv/_hooks/useFeatures';
import {FEATURE_FLAGS} from 'containers/mrv/constants';
import {getDataCollectionStartEndDatesByPhase} from 'containers/mrv/utils';
import {useMonitorPrefillImportFieldStatus} from 'containers/profile/fms-integration/hooks';
import {GET_PHASE_CULTIVATION_CYCLE_EVENTS} from 'views/mrv/graphql/queries/field-events';
import {usePhaseContext} from 'views/mrv/project/phases/PhaseContext';
import {
  getEarliestAndLatestEventDate,
  getTimelineRowForStage,
  getTimelineScale,
} from 'views/mrv/project/phases/stages/field-events/utils/timelineEvents';
import {useSelectedFieldContext} from 'views/mrv/project/phases/stages/SelectedFieldContext';
import {useStageContext} from 'views/mrv/project/phases/stages/StageContext';

export type TimelineRows = Array<TimelineRow>;

export const FieldEventsTimeline = () => {
  const locale = useDateFnsLocale();
  const {formatMessage} = useIntl();
  const {projectId} = useParsedMatchParams<{projectId: string}>();
  const {selectedField, selectedFieldEvents} = useSelectedFieldContext();
  const {currentPhase, project} = usePhaseContext();
  const currentProgram = project?.program;
  const {currentStage} = useStageContext();
  const isIntendedPrefillEnabled = useFeatureEnabled(FEATURE_FLAGS.INTENDED_PRACTICES_PREFILL);

  const {
    data,
    error,
    networkStatus: fieldEventsLoading,
  } = useQuery(GET_PHASE_CULTIVATION_CYCLE_EVENTS, {
    variables: {
      fieldId: String(selectedField?.id),
      projectId,
      phaseType: String(currentPhase?.type),
      prefill_monitoring_phase: isIntendedPrefillEnabled,
    },
    skip: !selectedField || !currentPhase,
    fetchPolicy: 'network-only',
    nextFetchPolicy: 'cache-and-network',
    errorPolicy: 'all',
  });

  const isEnrolmentPhase = currentPhase?.type === PhaseTypes.ENROLMENT;

  const selectedEvents = useMemo(() => {
    if (!selectedFieldEvents || !selectedFieldEvents.events) return [];
    return Object.values(selectedFieldEvents.events).flat();
  }, [selectedFieldEvents]);

  const rows = useMemo(() => {
    const stages = data?.mrv?.project?.program?.phases?.[0]?.stages;
    if (!stages) return [];
    return stages.reduce((timelineRows: TimelineRows, stage) => {
      const timelineRow = getTimelineRowForStage(stage, formatMessage, selectedEvents);
      if (timelineRow) {
        timelineRows.push(timelineRow);
      }

      return timelineRows;
    }, []);
  }, [data?.mrv?.project?.program?.phases, formatMessage, selectedEvents]);

  const {startDate, endDate} = useMemo(
    () =>
      getDataCollectionStartEndDatesByPhase(
        currentProgram?.reporting_period_start_date,
        currentProgram?.reporting_period_end_date,
        currentProgram?.is_single_phase_data_collection,
        currentProgram?.required_years_of_history,
        currentPhase?.type
      ),
    [currentProgram, currentPhase]
  );

  const timelineScaleValue = useMemo(() => {
    const reporting_period_end_date = new Date(endDate);
    const reporting_period_start_date = new Date(startDate);

    const {earliestDate, latestDate} = getEarliestAndLatestEventDate(rows);

    const timelineStartDate =
      isDefined(earliestDate) && isDefined(reporting_period_start_date)
        ? new Date(Math.min(earliestDate.getTime(), reporting_period_start_date.getTime()))
        : reporting_period_start_date;

    const timelineEndDate =
      isDefined(latestDate) && isDefined(reporting_period_end_date)
        ? new Date(Math.max(latestDate.getTime(), reporting_period_end_date.getTime()))
        : reporting_period_end_date;

    return getTimelineScale(timelineStartDate, timelineEndDate);
  }, [startDate, endDate, rows]);

  const {fieldIsPrefilling, monitorImportStatus} = useMonitorPrefillImportFieldStatus(
    selectedField?.md5
  );

  const fieldDataLoading = fieldEventsLoading < NetworkStatus.ready && !rows.length;

  if (
    isEnrolmentPhase &&
    ((monitorImportStatus.syncStatus !== RequestStatus.Loading && fieldDataLoading) ||
      fieldIsPrefilling)
  ) {
    return (
      <Box height={240}>
        <Skeleton variant="rounded" height="100%" width="100%" />
      </Box>
    );
  }

  const emptyMessage = formatMessage(
    error && !rows.length
      ? {
          id: 'FieldEventsTimeline.Error',
          defaultMessage: 'There was an issue loading your timeline events.',
        }
      : {
          id: 'FieldEventsTimeline.Empty',
          defaultMessage: 'Fill out the table below to get started.',
        }
  );

  return (
    <Timeline
      ariaLabel="Field events timeline"
      locale={locale}
      rows={rows}
      initialStartDate={startDate}
      initialEndDate={endDate}
      timeScale={timelineScaleValue}
      highlightedRowId={currentStage?.id}
      emptyMessage={emptyMessage}
    />
  );
};
