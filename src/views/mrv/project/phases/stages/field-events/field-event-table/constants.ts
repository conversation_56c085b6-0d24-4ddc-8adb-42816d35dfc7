import {darken, type SxProps} from '@mui/system';

import {Lab, lighten, type Theme} from '@regrow-internal/design-system';

import {StageTypes} from '__generated__/mrv/mrvApi.types';

export const DATA_GRID_CELL_COLORS_RGB = {
  disabled: 'rgb(203 203 184 / 12%)',
  disabledHover: 'rgb(203 203 184 / 12%)',
  warning: 'rgb(255 156 59 / 12%)',
};

export const DATE_INPUT_MIN_WIDTH = 172;

export const eventTypeMap: Partial<Record<StageTypes, Array<string>>> = {
  [StageTypes.CROP_EVENTS]: ['sowing', 'harvest', 'planting', 'termination', 'fallow'],
  [StageTypes.TILLAGE_EVENTS]: ['tillage'],
};

export const ALLOWED_STAGES_MONITOR_PREFILL: Array<StageTypes> = [
  StageTypes.CROP_EVENTS,
  StageTypes.TILLAGE_EVENTS,
];

export const rowEditStateDefaultValue = {};
export const hiddenAttributesForEPhase = {
  crop_yield: false,
  yield_rate_unit: false,
};
export const intialDataGridState = {
  pinnedColumns: {
    left: [Lab.GRID_CHECKBOX_SELECTION_COL_DEF.field, 'cultivationCycle'],
    right: ['actions'],
  },
};

export const fieldEventsDataGridStyleOverrides: SxProps<Theme> = {
  width: '100%',
  flexDirection: 'column',
  '.MuiDataGrid-columnHeaderTitleContainer': {
    width: '100%',
    '.MuiDataGrid-columnHeaderTitleContainerContent': {
      width: '100%',
    },
  },
  // DataGrid row styles
  '.MuiDataGrid-row': {
    '&:hover': {
      backgroundColor: DATA_GRID_CELL_COLORS_RGB.disabled,
    },
    '&.Mui-hovered': {
      backgroundColor: DATA_GRID_CELL_COLORS_RGB.disabled,
    },
    '&.Mui-selected': {
      backgroundColor: 'transparent',
      '.MuiDataGrid-cell:not(.disabled-cell, .actions)': {
        backgroundColor: theme => theme.palette.semanticPalette.surface.info,
      },
      '&:hover, &.Mui-hovered': {
        backgroundColor: 'transparent',
        '.MuiDataGrid-cell:not(.disabled-cell, .actions)': {
          backgroundColor: theme => darken(theme.palette.semanticPalette.surface.info, 0.025),
        },
      },
    },
    '&--editing': {
      boxShadow: 'none',
    },
  },

  // Cell styles
  '.MuiDataGrid-cell.MuiDataGrid-cell--editing:focus-within': {
    border: `1px solid`,
  },

  // Disabled cell styles
  '.disabled-cell': {
    borderRight: theme => `1px solid ${theme.palette.background.default}`,
    borderLeft: theme => `1px solid ${theme.palette.background.default}`,
    backgroundColor: DATA_GRID_CELL_COLORS_RGB.disabled,
  },

  // Required cell styles
  '.required-cell': {
    borderRight: theme => `1px solid ${theme.palette.background.default}`,
    borderLeft: theme => `1px solid ${theme.palette.background.default}`,
    backgroundColor: DATA_GRID_CELL_COLORS_RGB.warning,
  },

  '.error-cell': {
    backgroundColor: theme => theme.palette.semanticPalette.surface.error,
  },

  // Selected & hovered disabled cell styles
  '.MuiDataGrid-row.Mui-selected:hover, .MuiDataGrid-row.Mui-hovered, .MuiDataGrid-row.Mui-selected.Mui-hovered':
    {
      '.disabled-cell': {
        backgroundColor: theme => lighten(theme.palette.semanticPalette.stroke.secondary, 0.1),
      },
    },

  // Input field styles
  '.MuiFilledInput-root': {
    '&.Mui-focused': {
      border: 'transparent',
    },
    '&.Mui-disabled': {
      backgroundColor: DATA_GRID_CELL_COLORS_RGB.disabled,
    },
    '&.Mui-disabled > *': {
      opacity: 0,
    },
  },
  '.row-loading': {
    opacity: 0.5,
    backgroundColor: DATA_GRID_CELL_COLORS_RGB.disabled,
  },
};
