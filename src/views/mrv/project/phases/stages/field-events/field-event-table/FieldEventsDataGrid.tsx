import uniqueId from 'lodash/uniqueId';
import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {useIntl} from 'react-intl';

import {Box, Lab, Skeleton} from '@regrow-internal/design-system';

import type {FieldEventValues} from '__generated__/gql/graphql';
import {PhaseTypes, StageTypes} from '__generated__/mrv/mrvApi.types';
import {isEmptyObject, isTruthy} from '_common/utils/typeGuards';

import {useFeatureEnabled} from 'containers/mrv/_hooks/useFeatures';
import {useHasPermissions} from 'containers/mrv/_hooks/useHasPermissions';
import {FEATURE_FLAGS} from 'containers/mrv/constants';
import {EPermissions} from 'containers/mrv/types';
import {useMonitorPrefillImportFieldStatus} from 'containers/profile/fms-integration/hooks';
import {usePhaseContext} from 'views/mrv/project/phases/PhaseContext';
import {useFieldEventContext} from 'views/mrv/project/phases/stages/field-events/FieldEventContext';
import {SelectedEventsSnackbar} from 'views/mrv/project/phases/stages/field-events/update-events/SelectedEventsSnackbar';
import {
  canSaveFieldEvent,
  createViewModeRowModel,
  getEventTypeFromStageType,
  getUpdatedRowData,
  haveEditableRowsChanged,
  isNewFieldEvent,
  isNPOSet,
  resetEditsIfEmpty,
} from 'views/mrv/project/phases/stages/field-events/utils/fieldEventUtils';
import {useSelectedFieldContext} from 'views/mrv/project/phases/stages/SelectedFieldContext';
import {useStageContext} from 'views/mrv/project/phases/stages/StageContext';

import {
  fieldEventsDataGridStyleOverrides,
  hiddenAttributesForEPhase,
  intialDataGridState,
  rowEditStateDefaultValue,
} from './constants';
import {CustomToolbarHeader} from './table-components/TableComponents';
import {
  NO_PRACTICE_OBSERVATION_BY_EVENT_TYPE,
  type EditableRow,
  type FieldEventGridColDef,
  type FieldEventRowModel,
} from './table-components/types';
import {UnsavedChangesDialog} from './table-components/UnsavedChangesDialog';
import {
  getDefaultFieldEventsGridColumns,
  getDefaultRowEditState,
  getRows,
  rowHasValidationErrors,
} from './table-components/utils';

export const FieldEventsDataGrid = <T = FieldEventValues,>({
  columns,
}: {
  columns: Array<FieldEventGridColDef<T>>;
}) => {
  const {formatMessage} = useIntl();
  const {currentStage} = useStageContext();
  const {currentPhase, project, phaseIsReadOnly} = usePhaseContext();
  const [canBypassProjectFieldEventLock] = useHasPermissions([
    EPermissions.BYPASS_PROJECT_FIELD_EVENTS_LOCK,
  ]);
  const canCopyFieldEvents = useFeatureEnabled(FEATURE_FLAGS.MRV_COPY_FIELD_EVENTS);
  const canEditWhenPhaseUnLockedAndEventLocked =
    canBypassProjectFieldEventLock && !currentPhase?.is_locked;

  const currentProgram = project?.program;
  const isEnrolmentPhase = currentPhase?.type === PhaseTypes.ENROLMENT;
  const isEPhaseAndSinglePhaseDataCollectionOff =
    isEnrolmentPhase && currentProgram?.is_single_phase_data_collection === false;
  const formattedStageType = useMemo(() => {
    const stageType = currentStage?.type?.split('_').join(' ').toLowerCase();

    return stageType || '';
  }, [currentStage]);

  const eventType = useMemo(
    () => getEventTypeFromStageType(String(currentStage?.type)),
    [currentStage?.type]
  );

  const {selectedField, selectedFieldEvents, setSelectedFieldEvents} = useSelectedFieldContext();

  const {
    fieldCultivationCycles,
    isLoading,
    deleteFieldEvent,
    createOrUpdateEvent,
    updateNoPracticeObservation,
    updateFieldEventsLoading,
    bulkCreateOrUpdateEvents,
    bulkCreateOrUpdateLoading,
  } = useFieldEventContext();

  const {monitorIsLoading, fieldIsPrefilling} = useMonitorPrefillImportFieldStatus(
    selectedField?.md5
  );
  const shouldDisplayLoadingForMonitorPrefill =
    isEnrolmentPhase && monitorIsLoading && fieldIsPrefilling;

  const apiRef = Lab.useGridApiRef();

  const [rowIsLoading, setRowIsLoading] = useState<Array<Lab.GridRowId>>([]);
  const [hasSaveableEdits, setHasSaveableEdits] = useState<Array<EditableRow>>([]);
  const [unsavedRows, setUnsavedRows] = useState<Array<FieldEventRowModel<T>>>([]);

  const rows = useMemo(
    () =>
      !isLoading && fieldCultivationCycles
        ? [...unsavedRows, ...getRows<T>(fieldCultivationCycles, phaseIsReadOnly)]
        : unsavedRows,
    [isLoading, fieldCultivationCycles, unsavedRows, phaseIsReadOnly]
  );

  // Row modes state
  const rowEditState = useMemo(() => {
    if (rows.length && eventType && !updateFieldEventsLoading && !bulkCreateOrUpdateLoading) {
      return rows.reduce((acc: Lab.GridRowModesModel, row) => {
        const editStateToSet = getDefaultRowEditState(
          row,
          eventType,
          canEditWhenPhaseUnLockedAndEventLocked
        );
        if (editStateToSet) {
          acc[row.id] = editStateToSet;
        }
        return acc;
      }, {});
    }
    return rowEditStateDefaultValue;
  }, [
    rows,
    eventType,
    updateFieldEventsLoading,
    bulkCreateOrUpdateLoading,
    canEditWhenPhaseUnLockedAndEventLocked,
  ]);

  const [rowModesModel, setRowModesModel] = useState<Lab.GridRowModesModel>(rowEditState);
  useEffect(() => {
    if (rowEditState !== rowEditStateDefaultValue && rows.length && !isLoading) {
      setRowModesModel(prev => ({...prev, ...rowEditState}));
    }
  }, [rows, rowEditState, isLoading]);

  const isInEditMode = useCallback(
    (id: Lab.GridRowId) => {
      if (!rows.length || isEmptyObject(apiRef.current)) return false;
      return rowModesModel[id]?.mode === Lab.GridRowModes.Edit;
    },
    [apiRef, rowModesModel, rows.length]
  );

  const startRowEditMode = useCallback(
    ({id}: {id: Lab.GridRowId}) => {
      if (isInEditMode(id)) return;
      setRowModesModel(prev => ({...prev, [id]: {mode: Lab.GridRowModes.Edit}}));
    },
    [isInEditMode]
  );

  const handleRowEditStop: Lab.GridEventListener<'rowEditStop'> = (params, event) => {
    if (params.reason === Lab.GridRowEditStopReasons.rowFocusOut) {
      event.defaultMuiPrevented = true;
    }
  };

  const stopRowEditMode = useCallback(
    ({id, ignoreModifications = false}: {id: Lab.GridRowId; ignoreModifications?: boolean}) => {
      if (!isInEditMode(id)) return;
      setRowModesModel(prev => ({
        ...prev,
        [id]: {mode: Lab.GridRowModes.View, ignoreModifications},
      }));
    },
    [isInEditMode]
  );

  const handleToggleRowEditMode = useCallback(
    (id: FieldEventRowModel['id'], toggleOn: boolean) => () => {
      if (toggleOn) {
        startRowEditMode({id});
      } else {
        stopRowEditMode({id});
      }
    },
    [startRowEditMode, stopRowEditMode]
  );

  const isCellEditable = useCallback(
    ({row}: {row: FieldEventRowModel}) => {
      const isRowLocked =
        !canEditWhenPhaseUnLockedAndEventLocked && (row?.meta?.isLocked || row?.meta?.isReadOnly);
      if (isRowLocked) {
        return false;
      }

      if (row?.meta?.isUnsavedEvent) {
        return true;
      }

      if (currentStage?.type !== StageTypes.CROP_EVENTS) {
        const isNPO =
          eventType && isNPOSet(row.meta, NO_PRACTICE_OBSERVATION_BY_EVENT_TYPE[eventType]);
        if (isNPO) {
          return false;
        }
      }

      return true;
    },
    [canEditWhenPhaseUnLockedAndEventLocked, eventType, currentStage?.type]
  );

  // Adding/removing rows
  const removeRow = useCallback(
    (id: string, isUnsavedEvent: boolean = true) => {
      if (isUnsavedEvent) {
        const oldRows = unsavedRows.filter(row => row.id !== id);
        setUnsavedRows(oldRows);
      } else {
        apiRef.current.updateRows(rows.filter(row => row.id !== id));
      }
    },
    [apiRef, rows, unsavedRows]
  );

  const handleCancelClick = useCallback(
    (row: FieldEventRowModel) => () => {
      if (isTruthy(row.meta.isUnsavedEvent)) {
        removeRow(row.id, true);
        return;
      }
      stopRowEditMode({id: row.id, ignoreModifications: true});
    },
    [removeRow, stopRowEditMode]
  );

  const handleOnRowAdd = useCallback(() => {
    const id = uniqueId();
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    const newRow = {
      id,
      meta: {type: eventType, isUnsavedEvent: true, eventCount: 0},
    } as FieldEventRowModel<T>;
    setUnsavedRows([newRow, ...unsavedRows]);
    startRowEditMode({id});
  }, [eventType, startRowEditMode, unsavedRows]);

  // CRUD events
  const processRowUpdate = useCallback(
    async (
      newRow: FieldEventRowModel<T>,
      oldRow: FieldEventRowModel<T>
    ): Promise<FieldEventRowModel<T>> => {
      const newEvent = {...oldRow, ...newRow};
      const {meta} = newEvent;

      setRowIsLoading(prevState => [...prevState, newEvent.id]);

      try {
        const {errors, data} = await createOrUpdateEvent(newEvent);
        if (errors) {
          return await Promise.reject(newEvent);
        }

        const shouldResetRow = isNewFieldEvent(newEvent);
        if (shouldResetRow) {
          removeRow(newEvent.id, true);
        }
        setRowIsLoading(prevStage => prevStage.filter(id => id !== newEvent.id));

        return {
          ...newEvent,
          meta: {
            ...meta,
            isUnsavedEvent: false,
            eventCount: meta.eventCount + 1,
          },
          ...data?.createOrUpdateFieldEvent.event_values,
        };
      } catch (e) {
        return Promise.reject(newEvent);
      }
    },
    [createOrUpdateEvent, removeRow]
  );

  const onProcessRowUpdateError = useCallback(
    (row: FieldEventRowModel) => {
      startRowEditMode({id: row.id});
      setRowIsLoading(prevState => prevState.filter(id => id !== row.id));
    },
    [startRowEditMode]
  );

  const handleSaveAll = useCallback(async () => {
    const saveableRows = hasSaveableEdits.filter(row => row.canSave && !row.hasError);
    const loadingIds = saveableRows.map(row => row.id);
    setRowIsLoading(prev => [...prev, ...loadingIds]);

    const updatedRows = getUpdatedRowData<T>(apiRef, saveableRows);
    const unsavedNewEvents = updatedRows.filter(row => row.meta.isUnsavedEvent);

    try {
      const result = await bulkCreateOrUpdateEvents(updatedRows);

      if (result.errors) {
        return await Promise.reject(result.errors);
      }

      if (result.data?.bulkCreateOrUpdateFieldEvents) {
        const updatedEventIds = new Set(
          result.data.bulkCreateOrUpdateFieldEvents.events.map(e => e.id)
        );

        const rowsToUpdate = updatedRows.filter(row => updatedEventIds.has(row.id));

        if (rowsToUpdate.length) {
          apiRef.current.updateRows(rowsToUpdate);
        }
        if (unsavedNewEvents.length) {
          const unsavedEventIds = new Set(unsavedNewEvents.map(event => event.id));
          const newUnsavedRows = unsavedRows.filter(row => !unsavedEventIds.has(row.id));
          setUnsavedRows(newUnsavedRows);
        }
      }
      const updatedRowModes = createViewModeRowModel(saveableRows);
      setRowModesModel(prevModes => ({
        ...prevModes,
        ...updatedRowModes,
      }));
    } catch {
      // Error notification handled in mutation
    } finally {
      setRowIsLoading([]);
    }
  }, [hasSaveableEdits, apiRef, bulkCreateOrUpdateEvents, unsavedRows]);

  const handleDeleteClick = useCallback(
    (row: FieldEventRowModel) => async (): Promise<boolean> => {
      if (!selectedField) return Promise.reject(false);
      if (isNewFieldEvent(row)) {
        removeRow(row.id, true);
        return true;
      }
      try {
        const {data, errors} = await deleteFieldEvent(selectedField?.id, row);
        if (!errors && data?.deleteFieldEvent) {
          removeRow(row.id, false);
          return true;
        }
      } catch {
        return Promise.reject(false);
      }

      return Promise.reject(false);
    },
    [deleteFieldEvent, removeRow, selectedField]
  );

  const handleStateChange = useCallback(
    ({editRows}: Lab.GridState) => {
      // Only process if there are edit rows
      if (resetEditsIfEmpty(editRows, hasSaveableEdits, setHasSaveableEdits)) {
        return;
      }

      const hasUnsavedChanges: Array<EditableRow> = Object.entries(editRows).map(([id, values]) => {
        const rowVals = apiRef.current.getRowWithUpdatedValues(id, '');
        const rowHasErrors = rowHasValidationErrors(values);

        const canSaveRow = canSaveFieldEvent(eventType, rowVals);

        return {
          id,
          hasError: rowHasErrors,
          canSave: canSaveRow,
        };
      });

      const hasChanged = haveEditableRowsChanged(hasSaveableEdits, hasUnsavedChanges);

      if (hasChanged) {
        setHasSaveableEdits(hasUnsavedChanges);
      }
    },
    [apiRef, eventType, hasSaveableEdits]
  );

  // Row selection
  const handleRowSelectionModelChange = useCallback(
    (rowSelectionModel: Array<Lab.GridRowId>) => {
      if (
        (!selectedFieldEvents?.field?.id && !rowSelectionModel?.length) ||
        !eventType ||
        (selectedFieldEvents.field?.id && selectedField?.id !== selectedFieldEvents.field?.id)
      ) {
        return;
      }
      setSelectedFieldEvents(rowSelectionModel, eventType);
    },
    [eventType, selectedField?.id, selectedFieldEvents?.field?.id, setSelectedFieldEvents]
  );

  const rowSelectionModel = useMemo((): Lab.GridInputRowSelectionModel => {
    if (!eventType) return [];

    return selectedFieldEvents?.events?.[eventType] ?? [];
  }, [eventType, selectedFieldEvents]);

  const shouldDisableRowSelection = useMemo(
    () => !!(selectedFieldEvents?.field?.id && selectedField?.id !== selectedFieldEvents.field?.id),
    [selectedField?.id, selectedFieldEvents?.field?.id]
  );

  const isRowSelectable = useCallback(
    ({row}: Lab.GridRowParams<FieldEventRowModel<T>>) => {
      return row.meta.eventCount > 0 && !shouldDisableRowSelection;
    },
    [shouldDisableRowSelection]
  );

  // Styling
  const getRowClassName = useCallback(
    (row: Lab.GridRowClassNameParams<FieldEventRowModel<T>>) => {
      if (rowIsLoading.includes(row.id)) {
        return 'row-loading';
      }
      return '';
    },
    [rowIsLoading]
  );

  const slots = useMemo(
    () => ({
      toolbar: () => (
        <CustomToolbarHeader
          handleOnRowAdd={handleOnRowAdd}
          disableOnRowAdd={!canEditWhenPhaseUnLockedAndEventLocked && phaseIsReadOnly}
          handleSaveAll={handleSaveAll}
          disableSaveAll={
            bulkCreateOrUpdateLoading ||
            hasSaveableEdits.filter(e => e.canSave && !e.hasError).length === 0
          }
          bulkSaving={bulkCreateOrUpdateLoading}
        />
      ),
      loadingOverlay: () => (
        <Skeleton
          height="100%"
          aria-label="field events table loading"
          aria-busy={true}
          aria-live="polite"
          sx={theme => ({
            WebkitTransform: 'none',
            ...(shouldDisplayLoadingForMonitorPrefill && {
              opacity: 0.65,
              backgroundColor: theme.palette.background.paper,
            }),
          })}
        />
      ),
    }),
    [
      handleOnRowAdd,
      canEditWhenPhaseUnLockedAndEventLocked,
      phaseIsReadOnly,
      handleSaveAll,
      bulkCreateOrUpdateLoading,
      hasSaveableEdits,
      shouldDisplayLoadingForMonitorPrefill,
    ]
  );

  const selectedFieldEventsOnly = eventType && selectedFieldEvents?.events?.[eventType];

  const cols = useMemo(
    () =>
      getDefaultFieldEventsGridColumns<T>(
        apiRef,
        columns,
        eventType,
        currentStage?.type,
        handleToggleRowEditMode,
        handleCancelClick,
        handleDeleteClick,
        updateNoPracticeObservation,
        isInEditMode,
        formatMessage,
        rowIsLoading,
        canEditWhenPhaseUnLockedAndEventLocked,
        shouldDisableRowSelection,
        selectedFieldEventsOnly || []
      ),
    [
      apiRef,
      columns,
      currentStage,
      eventType,
      formatMessage,
      handleCancelClick,
      handleDeleteClick,
      handleToggleRowEditMode,
      isInEditMode,
      updateNoPracticeObservation,
      rowIsLoading,
      canEditWhenPhaseUnLockedAndEventLocked,
      shouldDisableRowSelection,
      selectedFieldEventsOnly,
    ]
  );

  return (
    <Box width="100%">
      <UnsavedChangesDialog
        formatMesssage={formatMessage}
        hasSaveableEdits={hasSaveableEdits}
        handleSaveAll={handleSaveAll}
      />
      <Lab.DataGrid
        apiRef={apiRef}
        aria-label={`Field ${selectedField?.id} ${formattedStageType} table`}
        disableChildrenSorting
        disableColumnReorder
        disableColumnFilter
        disableVirtualization
        autoHeight
        rows={rows}
        columns={cols}
        // row edit
        editMode="row"
        rowModesModel={rowModesModel}
        isCellEditable={isCellEditable}
        onRowModesModelChange={setRowModesModel}
        onRowEditStop={handleRowEditStop}
        processRowUpdate={processRowUpdate}
        onProcessRowUpdateError={onProcessRowUpdateError}
        // row selection
        checkboxSelection={canCopyFieldEvents}
        rowSelectionModel={rowSelectionModel}
        disableMultipleRowSelection={shouldDisableRowSelection}
        isRowSelectable={isRowSelectable}
        onRowSelectionModelChange={handleRowSelectionModelChange}
        slots={slots}
        loading={shouldDisplayLoadingForMonitorPrefill || (isLoading && !rows.length)}
        rowsLoadingMode="server"
        initialState={intialDataGridState}
        onStateChange={handleStateChange}
        columnVisibilityModel={
          isEPhaseAndSinglePhaseDataCollectionOff ? hiddenAttributesForEPhase : undefined
        }
        getRowClassName={getRowClassName}
        sx={fieldEventsDataGridStyleOverrides}
        disableRowSelectionOnClick
      />
      <SelectedEventsSnackbar />
    </Box>
  );
};
