import {push} from 'connected-react-router';
import React, {useMemo, useState} from 'react';
import {useIntl} from 'react-intl';
import {CircularProgress, FontIcon} from 'react-md';

import {useAppDispatch, useAppSelector} from 'store/useRedux';

import {Flex} from '_common/components/flex';
import {FluroButton} from '_common/components/fluro-button/fluro-button';
import {FluroChip} from '_common/components/fluro-chip/fluro-chip';
import {GearIcon} from '_common/components/fluro-icons';
import {
  FluroDataTable,
  FluroTableBody,
  FluroTableColumn,
  FluroTableHeader,
  FluroTableRow,
} from '_common/components/fluro-table-components';
import {showNotification} from '_common/components/NotificationSnackbar';
import {SearchInput} from '_common/components/search-input';
import {Text} from '_common/components/text/text';
import {useStateWithDebouncedValue} from '_common/hooks/use-debounced-value';
import {applyPathParams} from '_common/utils/routing-helpers';
import {naturalSortAlphaNum} from '_common/utils/sorters';
import {isDefined} from '_common/utils/typeGuards';

import {selectMeasurement} from 'containers/login/login-selectors';
import SIApi from 'containers/si/api/si';
import {useFetchGoldenProgramId} from 'containers/si/api/swr/hooks/useFetchGoldenProgramId';
import {useFetchProgramsList} from 'containers/si/api/swr/hooks/useFetchProgramsList';
import {SIInputPopup} from 'containers/si/components/Legacy/si-input-popup';
import {
  EMPTY_DATA_CELL_VALUE,
  globalKPILabel,
  outcomeKPILabel,
  practiceKPILabel,
} from 'containers/si/constants';
import {useIsAdmin} from 'containers/si/hooks/useIsAdmin';
import {defaultSIProgram} from 'containers/si/module/base';
import {ActionType} from 'containers/si/module/types';
import {makeProgramMetrics} from 'containers/si/programs/helpers/programs.transformation';
import {SI_CONFIGURE, SI_REPORT} from 'containers/si/routing/routes';
import {searchCollectionByKeys} from 'containers/si/utils/search';

export const ProgramList = () => {
  const {isRegrowAdmin} = useIsAdmin();
  const intl = useIntl();
  const {goldenProgramId} = useFetchGoldenProgramId();
  const {
    data: programs,
    isLoading: isLoadingPrograms,
    mutate: mutatePrograms,
  } = useFetchProgramsList({revalidateOnMount: true});

  const nonGoldenPrograms = useMemo(
    () => programs?.filter(({id}) => id !== goldenProgramId) ?? [],
    [goldenProgramId, programs]
  );

  const dispatch = useAppDispatch();
  const userUnitsSystem = useAppSelector(selectMeasurement);
  const [createProgramPopupVisible, setCreateProgramPopupVisible] = useState(false);
  const [searchText, setSearchText, debouncedSearchText] = useStateWithDebouncedValue('', 300);

  const createProgram = async (name: string | number) => {
    const program = {...defaultSIProgram, name: String(name)};
    setCreateProgramPopupVisible(false);
    await SIApi.addProgram(program);
    void mutatePrograms();
    showNotification({
      title: intl.formatMessage({id: 'note.success', defaultMessage: 'Success'}),
      message: `Program "${name}" successfully created`,
      type: 'success',
    });
  };

  const deleteProgram = async (programId: number, programName: string) => {
    const confirm = window.confirm(`Are you sure you want to delete the "${programName}" program?`);

    if (confirm) {
      await SIApi.removeProgram(programId);
      void mutatePrograms();
      showNotification({
        title: intl.formatMessage({id: 'note.success', defaultMessage: 'Success'}),
        message: `The "${programName}" program was deleted.`,
        type: 'success',
      });
    }
  };

  const sortedPrograms = useMemo(
    () => naturalSortAlphaNum(nonGoldenPrograms, 'name'),
    [nonGoldenPrograms]
  );
  const filteredPrograms = useMemo(
    () => searchCollectionByKeys(sortedPrograms, ['name', 'id'])(debouncedSearchText),
    [debouncedSearchText, sortedPrograms]
  );

  const programMetricsTransformer = useMemo(
    () => makeProgramMetrics(userUnitsSystem),
    [userUnitsSystem]
  );

  const selectProgramAndNavigate = (
    pathToApply: string,
    programId: number,
    e?: React.MouseEvent<HTMLElement>
  ) => {
    e?.stopPropagation?.();
    dispatch({type: ActionType.CLEAR_PROGRAM_STATE});
    dispatch(push(applyPathParams(pathToApply, {programId})));
  };

  return (
    <>
      {isLoadingPrograms ? (
        <CircularProgress className={'progress'} id={'si-programs-table'} />
      ) : (
        <>
          <Flex justifyContent="space-between" className="mb-1" nowrap>
            <SearchInput
              loading={isLoadingPrograms}
              value={searchText}
              onChange={setSearchText}
              placeholder="Search programs"
            />
            {isRegrowAdmin && (
              <FluroButton
                primary
                raised
                onClick={() => setCreateProgramPopupVisible(true)}
                data-testid="program-admin-program-create--SI"
              >
                Create a program
              </FluroButton>
            )}
          </Flex>
          {filteredPrograms.length > 0 && (
            <FluroDataTable>
              <FluroTableHeader>
                <FluroTableRow>
                  <FluroTableColumn>Program</FluroTableColumn>
                  <FluroTableColumn>Users</FluroTableColumn>
                  <FluroTableColumn>Years</FluroTableColumn>
                  <FluroTableColumn>Monitored Area / Billed Area</FluroTableColumn>
                  <FluroTableColumn>Data Products</FluroTableColumn>
                  <FluroTableColumn>{/* Action buttons */}</FluroTableColumn>
                </FluroTableRow>
              </FluroTableHeader>
              <FluroTableBody>
                {filteredPrograms.map(p => {
                  const programMetrics = programMetricsTransformer(p);

                  return (
                    <FluroTableRow
                      // todo: replace with design system table to get rows with roles and a11y props
                      data-testid={`program-list-row--${p.id}`}
                      key={p.id}
                      onClick={e => selectProgramAndNavigate(SI_REPORT, p.id, e)}
                      className="cursor-pointer"
                    >
                      <FluroTableColumn>
                        <Flex alignItems="center">
                          {p.name}&nbsp;
                          <Text className="ml-05" secondary variant="small-thin">
                            #{p.id}
                          </Text>
                        </Flex>
                      </FluroTableColumn>
                      <FluroTableColumn>{p.total_user_count ?? 0}</FluroTableColumn>
                      <FluroTableColumn>
                        {`${p.crop_year_start} - ${p.crop_year_end}`}
                      </FluroTableColumn>
                      <FluroTableColumn>
                        <Flex direction="row" alignItems="center">
                          {isDefined(programMetrics)
                            ? `${programMetrics.totalFieldArea.formattedValue} ${programMetrics.totalFieldArea.unit} / ${programMetrics.billedFieldArea.formattedValue} ${programMetrics.billedFieldArea.unit}`
                            : EMPTY_DATA_CELL_VALUE}
                        </Flex>
                      </FluroTableColumn>
                      <FluroTableColumn>
                        <Flex alignItems="center" nowrap>
                          <Flex gap="4px" className="ml-05">
                            {p.outcome_kpis.map(asset => {
                              const label = outcomeKPILabel[asset];

                              return isDefined(label) ? (
                                <FluroChip tone="light" size="small" label={label} key={asset} />
                              ) : null;
                            })}
                            {p.global_kpi_data_product_access.map(asset => {
                              const label = globalKPILabel[asset];

                              return isDefined(label) ? (
                                <FluroChip tone="light" size="small" label={label} key={asset} />
                              ) : null;
                            })}
                            {p.practice_kpis.map(asset => (
                              <FluroChip
                                tone="light"
                                size="small"
                                label={practiceKPILabel[asset]}
                                key={asset}
                              />
                            ))}
                          </Flex>
                        </Flex>
                      </FluroTableColumn>
                      <FluroTableColumn>
                        <Flex gap="10px" nowrap alignItems="center">
                          <FluroButton
                            primary
                            raised
                            onClick={e => {
                              selectProgramAndNavigate(SI_REPORT, p.id, e);
                            }}
                          >
                            View Program
                          </FluroButton>

                          <FluroButton
                            icon
                            iconEl={<GearIcon />}
                            tooltipPosition={'left'}
                            tooltipLabel={'Program configuration'}
                            onClick={e => {
                              selectProgramAndNavigate(SI_CONFIGURE, p.id, e);
                            }}
                          />
                          {isRegrowAdmin && (
                            <FluroButton
                              // todo: replace with design system table to get icon buttons with a11y props
                              data-testid="program-list-delete-program-button--SI"
                              icon
                              iconEl={<FontIcon>delete</FontIcon>}
                              onClick={async e => {
                                e.stopPropagation();
                                await deleteProgram(p.id, p.name);
                              }}
                              tooltipPosition={'left'}
                              tooltipLabel={'Delete program'}
                            />
                          )}
                        </Flex>
                      </FluroTableColumn>
                    </FluroTableRow>
                  );
                })}
              </FluroTableBody>
            </FluroDataTable>
          )}
          {filteredPrograms.length === 0
            ? nonGoldenPrograms.length !== 0 && debouncedSearchText
              ? `No program found for search = ${debouncedSearchText}`
              : 'The list programs is empty you can create a program.'
            : null}
          {createProgramPopupVisible && (
            <SIInputPopup
              title="Create a new program"
              subtitle="Program Name"
              type="text"
              value=""
              onHide={() => setCreateProgramPopupVisible(false)}
              onSave={createProgram}
            />
          )}
        </>
      )}
    </>
  );
};
