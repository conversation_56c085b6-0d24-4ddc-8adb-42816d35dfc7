import {useMemo} from 'react';

import {useAppSelector} from 'store/useRedux';

import {isNil} from '_common/utils/typeGuards';

import {selectMeasurement} from 'containers/login/login-selectors';
import {useGetCropLabelById} from 'containers/si/hooks/useGetCropLabelById';
import {
  COMMON_CROP_ROTATION_METRICS_FORMATTER_MAP,
  COMMON_CROP_ROTATION_METRICS_UNIT_MAP,
  type CommonCropRotationMetric,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.crop_rotation_common_rotations.transformation';
import type {SummaryTypeChartProps} from 'containers/si/programs/report/kpi/types';
import {isValidCommonKpiFiltersState} from 'containers/si/typeGuards';
import type {KPIDataFiltersState} from 'containers/si/types';

export const useCommonCropRotationBarChartContent = ({
  filtersState,
  isLoading,
  rotationsSummary,
}: {
  filtersState: KPIDataFiltersState;
  isLoading: boolean;
  rotationsSummary: Array<[string, CommonCropRotationMetric | null]> | null;
}) => {
  const hasValidFilters = isValidCommonKpiFiltersState(filtersState);

  const {getSISupportedCropRotationLabelById} = useGetCropLabelById();

  const userUnitsSystem = useAppSelector(selectMeasurement);

  const unitDetail = useMemo(
    () => COMMON_CROP_ROTATION_METRICS_UNIT_MAP.rotationArea(userUnitsSystem),
    [userUnitsSystem]
  );

  const commonCropRotationBarChartContent: SummaryTypeChartProps | null = useMemo(() => {
    if (!hasValidFilters || isLoading || isNil(rotationsSummary)) return null;

    const labels = rotationsSummary.map(([cropRotationCsv, _commonCropRotationMetrics]) =>
      getSISupportedCropRotationLabelById(cropRotationCsv)
    );

    const data = rotationsSummary.map(
      ([_cropRotationCsv, commonCropRotationMetrics]) =>
        commonCropRotationMetrics?.rotationArea.value ?? null
    );

    return {
      labels,
      data,
      unitDetail,
      formatter: COMMON_CROP_ROTATION_METRICS_FORMATTER_MAP.rotationArea,
    };
  }, [
    getSISupportedCropRotationLabelById,
    hasValidFilters,
    isLoading,
    rotationsSummary,
    unitDetail,
  ]);

  return commonCropRotationBarChartContent;
};
