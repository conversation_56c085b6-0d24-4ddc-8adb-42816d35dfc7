import omit from 'lodash/omit';
import {useMemo} from 'react';

import {useAppSelector} from 'store/useRedux';

import {selectMeasurement} from 'containers/login/login-selectors';
import {useFetchAndTransformCommonRotations} from 'containers/si/hooks/useFetchAndTransformCommonRotations';
import {useFetchAndTransformKPI} from 'containers/si/hooks/useFetchAndTransformKPI';
import {
  CROP_ROTATION_FALLOW_METRICS_FORMATTER_MAP,
  CROP_ROTATION_FALLOW_METRICS_LABEL_MAP,
  CROP_ROTATION_FALLOW_METRICS_UNIT_MAP,
  makeCropRotationFallowMetric,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.crop_rotation_fallow.transformation';
import {
  CROP_ROTATION_NUMBER_METRICS_FORMATTER_MAP,
  CROP_ROTATION_NUMBER_METRICS_LABEL_MAP,
  CROP_ROTATION_NUMBER_METRICS_UNIT_MAP,
  makeCropRotationNumberMetric,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.crop_rotation_number.transformation';
import {useCommonCropRotationBarChartContent} from 'containers/si/programs/report/kpi/components/CropRotationCard/useCommonCropRotationBarChartContent';
import {useBySubtypeDoughnutChartContent} from 'containers/si/programs/report/kpi/hooks/useBySubtypeDoughnutChartContent';
import {useGetKPIDashboardCommonFilters} from 'containers/si/programs/report/kpi/hooks/useGetKPIDashboardCommonFilters';
import type {
  SubTypeDoughnutChartWithTotalProps,
  SummaryTypeChartProps,
} from 'containers/si/programs/report/kpi/types';

const CROP_ROTATION_FALLOW_BY_SUBTYPE_LABEL_MAP = omit(
  CROP_ROTATION_FALLOW_METRICS_LABEL_MAP,
  'totalFallowDataArea'
);

const CROP_ROTATION_NUMBER_BY_SUBTYPE_LABEL_MAP = omit(
  CROP_ROTATION_NUMBER_METRICS_LABEL_MAP,
  'totalCropRotationDataArea'
);

export type UseCropRotationCardData = {
  isLoading: boolean;
  data: {
    commonCropRotationBarChartContent: SummaryTypeChartProps | null;
    cropRotationFallowAreaDoughnutChartContent: SubTypeDoughnutChartWithTotalProps | null;
    cropRotationByCropCountDoughnutChartContent: SubTypeDoughnutChartWithTotalProps | null;
  };
};

export const useCropRotationCardData = (): UseCropRotationCardData => {
  const {commonFilters, filtersState} = useGetKPIDashboardCommonFilters();

  const userUnitsSystem = useAppSelector(selectMeasurement);
  const cropRotationNumberTransformer = useMemo(
    () => makeCropRotationNumberMetric(userUnitsSystem),
    [userUnitsSystem]
  );

  const {isLoading: fetchCropRotationsNumberIsLoading, topLevel: cropRotationNumberTopLevel} =
    useFetchAndTransformKPI({
      kpi: 'crop_rotation_number',
      summaries: [],
      commonFilters,
      kpiTransformer: cropRotationNumberTransformer,
    });

  const cropRotationFallowTransformer = useMemo(
    () => makeCropRotationFallowMetric(userUnitsSystem),
    [userUnitsSystem]
  );

  const {isLoading: fetchCropRotationsFallowIsLoading, topLevel: cropRotationFallowTopLevel} =
    useFetchAndTransformKPI({
      kpi: 'crop_rotation_fallow',
      summaries: [],
      commonFilters,
      kpiTransformer: cropRotationFallowTransformer,
    });

  const {isLoading: fetchCommonRotationsIsLoading, rotationsSummary} =
    useFetchAndTransformCommonRotations({
      commonFilters,
      topN: 20,
    });

  const commonCropRotationBarChartContent = useCommonCropRotationBarChartContent({
    filtersState,
    isLoading: fetchCommonRotationsIsLoading,
    rotationsSummary,
  });

  const cropRotationFallowUnitDetail = useMemo(
    () => CROP_ROTATION_FALLOW_METRICS_UNIT_MAP.noFallowSeasonsArea(userUnitsSystem),
    [userUnitsSystem]
  );

  const cropRotationFallowAreaDoughnutChartContent = useBySubtypeDoughnutChartContent({
    kpiMetrics: cropRotationFallowTopLevel,
    isLoading: fetchCropRotationsFallowIsLoading,
    labelMap: CROP_ROTATION_FALLOW_BY_SUBTYPE_LABEL_MAP,
    formatter: CROP_ROTATION_FALLOW_METRICS_FORMATTER_MAP.noFallowSeasonsArea,
    unitDetail: cropRotationFallowUnitDetail,
  });

  const cropRotationByCropCountUnitDetail = useMemo(
    () => CROP_ROTATION_NUMBER_METRICS_UNIT_MAP.oneCropRotationArea(userUnitsSystem),
    [userUnitsSystem]
  );

  const cropRotationByCropCountDoughnutChartContent = useBySubtypeDoughnutChartContent({
    kpiMetrics: cropRotationNumberTopLevel,
    isLoading: fetchCropRotationsNumberIsLoading,
    labelMap: CROP_ROTATION_NUMBER_BY_SUBTYPE_LABEL_MAP,
    formatter: CROP_ROTATION_NUMBER_METRICS_FORMATTER_MAP.oneCropRotationArea,
    unitDetail: cropRotationByCropCountUnitDetail,
  });

  const data = {
    commonCropRotationBarChartContent,
    cropRotationFallowAreaDoughnutChartContent,
    cropRotationByCropCountDoughnutChartContent,
  };

  const isLoading =
    fetchCommonRotationsIsLoading ||
    fetchCropRotationsNumberIsLoading ||
    fetchCropRotationsFallowIsLoading;

  return {isLoading, data};
};
