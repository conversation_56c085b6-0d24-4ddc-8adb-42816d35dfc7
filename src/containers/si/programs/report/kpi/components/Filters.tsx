import React from 'react';

import {Box, Button, SvgIcon} from '@regrow-internal/design-system';

import {ProgramCropsSelect} from 'containers/si/components/filters/CropsSelect/ProgramCropsSelect';
import {DataScenarioSelect} from 'containers/si/components/filters/DataScenarioSelect';
import {RegionsSelect} from 'containers/si/components/filters/RegionsSelect';
import {YearsSelect} from 'containers/si/components/filters/YearsSelect';
import {SaveKPIFilter} from 'containers/si/components/Legacy/SaveKpiFilter/save-kpi-filter';
import {useTabAccess} from 'containers/si/hooks/useTabAccess';
import {useFilters} from 'containers/si/programs/report/kpi/hooks/useFilters';
import {isValidCommonKpiFiltersState} from 'containers/si/typeGuards';

export const Filters = () => {
  const {getHasTabAccess} = useTabAccess();
  const {
    isDirty,
    dirtyFiltersState,
    onApplyFiltersClick,
    onCancelClick,
    onFilterChange,
    onApplySavedView,
  } = useFilters();

  const isSubmitDisabled = !isDirty || !isValidCommonKpiFiltersState(dirtyFiltersState);

  return (
    <Box
      bgcolor="semanticPalette.surface.secondary"
      borderBottom={theme => `1px solid ${theme.palette.semanticPalette.stroke.main}`}
      p={theme => theme.spacing(2, 5)}
    >
      <Box
        data-testid="kpi-dashboard-filters"
        justifyContent="space-between"
        display="flex"
        gap={3}
        flexWrap={'wrap'}
      >
        <Box alignItems="center" display="flex" flexGrow={1} gap={3} flexWrap={'wrap'}>
          <SvgIcon color="inherit" fontSize="medium" type="filter" />

          {getHasTabAccess('data_scenarios') ? (
            <DataScenarioSelect
              dataScenarioId={dirtyFiltersState.dataScenarioId}
              onDataScenarioChange={dataScenarioId => onFilterChange({dataScenarioId})}
            />
          ) : null}
          <Box width={theme => theme.fixedWidths.xs}>
            <RegionsSelect
              regionIds={dirtyFiltersState.subsectionIds}
              dataScenarioId={dirtyFiltersState.dataScenarioId}
              onRegionsChange={subsectionIds => onFilterChange({subsectionIds})}
            />
          </Box>
          <ProgramCropsSelect
            cropIds={dirtyFiltersState.cropIds}
            regionIds={dirtyFiltersState.subsectionIds}
            dataScenarioId={dirtyFiltersState.dataScenarioId}
            onCropsChange={cropIds => onFilterChange({cropIds})}
          />
          <YearsSelect
            year={dirtyFiltersState.year}
            onYearChange={year => onFilterChange({year})}
          />
        </Box>
        <Box alignItems="flex-end" display="flex" gap={3}>
          {/* TODO: update saved view filter to use DS component */}
          <Box
            sx={theme => ({
              '& #saved-view-dropdown': {
                borderRadius: theme.borderRadii.sm,
                paddingBottom: '6px',
                paddingTop: '6px',
              },
            })}
          >
            <SaveKPIFilter onApplySavedView={onApplySavedView} />
          </Box>
          {isDirty && (
            <Button color="secondary" variant="outlined" onClick={onCancelClick}>
              Cancel
            </Button>
          )}
          <Button disabled={isSubmitDisabled} onClick={onApplyFiltersClick}>
            Apply Filters
          </Button>
        </Box>
      </Box>
    </Box>
  );
};
