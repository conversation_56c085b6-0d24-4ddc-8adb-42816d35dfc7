import {useFeatureFlagEnabled} from 'posthog-js/react';
import {useMemo} from 'react';

import {useAppSelector} from 'store/useRedux';

import {selectMeasurement} from 'containers/login/login-selectors';
import {useFetchAndTransformKPI} from 'containers/si/hooks/useFetchAndTransformKPI';
import {
  makeCoverCropExpectedMetrics,
  makeCoverCropExpectedWithNaExclusiveMetrics,
  makeCoverCropMetrics,
  type CoverCropMetrics,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.cover_crop.transformation';
import {
  CATEGORY_COLOR_KEY,
  FORMATTER_MAP,
  KPI,
  UNIT_MAP,
} from 'containers/si/programs/report/kpi/components/CoverCropCard/content';
import {type PrimaryMetricTileProps} from 'containers/si/programs/report/kpi/components/PrimaryMetricTile';
import {type TimeTrendTileProps} from 'containers/si/programs/report/kpi/components/TimeTrendTile';
import {useCropSummaryTypeBarChartContent} from 'containers/si/programs/report/kpi/hooks/useCropSummaryTypeBarChartContent';
import {useGetKPIDashboardCommonFilters} from 'containers/si/programs/report/kpi/hooks/useGetKPIDashboardCommonFilters';
import {usePrimaryMetricTileAndTimeTrendTileContent} from 'containers/si/programs/report/kpi/hooks/usePrimaryMetricTileAndTimeTrendTileContent';
import {useSubregionSummaryTypeBarChartContent} from 'containers/si/programs/report/kpi/hooks/useSubregionSummaryTypeBarChartContent';
import type {MetricLookupMap, SummaryTypeChartProps} from 'containers/si/programs/report/kpi/types';

export type UseCoverCropCardData = {
  isLoading: boolean;
  data: {
    primaryMetricTileContent: PrimaryMetricTileProps | null;
    timeTrendTileContent: TimeTrendTileProps | null;
    cropSummaryTypeBarChartContent: SummaryTypeChartProps | null;
    subregionSummaryTypeBarChartContent: SummaryTypeChartProps | null;
  };
};

const METRIC_MAP_WITH_ALT_METRICS: Pick<
  MetricLookupMap<CoverCropMetrics>,
  | 'primaryMetricKey'
  | 'uncertaintyMetricKey'
  | 'primaryAltMetricKey'
  | 'uncertaintyAltMetricKey'
  | 'secondaryMetricKey'
  | 'secondaryUncertaintyMetricKey'
  | 'trackedMetricKey'
  | 'unknownMetricKey'
  | 'notApplicableMetricKey'
> = {
  primaryMetricKey: 'adoption',
  uncertaintyMetricKey: 'adoptionStdDev',
  primaryAltMetricKey: 'adoptionNotApplicableExclusive',
  uncertaintyAltMetricKey: 'adoptionStdDevNotApplicableExclusive',
  secondaryMetricKey: 'covercroppedArea',
  secondaryUncertaintyMetricKey: 'covercroppedAreaStdDev',
  trackedMetricKey: 'totalTrackedArea',
  unknownMetricKey: 'unknownArea',
  notApplicableMetricKey: 'notApplicableArea',
};

const METRIC_MAP_WITHOUT_ALT_METRICS: Pick<
  MetricLookupMap<CoverCropMetrics>,
  | 'primaryMetricKey'
  | 'uncertaintyMetricKey'
  | 'secondaryMetricKey'
  | 'secondaryUncertaintyMetricKey'
  | 'trackedMetricKey'
  | 'unknownMetricKey'
  | 'notApplicableMetricKey'
> = {
  primaryMetricKey: 'adoption',
  uncertaintyMetricKey: 'adoptionStdDev',
  secondaryMetricKey: 'covercroppedArea',
  secondaryUncertaintyMetricKey: 'covercroppedAreaStdDev',
  trackedMetricKey: 'totalTrackedArea',
  unknownMetricKey: 'unknownArea',
  notApplicableMetricKey: 'notApplicableArea',
};

export const useCoverCropCardData = (): UseCoverCropCardData => {
  const {filtersState, commonFilters} = useGetKPIDashboardCommonFilters();

  const userUnitsSystem = useAppSelector(selectMeasurement);

  // TODO: SI-3446 remove feature flag and hard code METRIC MAP and kpiTransformer as constant in content.ts
  const isCoverCropNaInclusiveEnabled = useFeatureFlagEnabled('si-covercrop-na-inclusive');
  // TODO: SI-3064 remove feature flag and hard code kpiTransformer as constant in content.ts
  const isExpectedValuesEnabled = useFeatureFlagEnabled('si-expected-values');
  const isMosaicEnabled = useFeatureFlagEnabled('si-mosaic-be');

  // TODO: SI-3446 remove feature flag and hard code METRIC MAP as constant in content.ts
  const METRIC_MAP = useMemo(
    () =>
      isCoverCropNaInclusiveEnabled ? METRIC_MAP_WITH_ALT_METRICS : METRIC_MAP_WITHOUT_ALT_METRICS,
    [isCoverCropNaInclusiveEnabled]
  );

  // TODO: SI-3446 remove feature flag and hard code kpiTransformer as constant
  const kpiTransformer = useMemo(() => {
    if (isExpectedValuesEnabled && isMosaicEnabled) {
      if (isCoverCropNaInclusiveEnabled) {
        return makeCoverCropExpectedWithNaExclusiveMetrics(userUnitsSystem);
      } else {
        return makeCoverCropExpectedMetrics(userUnitsSystem);
      }
    } else {
      return makeCoverCropMetrics(userUnitsSystem);
    }
  }, [isCoverCropNaInclusiveEnabled, isExpectedValuesEnabled, isMosaicEnabled, userUnitsSystem]);

  const {
    isLoading,
    annualizedSummary: byYearMetricsPairs,
    boundarySummary: bySubregionMetricsPairs,
    cropTypeSummary: byCropMetricsPairs,
  } = useFetchAndTransformKPI({
    kpi: KPI,
    summaries: ['annualized', 'boundary', 'crop_type'],
    commonFilters,
    kpiTransformer,
  });

  const {primaryMetricTileContent, timeTrendTileContent} =
    usePrimaryMetricTileAndTimeTrendTileContent({
      byYearMetricsPairs,
      categoryColorKey: CATEGORY_COLOR_KEY,
      filtersState,
      isLoading,
      formatterMap: FORMATTER_MAP,
      metricMap: METRIC_MAP,
      unitMap: UNIT_MAP,
    });

  const cropSummaryTypeBarChartContent: SummaryTypeChartProps | null =
    useCropSummaryTypeBarChartContent({
      byCropMetricsPairs,
      categoryColorKey: CATEGORY_COLOR_KEY,
      filtersState,
      isLoading,
      formatterMap: FORMATTER_MAP,
      metricMap: METRIC_MAP,
      unitMap: UNIT_MAP,
    });

  const subregionSummaryTypeBarChartContent: SummaryTypeChartProps | null =
    useSubregionSummaryTypeBarChartContent({
      bySubregionMetricsPairs,
      categoryColorKey: CATEGORY_COLOR_KEY,
      isLoading,
      filtersState,
      formatterMap: FORMATTER_MAP,
      metricMap: METRIC_MAP,
      unitMap: UNIT_MAP,
    });

  const data = {
    subregionSummaryTypeBarChartContent,
    cropSummaryTypeBarChartContent,
    primaryMetricTileContent,
    timeTrendTileContent,
  };

  return {isLoading, data};
};
