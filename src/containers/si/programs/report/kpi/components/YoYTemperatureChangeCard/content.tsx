import {
  makeTemperatureMetrics,
  TEMPERATURE_METRICS_FORMATTER_MAP,
  TEMPERATURE_METRICS_UNIT_MAP,
  YOY_TEMP_CHANGE_CONTENT,
  type TemperatureMetrics,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.temperature.transformation';
import {PANEL_LABEL} from 'containers/si/programs/report/kpi/constants';
import type {MetricLookupMap} from 'containers/si/programs/report/kpi/types';

export const CARD_CONTENT = {
  title: YOY_TEMP_CHANGE_CONTENT.name,
  isDownwardChangePositive: true,
};

export const PANEL_CONTENT = {
  ByYear: {
    title: `${CARD_CONTENT.title} ${PANEL_LABEL.OVER_TIME}`,
    label: PANEL_LABEL.OVER_TIME,
    tooltipContent: YOY_TEMP_CHANGE_CONTENT.explainer(PANEL_LABEL.OVER_TIME),
  },
  ByBoundary: {
    title: `${CARD_CONTENT.title} ${PANEL_LABEL.BY_AREA}`,
    label: PANEL_LABEL.BY_AREA,
    tooltipContent: YOY_TEMP_CHANGE_CONTENT.explainer(PANEL_LABEL.BY_AREA),
    sortDirection: 'DESC',
  },
  ByCrop: {
    title: `${CARD_CONTENT.title} ${PANEL_LABEL.BY_CROP_TYPE}`,
    label: PANEL_LABEL.BY_CROP_TYPE,
    tooltipContent: YOY_TEMP_CHANGE_CONTENT.explainer(PANEL_LABEL.BY_CROP_TYPE),
    sortDirection: 'DESC',
  },
} as const;

export const KPI = 'temperature';
export const FORMATTER_MAP = TEMPERATURE_METRICS_FORMATTER_MAP;
export const UNIT_MAP = TEMPERATURE_METRICS_UNIT_MAP;

export const METRIC_MAP: Pick<
  MetricLookupMap<TemperatureMetrics>,
  'primaryMetricKey' | 'tierMetricKey'
> = {
  primaryMetricKey: 'yoyTemperatureDelta',
  tierMetricKey: 'tier',
};

export const TRANSFORMER = makeTemperatureMetrics;
