import {useMemo} from 'react';

import {useAppSelector} from 'store/useRedux';

import {selectMeasurement} from 'containers/login/login-selectors';
import {useFetchAndTransformKPI} from 'containers/si/hooks/useFetchAndTransformKPI';
import {
  C2GLULCMETRICS_FORMATTER_MAP,
  C2GLULCMETRICS_UNIT_MAP,
  makeC2gLulcMetrics,
  type C2gLulcMetrics,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.cropland_to_grassland_lulc.transformation';
import {
  G2C_DLUC_METRICS_FORMATTER_MAP,
  G2C_DLUC_METRICS_UNIT_MAP,
  makeG2cDlucMetrics,
  type G2cDlucMetrics,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.grassland_to_cropland_dluc.transformation';
import {
  G2CLULCMETRICS_FORMATTER_MAP,
  G2CLULCMETRICS_UNIT_MAP,
  makeG2cLulcMetrics,
  type G2cLulcMetrics,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.grassland_to_cropland_lulc.transformation';
import {PANEL_CONTENT} from 'containers/si/programs/report/kpi/components/LandUseChangeCard/content';
import {type PrimaryMetricTileProps} from 'containers/si/programs/report/kpi/components/PrimaryMetricTile';
import {type TimeTrendTileProps} from 'containers/si/programs/report/kpi/components/TimeTrendTile';
import {useGetKPIDashboardCommonFilters} from 'containers/si/programs/report/kpi/hooks/useGetKPIDashboardCommonFilters';
import {usePrimaryMetricTileAndTimeTrendTileContent} from 'containers/si/programs/report/kpi/hooks/usePrimaryMetricTileAndTimeTrendTileContent';
import type {MetricLookupMap} from 'containers/si/programs/report/kpi/types';

export type UseLandUseChangeCardData = {
  isLoading: boolean;
  data: {
    g2cPrimaryMetricTileContent: PrimaryMetricTileProps | null;
    g2cTimeTrendTileContent: TimeTrendTileProps | null;
    g2cdlucPrimaryMetricTileContent: PrimaryMetricTileProps | null;
    g2cdlucTimeTrendTileContent: TimeTrendTileProps | null;
    c2gPrimaryMetricTileContent: PrimaryMetricTileProps | null;
    c2gTimeTrendTileContent: TimeTrendTileProps | null;
  };
};

const G2C_METRIC_MAP: Required<
  Pick<MetricLookupMap<G2cLulcMetrics>, 'primaryMetricKey' | 'trackedMetricKey'>
> = {
  primaryMetricKey: 'convertedArea',
  trackedMetricKey: 'totalTrackedArea',
};

const C2G_METRIC_MAP: Required<
  Pick<MetricLookupMap<C2gLulcMetrics>, 'primaryMetricKey' | 'trackedMetricKey'>
> = {
  primaryMetricKey: 'restoredArea',
  trackedMetricKey: 'totalTrackedArea',
};

const G2C_DLUC_METRIC_MAP: Required<Pick<MetricLookupMap<G2cDlucMetrics>, 'primaryMetricKey'>> = {
  primaryMetricKey: 'convertedArea',
};

export const useLandUseChangeCardData = (): UseLandUseChangeCardData => {
  const {filtersState, commonFilters} = useGetKPIDashboardCommonFilters();

  const userUnitsSystem = useAppSelector(selectMeasurement);

  const grasslandToCroplandTransformer = useMemo(
    () => makeG2cLulcMetrics(userUnitsSystem),
    [userUnitsSystem]
  );

  const {isLoading: g2CIsLoading, annualizedSummary: g2cByYearMetricsPairs} =
    useFetchAndTransformKPI({
      kpi: 'grassland_to_cropland_lulc',
      summaries: ['annualized'],
      commonFilters,
      kpiTransformer: grasslandToCroplandTransformer,
      shouldFetch: !PANEL_CONTENT.G2C.isDisabledMessage(filtersState), // LUC metrics are not available with Data Scenario usage
    });

  const {
    primaryMetricTileContent: g2cPrimaryMetricTileContent,
    timeTrendTileContent: g2cTimeTrendTileContent,
  } = usePrimaryMetricTileAndTimeTrendTileContent({
    byYearMetricsPairs: g2cByYearMetricsPairs,
    filtersState,
    isLoading: g2CIsLoading,
    formatterMap: G2CLULCMETRICS_FORMATTER_MAP,
    metricMap: G2C_METRIC_MAP,
    unitMap: G2CLULCMETRICS_UNIT_MAP,
    isDownwardChangePositive: PANEL_CONTENT.G2C.isDownwardChangePositive,
  });

  const croplandToGrasslandTransformer = useMemo(
    () => makeC2gLulcMetrics(userUnitsSystem),
    [userUnitsSystem]
  );

  const {isLoading: c2gIsLoading, annualizedSummary: c2gByYearMetricsPairs} =
    useFetchAndTransformKPI({
      kpi: 'cropland_to_grassland_lulc',
      summaries: ['annualized'],
      commonFilters,
      kpiTransformer: croplandToGrasslandTransformer,
      shouldFetch: !PANEL_CONTENT.C2G.isDisabledMessage(filtersState), // LUC metrics are not available with Data Scenario usage
    });

  const {
    primaryMetricTileContent: c2gPrimaryMetricTileContent,
    timeTrendTileContent: c2gTimeTrendTileContent,
  } = usePrimaryMetricTileAndTimeTrendTileContent({
    byYearMetricsPairs: c2gByYearMetricsPairs,
    filtersState,
    isLoading: c2gIsLoading,
    formatterMap: C2GLULCMETRICS_FORMATTER_MAP,
    metricMap: C2G_METRIC_MAP,
    unitMap: C2GLULCMETRICS_UNIT_MAP,
    isDownwardChangePositive: PANEL_CONTENT.C2G.isDownwardChangePositive,
  });

  const grasslandToCroplandDlucTransformer = useMemo(
    () => makeG2cDlucMetrics(userUnitsSystem),
    [userUnitsSystem]
  );

  const {isLoading: g2cdlucIsLoading, annualizedSummary: g2cdlucByYearMetricsPairs} =
    useFetchAndTransformKPI({
      kpi: 'grassland_to_cropland_dluc',
      summaries: ['annualized'],
      commonFilters,
      kpiTransformer: grasslandToCroplandDlucTransformer,
      shouldFetch: !PANEL_CONTENT.G2C_DLUC.isDisabledMessage(filtersState), // LUC metrics are not available with Data Scenario usage
    });

  const {
    primaryMetricTileContent: g2cdlucPrimaryMetricTileContent,
    timeTrendTileContent: g2cdlucTimeTrendTileContent,
  } = usePrimaryMetricTileAndTimeTrendTileContent({
    byYearMetricsPairs: g2cdlucByYearMetricsPairs,
    filtersState,
    isLoading: g2cdlucIsLoading,
    formatterMap: G2C_DLUC_METRICS_FORMATTER_MAP,
    metricMap: G2C_DLUC_METRIC_MAP,
    unitMap: G2C_DLUC_METRICS_UNIT_MAP,
    isDownwardChangePositive: PANEL_CONTENT.G2C_DLUC.isDownwardChangePositive,
  });

  const data = {
    g2cPrimaryMetricTileContent,
    g2cTimeTrendTileContent,
    c2gPrimaryMetricTileContent,
    c2gTimeTrendTileContent,
    g2cdlucPrimaryMetricTileContent,
    g2cdlucTimeTrendTileContent,
  };

  return {isLoading: g2CIsLoading || c2gIsLoading, data};
};
