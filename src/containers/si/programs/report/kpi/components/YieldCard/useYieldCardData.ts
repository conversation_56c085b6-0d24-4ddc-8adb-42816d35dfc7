import uniq from 'lodash/uniq';
import {useMemo} from 'react';

import {useAppSelector} from 'store/useRedux';

import {getTypedValues} from '_common/utils/object';
import {isDefined} from '_common/utils/typeGuards';

import {selectMeasurement} from 'containers/login/login-selectors';
import {useFetchAndTransformKPI} from 'containers/si/hooks/useFetchAndTransformKPI';
import {
  makeYieldPerAreaMetrics,
  YIELD_PER_AREA_METRICS_FORMATTER_MAP,
  YIELD_PER_AREA_METRICS_UNIT_MAP,
  type YieldPerAreaMetrics,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.yield_per_area.transformation';
import {type PrimaryMetricTileProps} from 'containers/si/programs/report/kpi/components/PrimaryMetricTile';
import {type TimeTrendTileProps} from 'containers/si/programs/report/kpi/components/TimeTrendTile';
import {PANEL_CONTENT} from 'containers/si/programs/report/kpi/components/YieldCard/content';
import {useCropSummaryTypeBarChartContent} from 'containers/si/programs/report/kpi/hooks/useCropSummaryTypeBarChartContent';
import {useGetKPIDashboardCommonFilters} from 'containers/si/programs/report/kpi/hooks/useGetKPIDashboardCommonFilters';
import {usePrimaryMetricTileAndTimeTrendTileContent} from 'containers/si/programs/report/kpi/hooks/usePrimaryMetricTileAndTimeTrendTileContent';
import {useSubregionSummaryTypeBarChartContent} from 'containers/si/programs/report/kpi/hooks/useSubregionSummaryTypeBarChartContent';
import type {MetricLookupMap, SummaryTypeChartProps} from 'containers/si/programs/report/kpi/types';

export type UseYieldCardData = {
  isLoading: boolean;
  data: {
    primaryMetricTileContent: PrimaryMetricTileProps | null;
    timeTrendTileContent: TimeTrendTileProps | null;
    cropSummaryTypeBarChartContent: SummaryTypeChartProps | null;
    subregionSummaryTypeBarChartContent: SummaryTypeChartProps | null;
  };
};

const KPI = 'yield_per_area';
const FORMATTER_MAP = YIELD_PER_AREA_METRICS_FORMATTER_MAP;
const UNIT_MAP = YIELD_PER_AREA_METRICS_UNIT_MAP;
const METRIC_MAP: Pick<MetricLookupMap<YieldPerAreaMetrics>, 'primaryMetricKey'> = {
  primaryMetricKey: 'yieldPerArea',
};

export const useYieldCardData = (): UseYieldCardData => {
  const {filtersState, commonFilters} = useGetKPIDashboardCommonFilters();

  const userUnitsSystem = useAppSelector(selectMeasurement);
  const yieldPerAreaTransformer = useMemo(
    () => makeYieldPerAreaMetrics(userUnitsSystem),
    [userUnitsSystem]
  );

  const summariesForFetch = useMemo(
    () =>
      uniq(
        getTypedValues(PANEL_CONTENT)
          .map(({isDisabledMessage, fetchSummary}) =>
            !isDisabledMessage(filtersState) ? fetchSummary : undefined
          )
          .filter(isDefined)
      ),
    [filtersState]
  );

  const {
    isLoading,
    annualizedSummary: byYearMetricsPairs,
    boundarySummary: bySubregionMetricsPairs,
    cropTypeSummary: byCropMetricsPairs,
  } = useFetchAndTransformKPI({
    kpi: KPI,
    summaries: summariesForFetch,
    commonFilters,
    kpiTransformer: yieldPerAreaTransformer,
  });

  const {primaryMetricTileContent, timeTrendTileContent} =
    usePrimaryMetricTileAndTimeTrendTileContent({
      byYearMetricsPairs,
      filtersState,
      isLoading,
      formatterMap: FORMATTER_MAP,
      metricMap: METRIC_MAP,
      unitMap: UNIT_MAP,
    });

  const cropSummaryTypeBarChartContent: SummaryTypeChartProps | null =
    useCropSummaryTypeBarChartContent({
      byCropMetricsPairs,
      filtersState,
      isLoading,
      formatterMap: FORMATTER_MAP,
      metricMap: METRIC_MAP,
      unitMap: UNIT_MAP,
    });

  const subregionSummaryTypeBarChartContent: SummaryTypeChartProps | null =
    useSubregionSummaryTypeBarChartContent({
      bySubregionMetricsPairs,
      isLoading,
      filtersState,
      formatterMap: FORMATTER_MAP,
      metricMap: METRIC_MAP,
      unitMap: UNIT_MAP,
    });

  const data = {
    subregionSummaryTypeBarChartContent,
    cropSummaryTypeBarChartContent,
    primaryMetricTileContent,
    timeTrendTileContent,
  };

  return {isLoading, data};
};
