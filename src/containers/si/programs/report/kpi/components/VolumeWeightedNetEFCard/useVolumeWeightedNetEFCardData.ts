import uniq from 'lodash/uniq';
import {useMemo} from 'react';

import {getTypedValues} from '_common/utils/object';
import {isDefined} from '_common/utils/typeGuards';

import {useFetchAndTransformKPI} from 'containers/si/hooks/useFetchAndTransformKPI';
import {
  makeVolumeWeightedNetEFMetrics,
  VOLUMEWEIGHTEDNETEFMETRICS_FORMATTER_MAP,
  VOLUMEWEIGHTEDNETEFMETRICS_UNIT_MAP,
  type VolumeWeightedNetEFMetrics,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.volume_weighted_net_emissions_factor.transformation';
import {type PrimaryMetricTileProps} from 'containers/si/programs/report/kpi/components/PrimaryMetricTile';
import {type TimeTrendTileProps} from 'containers/si/programs/report/kpi/components/TimeTrendTile';
import {
  CARD_CONTENT,
  PANEL_CONTENT,
} from 'containers/si/programs/report/kpi/components/VolumeWeightedNetEFCard/content';
import {useCropSummaryTypeBarChartContent} from 'containers/si/programs/report/kpi/hooks/useCropSummaryTypeBarChartContent';
import {useGetKPIDashboardCommonFilters} from 'containers/si/programs/report/kpi/hooks/useGetKPIDashboardCommonFilters';
import {useHasPurchaseVolume} from 'containers/si/programs/report/kpi/hooks/useHasPurchaseVolume';
import {usePrimaryMetricTileAndTimeTrendTileContent} from 'containers/si/programs/report/kpi/hooks/usePrimaryMetricTileAndTimeTrendTileContent';
import type {MetricLookupMap, SummaryTypeChartProps} from 'containers/si/programs/report/kpi/types';

export type UseVolumeWeightedNetEFCardData = {
  isLoading: boolean;
  data: {
    primaryMetricTileContent: PrimaryMetricTileProps | null;
    timeTrendTileContent: TimeTrendTileProps | null;
    cropSummaryTypeBarChartContent: SummaryTypeChartProps | null;
    hasPurchaseVolume: boolean;
  };
};

const KPI = 'volume_weighted_net_emissions_factor';
const FORMATTER_MAP = VOLUMEWEIGHTEDNETEFMETRICS_FORMATTER_MAP;
const UNIT_MAP = VOLUMEWEIGHTEDNETEFMETRICS_UNIT_MAP;

const METRIC_MAP: Pick<MetricLookupMap<VolumeWeightedNetEFMetrics>, 'primaryMetricKey'> = {
  primaryMetricKey: 'volumeWeighteNetEmissionsPerYeild',
};

export const useVolumeWeightedNetEFCardData = (): UseVolumeWeightedNetEFCardData => {
  const {filtersState, commonFilters} = useGetKPIDashboardCommonFilters();

  const {hasPurchaseVolume} = useHasPurchaseVolume();

  const summariesForFetch = useMemo(
    () =>
      uniq(
        getTypedValues(PANEL_CONTENT)
          .map(({isDisabledMessage, fetchSummary}) =>
            !isDisabledMessage(filtersState, hasPurchaseVolume) ? fetchSummary : undefined
          )
          .filter(isDefined)
      ),
    [filtersState, hasPurchaseVolume]
  );

  const {
    isLoading,
    annualizedSummary: byYearMetricsPairs,
    cropTypeSummary: byCropMetricsPairs,
  } = useFetchAndTransformKPI({
    kpi: KPI,
    summaries: summariesForFetch,
    commonFilters,
    kpiTransformer: makeVolumeWeightedNetEFMetrics,
  });
  const {primaryMetricTileContent, timeTrendTileContent} =
    usePrimaryMetricTileAndTimeTrendTileContent({
      byYearMetricsPairs,
      filtersState,
      isLoading,
      formatterMap: FORMATTER_MAP,
      metricMap: METRIC_MAP,
      unitMap: UNIT_MAP,
      isDownwardChangePositive: CARD_CONTENT.isDownwardChangePositive,
    });

  const cropSummaryTypeBarChartContent: SummaryTypeChartProps | null =
    useCropSummaryTypeBarChartContent({
      byCropMetricsPairs,
      filtersState,
      isLoading,
      formatterMap: FORMATTER_MAP,
      metricMap: METRIC_MAP,
      unitMap: UNIT_MAP,
    });

  const data = {
    cropSummaryTypeBarChartContent,
    primaryMetricTileContent,
    timeTrendTileContent,
    hasPurchaseVolume,
  };

  return {isLoading, data};
};
