import {capitalizeFirstLetter} from '_common/utils/string';

import {
  DROUGHT_CONTENT,
  DROUGHT_METRICS_FORMATTER_MAP,
  DROUGHT_METRICS_UNIT_MAP,
  makeDroughtMetrics,
  type DroughtMetrics,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.drought_index.transformation';
import {PANEL_LABEL} from 'containers/si/programs/report/kpi/constants';
import type {MetricLookupMap} from 'containers/si/programs/report/kpi/types';

const tooltipContent = (panelLabel: PANEL_LABEL) => DROUGHT_CONTENT.explainer(panelLabel);

export const CARD_CONTENT = {
  title: capitalizeFirstLetter(DROUGHT_CONTENT.name),
  isDownwardChangePositive: true,
};

export const PANEL_CONTENT = {
  ByYear: {
    title: `${CARD_CONTENT.title} ${PANEL_LABEL.OVER_TIME}`,
    label: PANEL_LABEL.OVER_TIME,
    tooltipContent: tooltipContent(PANEL_LABEL.OVER_TIME),
  },
  ByBoundary: {
    title: `${CARD_CONTENT.title} ${PANEL_LABEL.BY_AREA}`,
    label: PANEL_LABEL.BY_AREA,
    tooltipContent: tooltipContent(PANEL_LABEL.BY_AREA),
    sortDirection: 'DESC',
  },
} as const;

export const KPI = 'drought_index';
export const FORMATTER_MAP = DROUGHT_METRICS_FORMATTER_MAP;
export const UNIT_MAP = DROUGHT_METRICS_UNIT_MAP;

export const METRIC_MAP: Pick<
  MetricLookupMap<DroughtMetrics>,
  'primaryMetricKey' | 'tierMetricKey'
> = {
  primaryMetricKey: 'droughtIndex',
  tierMetricKey: 'tier',
};

export const TRANSFORMER = makeDroughtMetrics;
