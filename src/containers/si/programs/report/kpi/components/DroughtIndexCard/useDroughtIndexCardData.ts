import {useMemo} from 'react';

import {getTypedValues} from '_common/utils/object';
import {isDefined, isNil} from '_common/utils/typeGuards';

import {useFetchAndTransformKPI} from 'containers/si/hooks/useFetchAndTransformKPI';
import {getChartDataRange} from 'containers/si/programs/helpers/chart.helpers';
import {BE_TO_FE_DROUGHT_INDEX_MAP} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.drought_index.transformation';
import {
  CARD_CONTENT,
  FORMATTER_MAP,
  KPI,
  METRIC_MAP,
  TRANSFORMER,
  UNIT_MAP,
} from 'containers/si/programs/report/kpi/components/DroughtIndexCard/content';
import {type PrimaryMetricTileProps} from 'containers/si/programs/report/kpi/components/PrimaryMetricTile';
import {type TimeTrendTileProps} from 'containers/si/programs/report/kpi/components/TimeTrendTile';
import {useGetKPIDashboardCommonFilters} from 'containers/si/programs/report/kpi/hooks/useGetKPIDashboardCommonFilters';
import {usePrimaryMetricTileAndTimeTrendTileContent} from 'containers/si/programs/report/kpi/hooks/usePrimaryMetricTileAndTimeTrendTileContent';
import {useSubregionSummaryTypeBarChartContent} from 'containers/si/programs/report/kpi/hooks/useSubregionSummaryTypeBarChartContent';
import type {SummaryTypeChartProps} from 'containers/si/programs/report/kpi/types';

export type UseDroughtIndexCardData = {
  isLoading: boolean;
  data: {
    primaryMetricTileContent: PrimaryMetricTileProps | null;
    timeTrendTileContent: TimeTrendTileProps | null;
    subregionSummaryTypeBarChartContent: SummaryTypeChartProps | null;
  };
};

export const useDroughtIndexCardData = (): UseDroughtIndexCardData => {
  const {filtersState, commonFilters} = useGetKPIDashboardCommonFilters();

  const {
    isLoading,
    annualizedSummary: byYearMetricsPairs,
    boundarySummary: bySubregionMetricsPairs,
  } = useFetchAndTransformKPI({
    kpi: KPI,
    summaries: ['annualized', 'boundary'],
    commonFilters,
    kpiTransformer: TRANSFORMER,
  });

  const chartRange = useMemo(() => {
    const droughtIndexClassifications = getTypedValues(BE_TO_FE_DROUGHT_INDEX_MAP);
    const chartRange_ = getChartDataRange(droughtIndexClassifications);

    return isDefined(chartRange_) ? {...chartRange_, stepSize: 1} : undefined;
  }, []);

  const {primaryMetricTileContent, timeTrendTileContent: timeTrendTileContent_} =
    usePrimaryMetricTileAndTimeTrendTileContent({
      byYearMetricsPairs,
      filtersState,
      isLoading,
      formatterMap: FORMATTER_MAP,
      metricMap: METRIC_MAP,
      unitMap: UNIT_MAP,
      isDownwardChangePositive: CARD_CONTENT.isDownwardChangePositive,
    });

  const timeTrendTileContent: TimeTrendTileProps | null = useMemo(() => {
    if (isNil(timeTrendTileContent_)) return null;

    return {
      ...timeTrendTileContent_,
      range: chartRange, // set the chart axis to the fixed drought classification values
    };
  }, [chartRange, timeTrendTileContent_]);

  const subregionSummaryTypeBarChartContent_: SummaryTypeChartProps | null =
    useSubregionSummaryTypeBarChartContent({
      bySubregionMetricsPairs,
      isLoading,
      filtersState,
      formatterMap: FORMATTER_MAP,
      metricMap: METRIC_MAP,
      unitMap: UNIT_MAP,
    });

  const subregionSummaryTypeBarChartContent: SummaryTypeChartProps | null = useMemo(() => {
    if (isNil(subregionSummaryTypeBarChartContent_)) return null;

    return {
      ...subregionSummaryTypeBarChartContent_,
      range: chartRange, // set the chart axis to the fixed drought classification values
    };
  }, [chartRange, subregionSummaryTypeBarChartContent_]);

  const data = {
    subregionSummaryTypeBarChartContent,
    primaryMetricTileContent,
    timeTrendTileContent,
  };

  return {isLoading, data};
};
