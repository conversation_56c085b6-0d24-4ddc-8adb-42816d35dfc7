import {useMemo} from 'react';

import {useFetchBookValues} from 'containers/si/api/swr/hooks/useFetchBookValues';
import {EF_BOOK_VALUE_LABEL, EF_REGROW_BASELINE_LABEL} from 'containers/si/constants';
import {useFetchAndTransformKPI} from 'containers/si/hooks/useFetchAndTransformKPI';
import {
  BOOK_VALUE_EF_METRICS_LABEL_MAP,
  bookValueEFFormatter,
  bookValueEFUnit,
  makeCropTypeBookValueEFMetricsPairs,
  makeCropTypeRegrowBaselineEFMetricsPairs,
} from 'containers/si/programs/helpers/book_values.transformation';
import {makeFertEFMetrics} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.fert_emissions_factor.transformation';
import {makeGhgEFMetrics} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.ghg_emissions_factor.transformation';
import {makeNetGhgEFMetrics} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.net_ghg_emissions_factor.transformation';
import {BOOK_VALUE_EF_METRICS_STACK_ORDER} from 'containers/si/programs/report/kpi/components/BookValueEFComparisonCard/content';
import {useCropLookupPairs} from 'containers/si/programs/report/kpi/hooks/useCropLookupPairs';
import {useGetKPIDashboardCommonFilters} from 'containers/si/programs/report/kpi/hooks/useGetKPIDashboardCommonFilters';
import {useSubTypeStackedAndGroupedOverSummaryTypeBarChartContent} from 'containers/si/programs/report/kpi/hooks/useSubTypeStackedAndGroupedOverSummaryTypeBarChartContent';
import type {SubTypeStackedAndGroupedOverSummaryTypeBarChartProps} from 'containers/si/programs/report/kpi/types';
import type {Metric} from 'containers/si/utils/value.types';

export type UseBookValueEFComparisonCardData = {
  isLoading: boolean;
  data: {
    ghgEFsStackededAndGroupedOverCropSummaryTypeBarChartContent: SubTypeStackedAndGroupedOverSummaryTypeBarChartProps | null;
    netEFsStackededAndGroupedOverCropSummaryTypeBarChartContent: SubTypeStackedAndGroupedOverSummaryTypeBarChartProps | null;
  };
};

export const useBookValueEFComparisonCardData = (): UseBookValueEFComparisonCardData => {
  const {commonFilters, filtersState} = useGetKPIDashboardCommonFilters();

  const {isLoading: isFetchBookValuesLoading, data} = useFetchBookValues();
  const bookValueEFMetricsByCropTypePairs = makeCropTypeBookValueEFMetricsPairs(data);

  const {isLoading: isFetchGhgEFKpiLoading, cropTypeSummary: ghgEFByCropMetricsPairs} =
    useFetchAndTransformKPI({
      kpi: 'ghg_emissions_factor',
      summaries: ['crop_type'],
      commonFilters,
      kpiTransformer: makeGhgEFMetrics,
    });

  const {isLoading: isFetchNetEFKpiLoading, cropTypeSummary: netEFByCropMetricsPairs} =
    useFetchAndTransformKPI({
      kpi: 'net_ghg_emissions_factor',
      summaries: ['crop_type'],
      commonFilters,
      kpiTransformer: makeNetGhgEFMetrics,
    });

  const {isLoading: isFetchFertEFKpiLoading, cropTypeSummary: fertEFByCropMetricsPairs} =
    useFetchAndTransformKPI({
      kpi: 'fert_emissions_factor',
      summaries: ['crop_type'],
      commonFilters,
      kpiTransformer: makeFertEFMetrics,
    });

  const regrowBaselineGhgEFMetricsByCropType = useMemo(() => {
    const fieldEmissionsEFByCropMetricsPairs = ghgEFByCropMetricsPairs?.map<
      [string, Metric | null]
    >(([cropId, metrics]) => [cropId, metrics?.ghgEmissionsPerYield ?? null]);

    return makeCropTypeRegrowBaselineEFMetricsPairs({
      bookValueEFMetricsByCropTypePairs,
      fieldEmissionsEFByCropMetricsPairs,
      fertilizerEmissionsEFByCropMetricsPairs: fertEFByCropMetricsPairs,
    });
  }, [bookValueEFMetricsByCropTypePairs, fertEFByCropMetricsPairs, ghgEFByCropMetricsPairs]);

  const regrowBaselineNetEFMetricsByCropType = useMemo(() => {
    const fieldEmissionsEFByCropMetricsPairs = netEFByCropMetricsPairs?.map<
      [string, Metric | null]
    >(([cropId, metrics]) => [cropId, metrics?.netGhgEmissionsPerYield ?? null]);

    return makeCropTypeRegrowBaselineEFMetricsPairs({
      bookValueEFMetricsByCropTypePairs,
      fieldEmissionsEFByCropMetricsPairs,
      fertilizerEmissionsEFByCropMetricsPairs: fertEFByCropMetricsPairs,
    });
  }, [bookValueEFMetricsByCropTypePairs, fertEFByCropMetricsPairs, netEFByCropMetricsPairs]);

  const selectedCropLookupPairs = useCropLookupPairs({filtersState});

  const netEFsStackededAndGroupedOverCropSummaryTypeBarChartContent =
    useSubTypeStackedAndGroupedOverSummaryTypeBarChartContent({
      byGroupBySummaryTypeMetricsPairs: [
        [EF_BOOK_VALUE_LABEL, bookValueEFMetricsByCropTypePairs],
        [EF_REGROW_BASELINE_LABEL, regrowBaselineNetEFMetricsByCropType],
      ],
      formatter: bookValueEFFormatter,
      isLoading: isFetchBookValuesLoading,
      labelMap: BOOK_VALUE_EF_METRICS_LABEL_MAP,
      nameToIdLookupPairs: selectedCropLookupPairs,
      stackOrder: BOOK_VALUE_EF_METRICS_STACK_ORDER,
      unitDetail: bookValueEFUnit(),
    });

  const ghgEFsStackededAndGroupedOverCropSummaryTypeBarChartContent =
    useSubTypeStackedAndGroupedOverSummaryTypeBarChartContent({
      byGroupBySummaryTypeMetricsPairs: [
        [EF_BOOK_VALUE_LABEL, bookValueEFMetricsByCropTypePairs],
        [EF_REGROW_BASELINE_LABEL, regrowBaselineGhgEFMetricsByCropType],
      ],
      formatter: bookValueEFFormatter,
      isLoading: isFetchBookValuesLoading,
      labelMap: BOOK_VALUE_EF_METRICS_LABEL_MAP,
      nameToIdLookupPairs: selectedCropLookupPairs,
      stackOrder: BOOK_VALUE_EF_METRICS_STACK_ORDER,
      unitDetail: bookValueEFUnit(),
    });

  return {
    isLoading:
      isFetchBookValuesLoading ||
      isFetchGhgEFKpiLoading ||
      isFetchNetEFKpiLoading ||
      isFetchFertEFKpiLoading,
    data: {
      ghgEFsStackededAndGroupedOverCropSummaryTypeBarChartContent,
      netEFsStackededAndGroupedOverCropSummaryTypeBarChartContent,
    },
  };
};
