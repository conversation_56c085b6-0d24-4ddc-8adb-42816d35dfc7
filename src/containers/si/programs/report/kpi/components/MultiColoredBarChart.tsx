import React, {useMemo} from 'react';

import {Bar<PERSON>hart, Box, Stack, Typography} from '@regrow-internal/design-system';

import {isDefined} from '_common/utils/typeGuards';

import {VERTICAL_BAR_CHART_LABEL_MAX_CHAR_COUNT} from 'containers/si/programs/report/kpi/constants';
import {makeSummaryTypeChartZeroRangeConfig} from 'containers/si/programs/report/kpi/helpers/makeSummaryTypeChartZeroRangeConfig';
import {type MultiColoredBarChartProps} from 'containers/si/programs/report/kpi/types';

export const MultiColoredBarChart = ({
  barColors,
  barColorsLegend,
  data,
  formatter,
  labels,
  tooltipText,
  unitDetail,
}: MultiColoredBarChartProps) => {
  const {primaryAxisBuffer, primaryAxisMaxTicks, primaryAxisTickFormatter} = useMemo(
    () => makeSummaryTypeChartZeroRangeConfig(data, unitDetail),
    [data, unitDetail]
  );

  return (
    <Stack minHeight={'1px'} minWidth={'1px'} flex="1 1 auto">
      <Box height={1} width={1}>
        <BarChart
          data={{
            labels,
            datasets: [
              {
                data,
                backgroundColor: barColors,
              },
            ],
          }}
          options={{
            scales: {
              y: {
                title: {
                  display: true,
                  text: unitDetail.unitName.abbr,
                },
                grace: primaryAxisBuffer,
                ticks: {
                  callback: isDefined(primaryAxisTickFormatter)
                    ? primaryAxisTickFormatter
                    : (v: string | number) => formatter(Number(v)),
                  maxTicksLimit: primaryAxisMaxTicks,
                },
              },
              x: {
                ticks: {
                  callback: function (primaryAxisIndexValue) {
                    const label = String(this.getLabelForValue(Number(primaryAxisIndexValue)));

                    return label.length > VERTICAL_BAR_CHART_LABEL_MAX_CHAR_COUNT
                      ? `${String(label).substring(
                          0,
                          VERTICAL_BAR_CHART_LABEL_MAX_CHAR_COUNT - 3
                        )}...`
                      : label;
                  },
                },
              },
            },
            plugins: {
              tooltip: {
                callbacks: {
                  title: context => {
                    const label = context[0]?.label;

                    return label ?? '';
                  },
                  afterTitle: context => {
                    const datum = context[0]?.raw;
                    const unitLabel = unitDetail.unitName.abbr;
                    const formattedValue = datum
                      ? [formatter(Number(datum)), unitLabel].join(' ')
                      : '';

                    return formattedValue;
                  },
                  beforeLabel: ({dataIndex}) => tooltipText?.[dataIndex]?.secondaryText ?? '',
                  label: () => '',
                  afterLabel: ({dataIndex}) => tooltipText?.[dataIndex]?.unknownText ?? '',
                  afterBody: context => {
                    const dataIndex = context[0]?.dataIndex ?? -1;

                    return tooltipText?.[dataIndex]?.notApplicableText ?? '';
                  },
                },
              },
            },
          }}
        />
      </Box>
      <Stack direction="row" height={10} my={1}>
        {barColorsLegend.map(({color}) => (
          <Box key={`key-${color}`} flexGrow={1} height={10} bgcolor={color}></Box>
        ))}
      </Stack>
      <Stack direction="row" height={10} my={1} justifyContent="space-between">
        {barColorsLegend.map(({label}, i) => (
          <Typography key={`key-${i}`} variant="body2" color="secondary">
            {label}
          </Typography>
        ))}
      </Stack>
    </Stack>
  );
};
