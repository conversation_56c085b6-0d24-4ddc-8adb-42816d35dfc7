import {type ScriptableContext, type TooltipItem} from 'chart.js';
import React, {useCallback, useMemo} from 'react';

import {AverageLinePlugin, Box, LineChart, Stack, useTheme} from '@regrow-internal/design-system';

import {isDefined, isNil, isNonEmptyArray} from '_common/utils/typeGuards';

import {makeSummaryTypeChartZeroRangeConfig} from 'containers/si/programs/report/kpi/helpers/makeSummaryTypeChartZeroRangeConfig';
import {type ColoredSummaryTypeChartProps} from 'containers/si/programs/report/kpi/types';

export type TimeTrendTileProps = ColoredSummaryTypeChartProps & {
  activePointIndex?: number;
  beginYAxisAtZero?: boolean;
  hideAverageLine?: boolean;
};

export const TimeTrendTile = ({
  activePointIndex,
  beginYAxisAtZero,
  categoryColorKey,
  data,
  formatter,
  hideAverageLine,
  labels,
  range,
  tooltipText,
  unitDetail,
}: TimeTrendTileProps) => {
  const {palette} = useTheme();

  const {primaryAxisBuffer, primaryAxisMaxTicks, primaryAxisTickFormatter} = useMemo(
    () => (isDefined(range) ? {} : makeSummaryTypeChartZeroRangeConfig(data, unitDetail)), // prioritize specified range over auto gen range
    [data, range, unitDetail]
  );

  const averageLineFormatter = useCallback(
    (value: number) => `${formatter(value)} ${unitDetail.unitName.abbr}`,
    [formatter, unitDetail.unitName.abbr]
  );

  const xAxisTickFormatter = useCallback(
    (_label: string | number, index: number) => {
      const isFirstOrLast = index === 0 || index === data.length - 1;

      return isFirstOrLast ? `${labels?.[index]}` : '';
    },
    [data.length, labels]
  );

  const yAxisTickFormatter = useMemo(
    () =>
      isDefined(primaryAxisTickFormatter)
        ? primaryAxisTickFormatter
        : (v: string | number) => formatter(Number(v)),
    [formatter, primaryAxisTickFormatter]
  );

  const getPointRadius = useCallback(
    (ctx: ScriptableContext<'line'>) => {
      const {dataIndex} = ctx;

      if (dataIndex === activePointIndex) return 4; // selected point

      if (dataIndex === 0 && data.length > 1) {
        return data.at(1) === null ? 3 : 1; // first point is ghost point or standard point
      }

      if (dataIndex === data.length - 1 && data.length > 1) {
        return data.at(-2) === null ? 3 : 1; // last point is ghost point or standard point
      }

      return data.at(dataIndex - 1) === null && data.at(dataIndex + 1) === null ? 3 : 1; // other point is ghost point or standard point
    },
    [activePointIndex, data]
  );

  const primaryMetricTooltip = useCallback(
    (context: Array<TooltipItem<'line'>>) => {
      if (isDefined(tooltipText)) {
        const dataIndex = context[0]?.dataIndex ?? -1;

        return tooltipText?.[dataIndex]?.primaryMetricWithUncertaintyText ?? '';
      } else {
        const datum = context[0]?.raw;
        const unitLabel = unitDetail.unitName.abbr;
        const formattedValue = datum ? [formatter(Number(datum)), unitLabel].join(' ') : '';

        return formattedValue;
      }
    },
    [formatter, tooltipText, unitDetail]
  );

  const subMetricsTooltip = useCallback(
    (context: Array<TooltipItem<'line'>>) => {
      const dataIndex = context[0]?.dataIndex;
      if (isNil(dataIndex) || isNil(tooltipText?.[dataIndex])) return '';

      const text = [
        tooltipText[dataIndex]?.primaryAltMetricWithUncertaintyText,
        tooltipText[dataIndex]?.secondaryText,
        tooltipText[dataIndex]?.unknownText,
        tooltipText[dataIndex]?.notApplicableText,
        tooltipText[dataIndex]?.tierText,
      ].filter(isDefined);

      return isNonEmptyArray(text) ? text : '';
    },
    [tooltipText]
  );

  return (
    <Stack alignItems="stretch" flex={'1 1 auto'} minHeight={'1px'} minWidth="1px">
      <Box height={1} width={1}>
        <LineChart
          data={{
            labels,
            datasets: [
              {
                data,
              },
            ],
          }}
          plugins={hideAverageLine ? [] : [AverageLinePlugin]}
          options={{
            datasets: {
              line: {
                backgroundColor: 'transparent',
                borderWidth: 2,
                fill: 'start',
                pointBackgroundColor: ctx =>
                  ctx.dataIndex === activePointIndex
                    ? palette.semanticPalette.surface.main
                    : palette.categoryPalette[1].chart,
                pointHoverBackgroundColor: palette.semanticPalette.surface.main,
                pointBorderColor: ctx =>
                  ctx.dataIndex === activePointIndex
                    ? palette.categoryPalette[1].chart
                    : 'transparent',
                pointBorderWidth: 1,
                pointHoverBorderColor: palette.categoryPalette[1].chart,
                pointRadius: getPointRadius,
                tension: 0.2,
              },
            },
            layout: {
              padding: {
                top: 20,
                left: -6,
              },
            },
            scales: {
              x: {
                ticks: {
                  callback: xAxisTickFormatter,
                },
                grid: {
                  display: false,
                },
              },
              y: {
                title: {
                  display: true,
                  text: unitDetail.unitName.abbr,
                },
                beginAtZero: isDefined(range) ? undefined : beginYAxisAtZero, // prioritize specified range
                grace: primaryAxisBuffer,
                ticks: {
                  maxTicksLimit: primaryAxisMaxTicks,
                  callback: yAxisTickFormatter,
                  stepSize: range?.stepSize,
                },
                suggestedMin: range?.min,
                suggestedMax: range?.max,
              },
            },
            plugins: {
              colorThemes: {
                type: 'single',
                color: palette.categoryPalette[categoryColorKey ?? 1].chart,
              },
              averageLine: hideAverageLine
                ? undefined
                : {
                    label: `YoY average`,
                    palette,
                    formatter: averageLineFormatter,
                  },
              tooltip: {
                caretPadding: 8,
                yAlign: undefined,
                titleFont: {weight: 'bold'},
                footerColor: palette.semanticPalette.textInverted.secondary,
                footerFont: {weight: 'normal'},
                callbacks: {
                  title: context => context[0]?.label ?? '',
                  afterBody: primaryMetricTooltip,
                  afterFooter: subMetricsTooltip,
                  label: () => '',
                },
              },
            },
          }}
        />
      </Box>
    </Stack>
  );
};
