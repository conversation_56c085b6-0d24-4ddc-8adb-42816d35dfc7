import uniq from 'lodash/uniq';
import {useMemo} from 'react';

import {getTypedValues} from '_common/utils/object';
import {isDefined} from '_common/utils/typeGuards';

import {useFetchAndTransformKPI} from 'containers/si/hooks/useFetchAndTransformKPI';
import {
  FERT_EF_METRICS_FORMATTER_MAP,
  FERT_EF_METRICS_UNIT_MAP,
  makeFertEFMetrics,
  type FertEFMetrics,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.fert_emissions_factor.transformation';
import {PANEL_CONTENT} from 'containers/si/programs/report/kpi/components/FertilizerEFCard/content';
import {type PrimaryMetricTileProps} from 'containers/si/programs/report/kpi/components/PrimaryMetricTile';
import {type TimeTrendTileProps} from 'containers/si/programs/report/kpi/components/TimeTrendTile';
import {useCropSummaryTypeBarChartContent} from 'containers/si/programs/report/kpi/hooks/useCropSummaryTypeBarChartContent';
import {useGetKPIDashboardCommonFilters} from 'containers/si/programs/report/kpi/hooks/useGetKPIDashboardCommonFilters';
import {usePrimaryMetricTileAndTimeTrendTileContent} from 'containers/si/programs/report/kpi/hooks/usePrimaryMetricTileAndTimeTrendTileContent';
import {useSubregionSummaryTypeBarChartContent} from 'containers/si/programs/report/kpi/hooks/useSubregionSummaryTypeBarChartContent';
import type {MetricLookupMap, SummaryTypeChartProps} from 'containers/si/programs/report/kpi/types';

export type UseFertilizerEFCardData = {
  isLoading: boolean;
  data: {
    primaryMetricTileContent: PrimaryMetricTileProps | null;
    timeTrendTileContent: TimeTrendTileProps | null;
    cropSummaryTypeBarChartContent: SummaryTypeChartProps | null;
    subregionSummaryTypeBarChartContent: SummaryTypeChartProps | null;
  };
};

const KPI = 'fert_emissions_factor';
const FORMATTER_MAP = FERT_EF_METRICS_FORMATTER_MAP;
const UNIT_MAP = FERT_EF_METRICS_UNIT_MAP;
const METRIC_MAP: Pick<MetricLookupMap<FertEFMetrics>, 'primaryMetricKey'> = {
  primaryMetricKey: 'fertEmissionsPerYield',
};
const TRANSFORMER = makeFertEFMetrics;

export const useFertilizerEFCardData = (): UseFertilizerEFCardData => {
  const {filtersState, commonFilters} = useGetKPIDashboardCommonFilters();

  const summariesForFetch = useMemo(
    () =>
      uniq(
        getTypedValues(PANEL_CONTENT)
          .map(({isDisabledMessage, fetchSummary}) =>
            !isDisabledMessage(filtersState) ? fetchSummary : undefined
          )
          .filter(isDefined)
      ),
    [filtersState]
  );

  const {
    isLoading,
    annualizedSummary: byYearMetricsPairs,
    boundarySummary: bySubregionMetricsPairs,
    cropTypeSummary: byCropMetricsPairs,
  } = useFetchAndTransformKPI({
    kpi: KPI,
    summaries: summariesForFetch,
    commonFilters,
    kpiTransformer: TRANSFORMER,
  });

  const {primaryMetricTileContent, timeTrendTileContent} =
    usePrimaryMetricTileAndTimeTrendTileContent({
      byYearMetricsPairs,
      filtersState,
      isLoading,
      formatterMap: FORMATTER_MAP,
      metricMap: METRIC_MAP,
      unitMap: UNIT_MAP,
      isDownwardChangePositive: PANEL_CONTENT.ByYear.isDownwardChangePositive,
    });

  const cropSummaryTypeBarChartContent: SummaryTypeChartProps | null =
    useCropSummaryTypeBarChartContent({
      byCropMetricsPairs,
      filtersState,
      isLoading,
      formatterMap: FORMATTER_MAP,
      metricMap: METRIC_MAP,
      unitMap: UNIT_MAP,
    });

  const subregionSummaryTypeBarChartContent: SummaryTypeChartProps | null =
    useSubregionSummaryTypeBarChartContent({
      bySubregionMetricsPairs,
      isLoading,
      filtersState,
      formatterMap: FORMATTER_MAP,
      metricMap: METRIC_MAP,
      unitMap: UNIT_MAP,
    });

  const data = {
    subregionSummaryTypeBarChartContent,
    cropSummaryTypeBarChartContent,
    primaryMetricTileContent,
    timeTrendTileContent,
  };

  return {isLoading, data};
};
