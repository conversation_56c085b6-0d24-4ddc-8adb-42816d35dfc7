import type {TooltipItem} from 'chart.js';
import React, {useCallback} from 'react';

import {Box, HorizontalBarChart, Stack, useTheme} from '@regrow-internal/design-system';

import {isDefined, isNil, isNonEmptyArray} from '_common/utils/typeGuards';

import {BAR_CHART_LABEL_MAX_CHAR_COUNT} from 'containers/si/programs/report/kpi/constants';
import type {ColoredSummaryTypeChartProps} from 'containers/si/programs/report/kpi/types';

export const ColoredSummaryTypeBarChart = ({
  categoryColorKey,
  data,
  formatter,
  labels,
  range,
  tooltipText,
  unitDetail,
}: ColoredSummaryTypeChartProps) => {
  const {palette} = useTheme();
  const primaryMetricTooltip = useCallback(
    (context: Array<TooltipItem<'bar'>>) => {
      if (isDefined(tooltipText)) {
        const dataIndex = context[0]?.dataIndex ?? -1;

        return tooltipText?.[dataIndex]?.primaryMetricWithUncertaintyText ?? '';
      } else {
        const datum = context[0]?.raw;
        const unitLabel = unitDetail.unitName.abbr;
        const formattedValue = datum ? [formatter(Number(datum)), unitLabel].join(' ') : '';

        return formattedValue;
      }
    },
    [formatter, tooltipText, unitDetail]
  );

  const subMetricsTooltip = useCallback(
    (context: Array<TooltipItem<'bar'>>) => {
      const dataIndex = context[0]?.dataIndex;
      if (isNil(dataIndex) || isNil(tooltipText?.[dataIndex])) return '';

      const text = [
        tooltipText[dataIndex]?.primaryAltMetricWithUncertaintyText,
        tooltipText[dataIndex]?.secondaryText,
        tooltipText[dataIndex]?.unknownText,
        tooltipText[dataIndex]?.notApplicableText,
        tooltipText[dataIndex]?.tierText,
      ].filter(isDefined);

      return isNonEmptyArray(text) ? text : '';
    },
    [tooltipText]
  );

  return (
    <Stack minHeight={'1px'} minWidth={'1px'} flex={'1 1 auto'}>
      <Box height={1} width={1}>
        <HorizontalBarChart
          data={{
            labels,
            datasets: [
              {
                data,
              },
            ],
          }}
          options={{
            scales: {
              x: {
                title: {
                  display: true,
                  text: unitDetail.unitName.abbr,
                },
                ticks: {
                  callback: (v: string | number) => formatter(Number(v)),
                  stepSize: range?.stepSize,
                },
                suggestedMin: range?.min,
                suggestedMax: range?.max,
              },
              y: {
                ticks: {
                  callback: function (primaryAxisIndexValue) {
                    const label = String(this.getLabelForValue(Number(primaryAxisIndexValue)));

                    return label.length > BAR_CHART_LABEL_MAX_CHAR_COUNT
                      ? `${String(label).substring(0, BAR_CHART_LABEL_MAX_CHAR_COUNT - 3)}...`
                      : label;
                  },
                },
              },
            },
            plugins: {
              colorThemes: {
                type: 'single',
                color: palette.categoryPalette[categoryColorKey ?? 1].chart,
              },
              tooltip: {
                titleFont: {weight: 'bold'},
                footerColor: palette.semanticPalette.textInverted.secondary,
                footerFont: {weight: 'normal'},
                callbacks: {
                  title: context => context[0]?.label ?? '',
                  afterBody: primaryMetricTooltip,
                  afterFooter: subMetricsTooltip,
                  label: () => '',
                },
              },
            },
          }}
        />
      </Box>
    </Stack>
  );
};
