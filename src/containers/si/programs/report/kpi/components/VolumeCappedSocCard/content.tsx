import React from 'react';

import {CardTooltip} from 'containers/si/programs/report/kpi/components/CardTooltip';
import {
  NO_DATA_FOR_MISSING_PURCHASE_VOLUME_COPY,
  PANEL_LABEL,
} from 'containers/si/programs/report/kpi/constants';

const makeTooltip = (panelLabel: string): JSX.Element => (
  <CardTooltip
    title={`Total field SOC sequestration ${panelLabel} (volume-capped)`}
    body={[
      `The sum of all SOC sequestered broken down ${panelLabel}, specific to the volume of the commodities purchased.`,
      `Note that standard error is not available for volume-based data and that volume-based data is omitted for commodities/subregions/years without volumes.`,
    ]}
  />
);

export const CARD_CONTENT = {isDownwardChangePositive: false};

export const PANEL_CONTENT = {
  ByYear: {
    title: `Total field SOC sequestration ${PANEL_LABEL.OVER_TIME} (volume-capped)`,
    label: PANEL_LABEL.OVER_TIME,
    tooltip: makeTooltip(PANEL_LABEL.OVER_TIME),
  },
  ByCrop: {
    title: `Total field SOC sequestration ${PANEL_LABEL.BY_CROP_TYPE} (volume-capped)`,
    label: PANEL_LABEL.BY_CROP_TYPE,
    tooltip: makeTooltip(PANEL_LABEL.BY_CROP_TYPE),
    isDisabledMessage: (hasPurchaseVolume: boolean) =>
      !hasPurchaseVolume ? NO_DATA_FOR_MISSING_PURCHASE_VOLUME_COPY : undefined,
  },
  ByBoundary: {
    title: `Total field SOC sequestration ${PANEL_LABEL.BY_AREA} (volume-capped)`,
    label: PANEL_LABEL.BY_AREA,
    tooltip: makeTooltip(PANEL_LABEL.BY_AREA),
    isDisabledMessage: (hasPurchaseVolume: boolean) =>
      !hasPurchaseVolume ? NO_DATA_FOR_MISSING_PURCHASE_VOLUME_COPY : undefined,
  },
} as const;
