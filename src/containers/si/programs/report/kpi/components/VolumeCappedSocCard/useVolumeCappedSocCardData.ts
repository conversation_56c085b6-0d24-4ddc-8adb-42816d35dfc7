import {useFetchAndTransformKPI} from 'containers/si/hooks/useFetchAndTransformKPI';
import {
  makeVolumeWeightedSocMetrics,
  VOLUMEWEIGHTEDSOCMETRICS_FORMATTER_MAP,
  VOLUMEWEIGHTEDSOCMETRICS_UNIT_MAP,
  type VolumeWeightedSocMetrics,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.volume_weighted_soc_kg.transformation';
import {type PrimaryMetricTileProps} from 'containers/si/programs/report/kpi/components/PrimaryMetricTile';
import {type TimeTrendTileProps} from 'containers/si/programs/report/kpi/components/TimeTrendTile';
import {CARD_CONTENT} from 'containers/si/programs/report/kpi/components/VolumeCappedSocCard/content';
import {useCropSummaryTypeBarChartContent} from 'containers/si/programs/report/kpi/hooks/useCropSummaryTypeBarChartContent';
import {useGetKPIDashboardCommonFilters} from 'containers/si/programs/report/kpi/hooks/useGetKPIDashboardCommonFilters';
import {useHasPurchaseVolume} from 'containers/si/programs/report/kpi/hooks/useHasPurchaseVolume';
import {usePrimaryMetricTileAndTimeTrendTileContent} from 'containers/si/programs/report/kpi/hooks/usePrimaryMetricTileAndTimeTrendTileContent';
import {useSubregionSummaryTypeBarChartContent} from 'containers/si/programs/report/kpi/hooks/useSubregionSummaryTypeBarChartContent';
import type {MetricLookupMap, SummaryTypeChartProps} from 'containers/si/programs/report/kpi/types';

export type UseVolumeCappedSocCardData = {
  isLoading: boolean;
  data: {
    primaryMetricTileContent: PrimaryMetricTileProps | null;
    timeTrendTileContent: TimeTrendTileProps | null;
    cropSummaryTypeBarChartContent: SummaryTypeChartProps | null;
    subregionSummaryTypeBarChartContent: SummaryTypeChartProps | null;
    hasPurchaseVolume: boolean;
  };
};

const KPI = 'volume_weighted_soc_kg';
const FORMATTER_MAP = VOLUMEWEIGHTEDSOCMETRICS_FORMATTER_MAP;
const UNIT_MAP = VOLUMEWEIGHTEDSOCMETRICS_UNIT_MAP;

const METRIC_MAP: Pick<MetricLookupMap<VolumeWeightedSocMetrics>, 'primaryMetricKey'> = {
  primaryMetricKey: 'volumeWeightedSocMass',
};

export const useVolumeCappedSocCardData = (): UseVolumeCappedSocCardData => {
  const {hasPurchaseVolume} = useHasPurchaseVolume();
  const {filtersState, commonFilters} = useGetKPIDashboardCommonFilters();

  const {
    isLoading,
    annualizedSummary: byYearMetricsPairs,
    boundarySummary: bySubregionMetricsPairs,
    cropTypeSummary: byCropMetricsPairs,
  } = useFetchAndTransformKPI({
    kpi: KPI,
    summaries: ['annualized', 'crop_type', 'boundary'],
    commonFilters,
    kpiTransformer: makeVolumeWeightedSocMetrics,
  });
  const {primaryMetricTileContent, timeTrendTileContent} =
    usePrimaryMetricTileAndTimeTrendTileContent({
      byYearMetricsPairs,
      filtersState,
      isLoading,
      formatterMap: FORMATTER_MAP,
      metricMap: METRIC_MAP,
      unitMap: UNIT_MAP,
      isDownwardChangePositive: CARD_CONTENT.isDownwardChangePositive,
    });

  const cropSummaryTypeBarChartContent: SummaryTypeChartProps | null =
    useCropSummaryTypeBarChartContent({
      byCropMetricsPairs,
      filtersState,
      isLoading,
      formatterMap: FORMATTER_MAP,
      metricMap: METRIC_MAP,
      unitMap: UNIT_MAP,
    });

  const subregionSummaryTypeBarChartContent: SummaryTypeChartProps | null =
    useSubregionSummaryTypeBarChartContent({
      bySubregionMetricsPairs,
      isLoading,
      filtersState,
      formatterMap: FORMATTER_MAP,
      metricMap: METRIC_MAP,
      unitMap: UNIT_MAP,
    });

  const data = {
    subregionSummaryTypeBarChartContent,
    cropSummaryTypeBarChartContent,
    primaryMetricTileContent,
    timeTrendTileContent,
    hasPurchaseVolume,
  };

  return {isLoading, data};
};
