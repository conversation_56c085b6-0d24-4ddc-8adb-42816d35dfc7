import pick from 'lodash/pick';
import {useFeatureFlagEnabled} from 'posthog-js/react';
import {useMemo} from 'react';

import {useAppSelector} from 'store/useRedux';

import {selectMeasurement} from 'containers/login/login-selectors';
import {useFetchAndTransformKPI} from 'containers/si/hooks/useFetchAndTransformKPI';
import {
  makeTillageExpectedMetrics,
  makeTillageMetrics,
  TILLAGEMETRICS_FORMATTER_MAP,
  TILLAGEMETRICS_LABEL_MAP,
  TILLAGEMETRICS_UNIT_MAP,
  type TillageMetrics,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.tillage.transformation';
import {type PrimaryMetricTileProps} from 'containers/si/programs/report/kpi/components/PrimaryMetricTile';
import {type TimeTrendTileProps} from 'containers/si/programs/report/kpi/components/TimeTrendTile';
import {useCropSummaryTypeBarChartContent} from 'containers/si/programs/report/kpi/hooks/useCropSummaryTypeBarChartContent';
import {useGetKPIDashboardCommonFilters} from 'containers/si/programs/report/kpi/hooks/useGetKPIDashboardCommonFilters';
import {usePrimaryMetricTileAndTimeTrendTileContent} from 'containers/si/programs/report/kpi/hooks/usePrimaryMetricTileAndTimeTrendTileContent';
import {useSubregionSummaryTypeBarChartContent} from 'containers/si/programs/report/kpi/hooks/useSubregionSummaryTypeBarChartContent';
import {useSubTypeStackedOverTimeBarChartContent} from 'containers/si/programs/report/kpi/hooks/useSubTypeStackedOverTimeBarChartContent';
import type {
  MetricLookupMap,
  SubTypeStackedOverTimeBarChartProps,
  SummaryTypeChartProps,
} from 'containers/si/programs/report/kpi/types';

export type UseTillageCardData = {
  isLoading: boolean;
  data: {
    primaryMetricTileContent: PrimaryMetricTileProps | null;
    timeTrendTileContent: TimeTrendTileProps | null;
    tillageTypeOverTimeBarChartContent: SubTypeStackedOverTimeBarChartProps | null;
    cropSummaryTypeBarChartContent: SummaryTypeChartProps | null;
    subregionSummaryTypeBarChartContent: SummaryTypeChartProps | null;
  };
};

const KPI = 'tillage';
const FORMATTER_MAP = TILLAGEMETRICS_FORMATTER_MAP;
const UNIT_MAP = TILLAGEMETRICS_UNIT_MAP;

const METRIC_MAP: Pick<
  MetricLookupMap<TillageMetrics>,
  | 'primaryMetricKey'
  | 'secondaryMetricKey'
  | 'uncertaintyMetricKey'
  | 'secondaryUncertaintyMetricKey'
  | 'trackedMetricKey'
  | 'unknownMetricKey'
> = {
  primaryMetricKey: 'adoption',
  uncertaintyMetricKey: 'adoptionStdDev',
  secondaryMetricKey: 'conservationTillageArea',
  secondaryUncertaintyMetricKey: 'conservationTillageAreaStdDev',
  trackedMetricKey: 'totalTrackedArea',
  unknownMetricKey: 'unknownArea',
};

const BY_SUBTYPE_LABEL_MAP = pick(TILLAGEMETRICS_LABEL_MAP, [
  'noTillageArea',
  'reducedTillageArea',
  'conventionalTillageArea',
  'unknownArea',
]);

export const useTillageCardData = (): UseTillageCardData => {
  const {filtersState, commonFilters} = useGetKPIDashboardCommonFilters();

  const userUnitsSystem = useAppSelector(selectMeasurement);

  // TODO: SI-3064 remove feature flag and hard code transformer as constant with constants above
  const isExpectedValuesEnabled = useFeatureFlagEnabled('si-expected-values');
  const isMosaicEnabled = useFeatureFlagEnabled('si-mosaic-be');
  const kpiTransformer = useMemo(
    () =>
      isExpectedValuesEnabled && isMosaicEnabled
        ? makeTillageExpectedMetrics(userUnitsSystem)
        : makeTillageMetrics(userUnitsSystem),
    [isExpectedValuesEnabled, isMosaicEnabled, userUnitsSystem]
  );

  const {
    isLoading,
    annualizedSummary: byYearMetricsPairs,
    boundarySummary: bySubregionMetricsPairs,
    cropTypeSummary: byCropMetricsPairs,
  } = useFetchAndTransformKPI({
    kpi: KPI,
    summaries: ['annualized', 'boundary', 'crop_type'],
    commonFilters,
    kpiTransformer,
  });

  const {primaryMetricTileContent, timeTrendTileContent} =
    usePrimaryMetricTileAndTimeTrendTileContent({
      byYearMetricsPairs,
      filtersState,
      isLoading,
      formatterMap: FORMATTER_MAP,
      metricMap: METRIC_MAP,
      unitMap: UNIT_MAP,
    });

  // all metrics (defined by labelMap keys) must have the same unitDetail and formatter
  const tillageTypeOverTimeBarChartContent = useSubTypeStackedOverTimeBarChartContent({
    byYearMetricsPairs,
    filtersState,
    formatter: FORMATTER_MAP['conservationTillageArea'],
    labelMap: BY_SUBTYPE_LABEL_MAP,
    unitDetail: UNIT_MAP['conservationTillageArea'](userUnitsSystem),
  });

  const cropSummaryTypeBarChartContent: SummaryTypeChartProps | null =
    useCropSummaryTypeBarChartContent({
      byCropMetricsPairs,
      filtersState,
      isLoading,
      formatterMap: FORMATTER_MAP,
      metricMap: METRIC_MAP,
      unitMap: UNIT_MAP,
    });

  const subregionSummaryTypeBarChartContent: SummaryTypeChartProps | null =
    useSubregionSummaryTypeBarChartContent({
      bySubregionMetricsPairs,
      isLoading,
      filtersState,
      formatterMap: FORMATTER_MAP,
      metricMap: METRIC_MAP,
      unitMap: UNIT_MAP,
    });

  const data = {
    subregionSummaryTypeBarChartContent,
    cropSummaryTypeBarChartContent,
    tillageTypeOverTimeBarChartContent,
    primaryMetricTileContent,
    timeTrendTileContent,
  };

  return {isLoading, data};
};
