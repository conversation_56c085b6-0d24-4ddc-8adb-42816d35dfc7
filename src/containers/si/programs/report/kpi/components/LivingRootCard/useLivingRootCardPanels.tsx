import React, {useMemo} from 'react';

import {isDefined} from '_common/utils/typeGuards';

import {PANEL_CONTENT} from 'containers/si/programs/report/kpi/components/LivingRootCard/content';
import {type UseLivingRootCardData} from 'containers/si/programs/report/kpi/components/LivingRootCard/useLivingRootCardData';
import {MultiColoredBarChart} from 'containers/si/programs/report/kpi/components/MultiColoredBarChart';
import {NoData} from 'containers/si/programs/report/kpi/components/NoData';
import {OverTimePanel} from 'containers/si/programs/report/kpi/components/OverTimePanel';

export const useLivingRootCardPanels = ({
  data: {livingRootByMonthBarChartContent, primaryMetricTileContent, timeTrendTileContent},
}: UseLivingRootCardData) => {
  const OverTimePanelComponent = useMemo(
    () => (
      <OverTimePanel
        primaryMetricTileContent={primaryMetricTileContent}
        timeTrendTileContent={timeTrendTileContent}
      />
    ),
    [primaryMetricTileContent, timeTrendTileContent]
  );

  const ByMonthChartComponent = useMemo(
    () =>
      isDefined(livingRootByMonthBarChartContent) ? (
        <MultiColoredBarChart {...livingRootByMonthBarChartContent} />
      ) : (
        <NoData />
      ),
    [livingRootByMonthBarChartContent]
  );

  const panelsLookup = useMemo(
    () => ({
      [PANEL_CONTENT.CoverOverTime.label]: {
        component: OverTimePanelComponent,
        ...PANEL_CONTENT.CoverOverTime,
      },
      [PANEL_CONTENT.CoverSelectedYear.label]: {
        component: ByMonthChartComponent,
        ...PANEL_CONTENT.CoverSelectedYear,
      },
    }),
    [OverTimePanelComponent, ByMonthChartComponent]
  );

  return panelsLookup;
};
