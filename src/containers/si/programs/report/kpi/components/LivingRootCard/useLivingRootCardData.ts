import fromPairs from 'lodash/fromPairs';
import {useMemo} from 'react';

import {isNil} from '_common/utils/typeGuards';

import {useFetchAndTransformKPI} from 'containers/si/hooks/useFetchAndTransformKPI';
import {
  LIVING_ROOT_COVER_DAYS_FORMATTER,
  LIVING_ROOT_COVER_DAYS_UNIT,
  LIVING_ROOT_COVER_METRICS_FORMATTER_MAP,
  LIVING_ROOT_COVER_METRICS_UNIT_MAP,
  makeAllLivingRootCoverDaysMetricsFromMetrics,
  makeLivingRootCoverMetrics,
  type LivingRootCoverDaysMetrics,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.living_root_cover.transformation';
import {makeLivingRootHealthMetrics} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.living_root_health.transformation';
import type {FormatterMapType, UnitMapType} from 'containers/si/programs/helpers/types';
import {useLivingRootByMonthColoredBarChartContent} from 'containers/si/programs/report/kpi/components/LivingRootCard/useLivingRootByMonthColoredBarChartContent';
import {type PrimaryMetricTileProps} from 'containers/si/programs/report/kpi/components/PrimaryMetricTile';
import {type TimeTrendTileProps} from 'containers/si/programs/report/kpi/components/TimeTrendTile';
import {useGetKPIDashboardCommonFilters} from 'containers/si/programs/report/kpi/hooks/useGetKPIDashboardCommonFilters';
import {usePrimaryMetricTileAndTimeTrendTileContent} from 'containers/si/programs/report/kpi/hooks/usePrimaryMetricTileAndTimeTrendTileContent';
import type {
  MetricLookupMap,
  MultiColoredBarChartProps,
} from 'containers/si/programs/report/kpi/types';
import type {Metric} from 'containers/si/utils/value.types';

export type UseLivingRootCardData = {
  isLoading: boolean;
  data: {
    livingRootByMonthBarChartContent: MultiColoredBarChartProps | null;
    primaryMetricTileContent: PrimaryMetricTileProps | null;
    timeTrendTileContent: TimeTrendTileProps | null;
  };
};

const COVER_KPI = 'living_root_cover';

const HEALTH_KPI = 'living_root_health';

type LivingRootCustomMetricType = {
  avg: Metric | null;
  avgDays: Metric | null;
};

const METRIC_MAP: Pick<
  MetricLookupMap<LivingRootCustomMetricType>,
  'primaryMetricKey' | 'secondaryMetricKey'
> = {
  primaryMetricKey: 'avg',
  secondaryMetricKey: 'avgDays',
};

const UNIT_MAP: UnitMapType<LivingRootCustomMetricType> = {
  avg: LIVING_ROOT_COVER_METRICS_UNIT_MAP.yearAvg,
  avgDays: LIVING_ROOT_COVER_DAYS_UNIT,
};

const FORMATTER_MAP: FormatterMapType<LivingRootCustomMetricType> = {
  avg: LIVING_ROOT_COVER_METRICS_FORMATTER_MAP.yearAvg,
  avgDays: LIVING_ROOT_COVER_DAYS_FORMATTER,
};

export const useLivingRootCardData = (): UseLivingRootCardData => {
  const {filtersState, commonFilters} = useGetKPIDashboardCommonFilters();

  const livingRootCoverTransformer = makeLivingRootCoverMetrics;
  const livingRootHealthTransformer = makeLivingRootHealthMetrics;

  const {
    isLoading: coverIsLoading,
    annualizedSummary: coverByYearMetricsPairs,
    topLevel: topLevelCoverMetrics,
  } = useFetchAndTransformKPI({
    kpi: COVER_KPI,
    summaries: ['annualized'],
    commonFilters,
    kpiTransformer: livingRootCoverTransformer,
  });

  const {isLoading: healthIsLoading, topLevel: topLevelHealthMetrics} = useFetchAndTransformKPI({
    kpi: HEALTH_KPI,
    summaries: [],
    commonFilters,
    kpiTransformer: livingRootHealthTransformer,
  });

  const byYearLivingRootCoverDaysMetricsLookup = useMemo(() => {
    if (isNil(coverByYearMetricsPairs)) return null;

    return fromPairs(
      coverByYearMetricsPairs.map<[string, LivingRootCoverDaysMetrics | null]>(
        ([year, metrics]) => [year, makeAllLivingRootCoverDaysMetricsFromMetrics(metrics)]
      )
    );
  }, [coverByYearMetricsPairs]);

  const byYearLivingRootCoverPercentAndDaysMetricsPairs = useMemo(() => {
    if (isNil(coverByYearMetricsPairs) || isNil(byYearLivingRootCoverDaysMetricsLookup))
      return null;

    return coverByYearMetricsPairs.map<[string, LivingRootCustomMetricType]>(([year, metrics]) => [
      year,
      {
        avg: metrics?.yearAvg ?? null,
        avgDays: byYearLivingRootCoverDaysMetricsLookup[year]?.yearAvgDays ?? null,
      },
    ]);
  }, [coverByYearMetricsPairs, byYearLivingRootCoverDaysMetricsLookup]);

  const {primaryMetricTileContent, timeTrendTileContent} =
    usePrimaryMetricTileAndTimeTrendTileContent({
      byYearMetricsPairs: byYearLivingRootCoverPercentAndDaysMetricsPairs,
      filtersState,
      isLoading: coverIsLoading,
      formatterMap: FORMATTER_MAP,
      metricMap: METRIC_MAP,
      unitMap: UNIT_MAP,
    });

  const livingRootByMonthBarChartContent = useLivingRootByMonthColoredBarChartContent({
    byYearLivingRootCoverDaysMetricsLookup,
    filtersState,
    isLoading: coverIsLoading,
    livingRootCoverMetrics: topLevelCoverMetrics,
    livingRootHealthMetrics: topLevelHealthMetrics,
  });

  const data = {
    livingRootByMonthBarChartContent,
    primaryMetricTileContent,
    timeTrendTileContent,
  };

  return {isLoading: coverIsLoading || healthIsLoading, data};
};
