import omit from 'lodash/omit';
import {useMemo} from 'react';

import {useTheme} from '@regrow-internal/design-system';

import {getTypedEntries} from '_common/utils/object';
import {isNil} from '_common/utils/typeGuards';

import {MONTHS_SHORT} from 'containers/si/constants';
import {
  LIVING_ROOT_COVER_DAYS_FORMATTER,
  LIVING_ROOT_COVER_DAYS_UNIT,
  LIVING_ROOT_COVER_METRICS_FORMATTER_MAP,
  LIVING_ROOT_COVER_METRICS_UNIT_MAP,
  type LivingRootCoverDaysMetrics,
  type LivingRootCoverMetrics,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.living_root_cover.transformation';
import {
  getNDVIcolor,
  LIVING_ROOT_HEALTH_NDVI_TO_CATEGORY_COLOR_MAP,
  type LivingRootHealthMetrics,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.living_root_health.transformation';
import type {
  FormatterMapType,
  LivingRootMonthKeys,
  UnitMapType,
} from 'containers/si/programs/helpers/types';
import {curriedMakeBySummaryTypeKeyMetricLookups} from 'containers/si/programs/report/kpi/helpers/curriedMakeBySummaryTypeKeyMetricLookups';
import {makeMetricMapLookups} from 'containers/si/programs/report/kpi/helpers/makeMetricMapLookups';
import {makeSummaryTypeChartContent} from 'containers/si/programs/report/kpi/helpers/makeSummaryTypeChartContent';
import {useUnitAndMetricHelpers} from 'containers/si/programs/report/kpi/hooks/useUnitAndMetricHelpers';
import type {
  KpiMetrics,
  MetricLookupMap,
  MultiColoredBarChartProps,
} from 'containers/si/programs/report/kpi/types';
import type {KPIDataFiltersState} from 'containers/si/types';
import type {Metric} from 'containers/si/utils/value.types';

type LivingRootCustomMetricType = {
  avg: Metric | null;
  avgDays: Metric | null;
};

const METRIC_MAP: Pick<
  MetricLookupMap<LivingRootCustomMetricType>,
  'primaryMetricKey' | 'secondaryMetricKey'
> = {
  primaryMetricKey: 'avg',
  secondaryMetricKey: 'avgDays',
};

const UNIT_MAP: UnitMapType<LivingRootCustomMetricType> = {
  avg: LIVING_ROOT_COVER_METRICS_UNIT_MAP.Jan,
  avgDays: LIVING_ROOT_COVER_DAYS_UNIT,
};

const FORMATTER_MAP: FormatterMapType<LivingRootCustomMetricType> = {
  avg: LIVING_ROOT_COVER_METRICS_FORMATTER_MAP.Jan,
  avgDays: LIVING_ROOT_COVER_DAYS_FORMATTER,
};

export const useLivingRootByMonthColoredBarChartContent = ({
  byYearLivingRootCoverDaysMetricsLookup,
  filtersState,
  isLoading,
  livingRootCoverMetrics,
  livingRootHealthMetrics,
}: {
  byYearLivingRootCoverDaysMetricsLookup: Record<string, LivingRootCoverDaysMetrics | null> | null;
  filtersState: KPIDataFiltersState;
  isLoading: boolean;
  livingRootCoverMetrics: LivingRootCoverMetrics | null;
  livingRootHealthMetrics: LivingRootHealthMetrics | null;
}) => {
  const {palette} = useTheme();

  const byMonthMetricLookups = useMemo(() => {
    if (
      isLoading ||
      isNil(filtersState.year) ||
      isNil(livingRootCoverMetrics) ||
      isNil(livingRootHealthMetrics) ||
      isNil(byYearLivingRootCoverDaysMetricsLookup)
    )
      return null;

    const selectedYear = filtersState.year;

    const byMonthLivingRootCoverPercentAndDaysMetricsPairs = getTypedEntries(
      omit(livingRootCoverMetrics, 'yearAvg')
    ).map<[string, KpiMetrics | null]>(([month, metric]) => [
      month,
      {
        avg: metric,
        avgDays: byYearLivingRootCoverDaysMetricsLookup[selectedYear]?.[`${month}Days`] ?? null,
      },
    ]);

    const makeLookup = curriedMakeBySummaryTypeKeyMetricLookups(
      byMonthLivingRootCoverPercentAndDaysMetricsPairs
    );

    return makeMetricMapLookups({makeLookup, metricMap: METRIC_MAP});
  }, [
    byYearLivingRootCoverDaysMetricsLookup,
    livingRootCoverMetrics,
    livingRootHealthMetrics,
    filtersState.year,
    isLoading,
  ]);

  const unitAndMetricHelpers = useUnitAndMetricHelpers({
    unitMap: UNIT_MAP,
    metricMap: METRIC_MAP,
    formatterMap: FORMATTER_MAP,
  });

  const byMonthColoredBarChartContent: MultiColoredBarChartProps | null = useMemo(() => {
    if (isNil(byMonthMetricLookups) || isNil(livingRootHealthMetrics)) return null;

    const summaryTypeChartContent = makeSummaryTypeChartContent({
      bySubtypeBySummaryTypeKeyMetricLookups: byMonthMetricLookups,
      nameToIdLookupPairs: MONTHS_SHORT.map(m => [m, m]),
      unitAndMetricHelpers,
    });

    if (isNil(summaryTypeChartContent)) return null;

    // the label keys are known to be LivingRootMonthKeys, rather than simple strings
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    const months = summaryTypeChartContent.labels as unknown as Array<LivingRootMonthKeys>;

    const barColorsLegend = LIVING_ROOT_HEALTH_NDVI_TO_CATEGORY_COLOR_MAP.map(({color, label}) => ({
      color: palette.categoryPalette[color].highlight,
      label,
    }));

    const barColors = months.map(month => {
      const categoryColorKey = getNDVIcolor(livingRootHealthMetrics[month]);

      return categoryColorKey
        ? palette.categoryPalette[categoryColorKey].highlight
        : palette.grey[200];
    });

    return {...summaryTypeChartContent, barColors, barColorsLegend};
  }, [
    byMonthMetricLookups,
    livingRootHealthMetrics,
    palette.categoryPalette,
    palette.grey,
    unitAndMetricHelpers,
  ]);

  return byMonthColoredBarChartContent;
};
