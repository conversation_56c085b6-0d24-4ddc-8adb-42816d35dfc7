import {type ChartDataset} from 'chart.js';
import type React from 'react';

import {type Theme} from '@regrow-internal/design-system';

import {type MeasurementEnum} from '_common/utils/measurement-unit-options';

import type {KPI, OutcomeKPI, PracticeKPI} from 'containers/si/api/apiTypes';
import type {Metric, UnitDetail, UnitType} from 'containers/si/utils/value.types';

export type KpiMetrics = Record<string, Metric | null | undefined>;

/**
 * NOTE: primaryMetric, uncertaintyMetric, primaryAltMetric, uncertaintyAltMetric must have the same unit type.
 * Also, secondaryMetric, trackedMetric, unknownMetric, notApplicableMetric MUST have the same unit type.
 * These groups will utilize the same unitDetails and formatters
 *  ex. {
 *    primaryMetricKey: unit-interval;
 *    uncertaintyMetricKey?: unit-interval;
 *    primaryAltMetricKey: unit-interval;
 *    uncertaintyAltMetricKey?: unit-interval;
 *    secondaryMetricKey?: ha;
 *    trackedMetricKey?: ha;
 *    unknownMetricKey?: ha;
 *    notApplicableMetricKey?: ha;
 *  }
 */
export type MetricLookupMap<T> = {
  primaryMetricKey: keyof T;
  uncertaintyMetricKey?: keyof T;
  primaryAltMetricKey?: keyof T;
  uncertaintyAltMetricKey?: keyof T;
  secondaryMetricKey?: keyof T;
  secondaryUncertaintyMetricKey?: keyof T;
  trackedMetricKey?: keyof T;
  unknownMetricKey?: keyof T;
  notApplicableMetricKey?: keyof T;
  tierMetricKey?: keyof T;
};

export type FormatterLookupMap<T> = Record<keyof T, (value: number) => string>;

export type UnitLookupMap<T> = Record<
  keyof T,
  (unitsSystem: MeasurementEnum) => UnitDetail<UnitType>
>;

export type LabelLookupMap<T> = Record<
  keyof T,
  {
    label: string;
    categoryPaletteColorKey: keyof Theme['palette']['categoryPalette'];
    categoryPaletteColorLighten?: number;
  }
>;

export type SummaryTypeLookupPairs = Array<[string, string]>;

export type UnitAndMetricHelpers = {
  primaryMetricUnitDetails: UnitDetail<UnitType>;
  primaryMetricFormatter: (value: number) => string;
  secondaryMetricUnitDetails?: UnitDetail<UnitType>;
};

export type BySubtypeBySummaryTypeKeyMetricLookups = {
  primaryMetrics: KpiMetrics;
  uncertaintyMetrics?: KpiMetrics;
  primaryAltMetrics?: KpiMetrics;
  uncertaintyAltMetrics?: KpiMetrics;
  secondaryMetrics?: KpiMetrics;
  secondaryUncertaintyMetrics?: KpiMetrics;
  trackedMetrics?: KpiMetrics;
  unknownMetrics?: KpiMetrics;
  notApplicableMetrics?: KpiMetrics;
  tierMetrics?: KpiMetrics;
};

export type MetricTooltipContent = {
  primaryMetricWithUncertaintyText: string;
  primaryAltMetricWithUncertaintyText?: string;
  secondaryText?: string;
  unknownText?: string;
  notApplicableText?: string;
  tierText?: string;
};

export type ChartData = {
  labels: Array<string>;
  data: Array<number | null>;
  tooltipText?: Array<MetricTooltipContent>;
};

export type SummaryTypeChartProps = ChartData & {
  unitDetail: UnitDetail<UnitType>;
  formatter: (value: number) => string;
  range?: {min: number; max: number; stepSize?: number};
};

export type ColoredSummaryTypeChartProps = SummaryTypeChartProps & {
  categoryColorKey?: keyof Theme['palette']['categoryPalette'];
};

export type SubTypeDoughnutChartWithTotalProps = {
  backgroundColor: Array<string>;
  data: Array<number>;
  dataLabelFormatter: (value: number) => string;
  formatter: (value: number) => string;
  labels: Array<string>;
  unitDetail: UnitDetail<UnitType>;
};

export type MultiColoredBarChartProps = SummaryTypeChartProps & {
  barColors: Array<string>;
  barColorsLegend: Array<{color: string; label: string}>;
};

export type ChartDatasetData = {
  labels: Array<string>;
  datasets: Array<ChartDataset<'bar', ChartData['data']>>;
};

export type SubtypeStackedChartProps = ChartDatasetData & {
  unitDetail: UnitDetail<UnitType>;
  formatter: (value: number) => string;
  range?: {min: number; max: number};
};

export type SubTypeStackedOverTimeBarChartProps = SubtypeStackedChartProps;

export type SubTypeStackedAndGroupedOverSummaryTypeBarChartProps = SubtypeStackedChartProps & {
  groups: Array<string>;
  barColorsLegend: Array<{color: string; label: string}>;
};

// #region Dashboard Section types
export type ReportDashboardSection = {
  title: string;
  description?: React.ReactNode;
  bgColor?: string;
  helpLink?: string;
  categories: Array<Category>;
};

export type Category = {
  title: string;
  helpLink?: string;
  cardRows: Array<CardRow>;
  /** filter a whole category */
  extraAvailabilityCheck?: boolean;
};

// A group of cards to display on a single row together
type CardRow = Array<Card>;

type Card = {
  kpi: KPI | OutcomeKPI | PracticeKPI;
  /** filter a card */
  extraAvailabilityCheck?: boolean;
  component: () => React.ReactElement;
  minGridWidth?: 1 | 2 | 3;
};
// #endregion Dashboard Section types
