import uniq from 'lodash/uniq';
import React, {useCallback, useEffect, useMemo} from 'react';

import {
  DialogActions,
  DialogContent,
  FormLabel,
  SimpleDialog,
  Stack,
  Switch,
  Typography,
  type SimpleDialogProps,
} from '@regrow-internal/design-system';

import {downloadFile, MimeType} from '_common/utils/download';
import {isDefined} from '_common/utils/typeGuards';

import {MIXPANEL_EVENTS} from 'containers/shared/utils/constants';
import Mixpanel from 'containers/shared/utils/mixpanel-utils';
import SIApi from 'containers/si/api/si';
import {useFetchSupplySheds} from 'containers/si/api/swr/hooks/useFetchSupplySheds';
import {DownloadButton} from 'containers/si/components/dialogs/DownloadButton/DownloadButton';
import {ProgramCropsSelect} from 'containers/si/components/filters/CropsSelect/ProgramCropsSelect';
import {DataScenarioSelect} from 'containers/si/components/filters/DataScenarioSelect';
import {RegionsSelect} from 'containers/si/components/filters/RegionsSelect';
import {YearsSelect} from 'containers/si/components/filters/YearsSelect';
import {useCommonFilters} from 'containers/si/hooks/useCommonFilters';
import {useProgramId} from 'containers/si/hooks/useProgramId';
import {useTabAccess} from 'containers/si/hooks/useTabAccess';
import {getSupplyShedLookupBySubregionId} from 'containers/si/module/helpers/supply-shed-helpers';
import {DEAggregationLevelSelect} from 'containers/si/programs/report/kpi/data-export/DEAggregationSelect';
import {SupplyShedsSelect} from 'containers/si/programs/report/kpi/data-export/SupplyShedsSelect';
import type {AggregationUnitType} from 'containers/si/programs/report/kpi/data-export/types';
import {isValidCommonKpiFiltersState} from 'containers/si/typeGuards';

interface DataExportDialogProps {
  onClose: SimpleDialogProps['onClose'];
}

export const DataExportDialog: React.FC<DataExportDialogProps> = ({onClose}) => {
  const {getHasTabAccess} = useTabAccess();
  const {programId} = useProgramId();
  const {data: programSupplySheds} = useFetchSupplySheds();

  const [aggregationUnitFilter, setAggregationUnitFilter] =
    React.useState<AggregationUnitType>(undefined);
  const {setFilterState, filtersState, validCropIds, validSubsectionIds} = useCommonFilters({
    preventInitialization: true,
  });

  const supplyShedLookup = useMemo(
    () => getSupplyShedLookupBySubregionId(programSupplySheds ?? []),
    [programSupplySheds]
  );

  const filterIsValid = useMemo(
    () => isDefined(aggregationUnitFilter) && isValidCommonKpiFiltersState(filtersState),
    [aggregationUnitFilter, filtersState]
  );

  const onDownload = useCallback(async () => {
    Mixpanel.track(`click::${MIXPANEL_EVENTS.SI.dataExport}::export-trigger-button`);
    const translateSubregionIdsToSupplyShedIds = (subregionIds: Array<number>) => {
      return uniq(subregionIds.map(subregionId => supplyShedLookup[subregionId])).filter(isDefined);
    };

    if (isDefined(programId) && isDefined(filtersState.year) && isDefined(aggregationUnitFilter)) {
      try {
        const response = await SIApi.getDataExport(programId, {
          data:
            aggregationUnitFilter === 'supply_shed'
              ? {
                  crop_ids: validCropIds,
                  boundary_unit_ids: translateSubregionIdsToSupplyShedIds(validSubsectionIds),
                  year_of_interest: filtersState.year,
                  aggregation_unit: aggregationUnitFilter,
                  boundary_unit_type: 'supply_shed',
                  mrv_masked: filtersState.mrvMasked,
                }
              : {
                  crop_ids: validCropIds,
                  boundary_unit_ids: validSubsectionIds,
                  year_of_interest: filtersState.year,
                  aggregation_unit: isDefined(filtersState.dataScenarioId)
                    ? 'subsection'
                    : aggregationUnitFilter,
                  boundary_unit_type: 'subsection',
                  data_scenario_id: filtersState.dataScenarioId,
                  mrv_masked: filtersState.mrvMasked,
                },
        });

        const filename = `Regrow Sustainability Insights Export_${filtersState.year}.xlsx`;

        return Promise.resolve(downloadFile(response.data, filename, MimeType.ApplicationXlsx));
      } catch (error) {
        return Promise.reject(error);
      } finally {
        onClose({}, 'backdropClick');
      }
    } else {
      return Promise.reject('Invalid request');
    }
  }, [
    aggregationUnitFilter,
    filtersState.dataScenarioId,
    filtersState.mrvMasked,
    filtersState.year,
    onClose,
    programId,
    supplyShedLookup,
    validCropIds,
    validSubsectionIds,
  ]);

  useEffect(() => {
    if (isDefined(filtersState.dataScenarioId)) {
      setAggregationUnitFilter(prev => (prev !== 'subsection' ? undefined : prev));
    }
  }, [filtersState.dataScenarioId]);

  return (
    <SimpleDialog open title="Export data" onClose={onClose}>
      <DialogContent>
        <Stack gap={4}>
          <DataScenarioSelect
            dataScenarioId={filtersState.dataScenarioId}
            onDataScenarioChange={dataScenarioId => setFilterState({dataScenarioId})}
            label="Data scenario"
          />
          <DEAggregationLevelSelect
            aggregationUnit={aggregationUnitFilter}
            dataScenarioId={filtersState.dataScenarioId}
            onAggregationLevelChange={aggregationUnit => setAggregationUnitFilter(aggregationUnit)}
            label="Aggregation level"
          />
          {aggregationUnitFilter === 'supply_shed' ? (
            <SupplyShedsSelect
              regionIds={filtersState.subsectionIds}
              onRegionsChange={subsectionIds => setFilterState({subsectionIds})}
              label="Supply sheds"
            />
          ) : (
            <RegionsSelect
              regionIds={filtersState.subsectionIds}
              dataScenarioId={filtersState.dataScenarioId}
              onRegionsChange={subsectionIds => setFilterState({subsectionIds})}
              label="Subregions"
            />
          )}
          <ProgramCropsSelect
            cropIds={filtersState.cropIds}
            regionIds={filtersState.subsectionIds}
            dataScenarioId={filtersState.dataScenarioId}
            onCropsChange={cropIds => setFilterState({cropIds})}
            label="Crops"
          />
          <YearsSelect
            year={filtersState.year}
            onYearChange={year => setFilterState({year})}
            label="Year"
          />
          {getHasTabAccess('mask_mrv') && (
            <Stack gap={2}>
              <Stack>
                <FormLabel>Omit fields in MRV programs</FormLabel>
                <Typography variant="body2" color={'semanticPalette.text.secondary'}>
                  This will omit fields enrolled in any MRV program within the Regrow platform from
                  your data set.
                </Typography>
              </Stack>
              <Switch
                sx={{margin: 0}} // DS Switch has side margins
                checked={filtersState.mrvMasked}
                onChange={evt => setFilterState({mrvMasked: evt.target.checked})}
              />
            </Stack>
          )}
        </Stack>
      </DialogContent>
      <DialogActions>
        <DownloadButton disabled={!filterIsValid} onDownload={onDownload}>
          Export
        </DownloadButton>
      </DialogActions>
    </SimpleDialog>
  );
};
