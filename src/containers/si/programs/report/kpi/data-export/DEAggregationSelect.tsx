import React from 'react';

import {
  FormControl,
  FormLabel,
  MenuItem,
  Select,
  Stack,
  Typography,
} from '@regrow-internal/design-system';

import {getTypedEntries} from '_common/utils/object';
import {isDefined, isEmptyString, isUndefined} from '_common/utils/typeGuards';

import {UnavailableOptionLabel} from 'containers/si/components/filters/UnavailableOptionLabel';
import {DATA_EXPORT_AGGREGATION_LABELS} from 'containers/si/programs/report/kpi/data-export/constants';
import type {AggregationUnitType} from 'containers/si/programs/report/kpi/data-export/types';
import type {KPIDataFiltersState} from 'containers/si/types';

type AggregationLevelSelectProps = {
  aggregationUnit: AggregationUnitType;
  dataScenarioId: KPIDataFiltersState['dataScenarioId'];
  onAggregationLevelChange: (aggregationUnit: AggregationUnitType) => void;
  label?: string;
};

export const DEAggregationLevelSelect = ({
  aggregationUnit,
  dataScenarioId,
  onAggregationLevelChange,
  label,
}: AggregationLevelSelectProps) => {
  const isDataScenarioSelected = isDefined(dataScenarioId);

  return (
    <FormControl>
      <Stack gap={2}>
        {isDefined(label) && <FormLabel>{label}</FormLabel>}
        <Select<AggregationUnitType>
          aria-label="Select aggregation level"
          displayEmpty
          onChange={e =>
            onAggregationLevelChange(isEmptyString(e.target.value) ? undefined : e.target.value)
          }
          value={aggregationUnit ?? ''}
          label={label}
          renderValue={(value: AggregationUnitType) =>
            isEmptyString(value) || isUndefined(value) ? (
              <Typography color="text.placeholder">Select aggregation level</Typography>
            ) : (
              DATA_EXPORT_AGGREGATION_LABELS[value]
            )
          }
        >
          {getTypedEntries(DATA_EXPORT_AGGREGATION_LABELS).map(([key, optionLabel]) => {
            return key === 'subsection' ? (
              <MenuItem key={key} value="subsection">
                {DATA_EXPORT_AGGREGATION_LABELS['subsection']}{' '}
              </MenuItem>
            ) : (
              <MenuItem key={key} value={key} disabled={isDataScenarioSelected}>
                {isDataScenarioSelected ? (
                  <UnavailableOptionLabel
                    label={optionLabel}
                    message={`Aggregation by ${optionLabel} is only available with the Default data scenario`}
                  />
                ) : (
                  optionLabel
                )}
              </MenuItem>
            );
          })}
        </Select>
      </Stack>
    </FormControl>
  );
};
