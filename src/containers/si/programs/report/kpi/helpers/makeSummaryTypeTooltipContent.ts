import {capitalizeFirstLetter} from '_common/utils/string';
import {isDefined, isNil, isNonEmptyString, isNull, isUndefined} from '_common/utils/typeGuards';

import {
  CROPLAND_NOTAPPLICABLE_TEXT,
  CROPLAND_SATELLITE_UNVERIFIED_TEXT,
  CROPLAND_SATELLITE_VERIFIED_TEXT,
  EMPTY_DATA_CELL_VALUE,
  EXCLUDING_NOTAPPLICABLE_TEXT,
  PLUS_OR_MINUS,
  QUANTIFICATION_LEVEL_LABEL,
} from 'containers/si/constants';
import type {
  BySubtypeBySummaryTypeKeyMetricLookups,
  MetricTooltipContent,
  UnitAndMetricHelpers,
} from 'containers/si/programs/report/kpi/types';
import type {UnitDetail, UnitType, ValueWithFormatAndUnit} from 'containers/si/utils/value.types';

export const makeSummaryTypeTooltipContent = ({
  bySubtypeBySummaryTypeKeyMetricLookups,
  unitAndMetricHelpers,
  summaryTypeKeys,
}: {
  bySubtypeBySummaryTypeKeyMetricLookups: BySubtypeBySummaryTypeKeyMetricLookups;
  unitAndMetricHelpers: UnitAndMetricHelpers;
  summaryTypeKeys: Array<string>;
}): Array<MetricTooltipContent> | undefined =>
  summaryTypeKeys.map(key => {
    const primaryMetrics = {
      primaryMetric: bySubtypeBySummaryTypeKeyMetricLookups['primaryMetrics']?.[key],
      uncertaintyMetric: bySubtypeBySummaryTypeKeyMetricLookups['uncertaintyMetrics']?.[key],
    };

    const primaryMetricWithUncertaintyText = makePrimaryMetricWithUncertaintyTooltipContent(
      primaryMetrics,
      unitAndMetricHelpers.primaryMetricUnitDetails
    );

    const primaryAltMetrics = {
      primaryAltMetric: bySubtypeBySummaryTypeKeyMetricLookups['primaryAltMetrics']?.[key],
      uncertaintyAltMetric: bySubtypeBySummaryTypeKeyMetricLookups['uncertaintyAltMetrics']?.[key],
    };

    const primaryAltMetricWithUncertaintyText = makePrimaryAltMetricWithUncertaintyTooltipContent(
      primaryAltMetrics,
      unitAndMetricHelpers.primaryMetricUnitDetails,
      EXCLUDING_NOTAPPLICABLE_TEXT // TODO: This is very specific to cover crop metrics.
      // Should there be time in the future we should allow developers to compose these helpers per kpi
    );

    const secondaryTypeMetrics = {
      secondaryMetric: bySubtypeBySummaryTypeKeyMetricLookups['secondaryMetrics']?.[key],
      secondaryUncertaintyMetric:
        bySubtypeBySummaryTypeKeyMetricLookups['secondaryUncertaintyMetrics']?.[key],
      trackedMetric: bySubtypeBySummaryTypeKeyMetricLookups['trackedMetrics']?.[key],
      unknownMetric: bySubtypeBySummaryTypeKeyMetricLookups['unknownMetrics']?.[key],
      notApplicableMetric: bySubtypeBySummaryTypeKeyMetricLookups['notApplicableMetrics']?.[key], // TODO: This is very specific to cover crop metrics.
      // Should there be time in the future we should allow developers to compose these helpers per kpi
    };

    const secondaryMetricTypeContent = !isUndefined(unitAndMetricHelpers.secondaryMetricUnitDetails)
      ? makeSecondaryMetricTypeTooltipContent(
          secondaryTypeMetrics,
          unitAndMetricHelpers.secondaryMetricUnitDetails
        )
      : {};

    const tierText = makeTierMetricTypeTooltipContent({
      tierMetric: bySubtypeBySummaryTypeKeyMetricLookups['tierMetrics']?.[key],
    });

    return {
      primaryMetricWithUncertaintyText,
      primaryAltMetricWithUncertaintyText,
      ...secondaryMetricTypeContent,
      tierText,
    };
  });

export const makePrimaryMetricWithUncertaintyTooltipContent = <U extends UnitType, U1 extends U>(
  metrics: {
    primaryMetric?: ValueWithFormatAndUnit<U1> | null;
    uncertaintyMetric?: ValueWithFormatAndUnit<U1> | null;
  },
  unitDetail: UnitDetail<U>
) => {
  const {primaryMetric, uncertaintyMetric} = metrics;
  const unitText = isNonEmptyString(unitDetail.unitName.abbr) ? ` ${unitDetail.unitName.abbr}` : '';

  let primaryText: string;

  if (isNil(primaryMetric)) {
    primaryText = EMPTY_DATA_CELL_VALUE;
  } else {
    primaryText = `${primaryMetric.formattedValue}${unitText}`;
    if (isDefined(uncertaintyMetric)) {
      primaryText = `${primaryText} ${PLUS_OR_MINUS} ${uncertaintyMetric.formattedValue}`;
    }
  }

  return primaryText;
};

export const makePrimaryAltMetricWithUncertaintyTooltipContent = <U extends UnitType, U1 extends U>(
  metrics: {
    primaryAltMetric?: ValueWithFormatAndUnit<U1> | null;
    uncertaintyAltMetric?: ValueWithFormatAndUnit<U1> | null;
  },
  unitDetail: UnitDetail<U>,
  optionalText?: string
) => {
  const {primaryAltMetric, uncertaintyAltMetric} = metrics;

  if (isUndefined(primaryAltMetric)) return undefined;

  const unitText = isNonEmptyString(unitDetail.unitName.abbr) ? ` ${unitDetail.unitName.abbr}` : '';

  let primaryAltText: string;

  if (isNil(primaryAltMetric)) {
    primaryAltText = EMPTY_DATA_CELL_VALUE;
  } else {
    primaryAltText = `${primaryAltMetric.formattedValue}${unitText}`;
    if (isDefined(uncertaintyAltMetric)) {
      primaryAltText = `${primaryAltText} ${PLUS_OR_MINUS} ${uncertaintyAltMetric.formattedValue}`;
    }
  }

  if (isNonEmptyString(optionalText)) {
    primaryAltText = `${primaryAltText} (${optionalText})`;
  }

  return primaryAltText;
};

export const makeTierMetricTypeTooltipContent = <U extends UnitType, U3 extends U>(metrics: {
  tierMetric?: ValueWithFormatAndUnit<U3> | null;
}) => {
  const {tierMetric} = metrics;
  if (isNil(tierMetric)) return undefined;

  return `${capitalizeFirstLetter(QUANTIFICATION_LEVEL_LABEL)}: ${capitalizeFirstLetter(
    tierMetric.formattedValue
  )}`;
};

export const makeSecondaryMetricTypeTooltipContent = <U extends UnitType, U2 extends U>(
  metrics: {
    secondaryMetric?: ValueWithFormatAndUnit<U2> | null;
    secondaryUncertaintyMetric?: ValueWithFormatAndUnit<U2> | null;
    trackedMetric?: ValueWithFormatAndUnit<U2> | null;
    unknownMetric?: ValueWithFormatAndUnit<U2> | null;
    notApplicableMetric?: ValueWithFormatAndUnit<U2> | null;
  },
  unitDetail: UnitDetail<U>
): Omit<MetricTooltipContent, 'primaryMetricWithUncertaintyText'> => {
  const {
    secondaryMetric,
    secondaryUncertaintyMetric,
    trackedMetric,
    unknownMetric,
    notApplicableMetric,
  } = metrics;

  let secondaryText: string | undefined,
    trackedText: string | undefined,
    unknownText: string | undefined,
    notApplicableText: string | undefined;

  const unitText = isNonEmptyString(unitDetail.unitName.abbr) ? ` ${unitDetail.unitName.abbr}` : '';

  if (!isUndefined(secondaryMetric)) {
    if (isNull(secondaryMetric)) {
      secondaryText = EMPTY_DATA_CELL_VALUE;
    } else {
      secondaryText = `${secondaryMetric.formattedValue}${unitText}`;
      if (isDefined(secondaryUncertaintyMetric)) {
        secondaryText = `${secondaryText} ${PLUS_OR_MINUS} ${secondaryUncertaintyMetric.formattedValue}`;
      }
    }
  }

  if (isDefined(trackedMetric)) {
    trackedText = `${trackedMetric.formattedValue}`;
    if (isDefined(unknownMetric)) {
      trackedText = `${trackedText} ${CROPLAND_SATELLITE_VERIFIED_TEXT}${unitText}`;
    } else {
      trackedText = `${trackedText}${unitText}`;
    }
  }

  if (isDefined(secondaryMetric) && isDefined(trackedMetric)) {
    secondaryText = `${secondaryText} / ${trackedText}`;
  }

  if (isDefined(unknownMetric)) {
    unknownText = `${unknownMetric.formattedValue} ${CROPLAND_SATELLITE_UNVERIFIED_TEXT}${unitText}`;
  }

  if (isDefined(notApplicableMetric)) {
    notApplicableText = `${notApplicableMetric.formattedValue} ${CROPLAND_NOTAPPLICABLE_TEXT}${unitText}`;
  }

  return {
    secondaryText,
    unknownText,
    notApplicableText,
  };
};
