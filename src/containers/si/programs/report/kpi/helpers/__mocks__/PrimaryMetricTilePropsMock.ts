import {MeasurementEnum} from '_common/utils/measurement-unit-options';

import {
  CO2E,
  CROPLAND_NOTAPPLICABLE_TEXT,
  CROPLAND_SATELLITE_UNVERIFIED_TEXT,
  CROPLAND_SATELLITE_VERIFIED_TEXT,
  PLUS_OR_MINUS,
} from 'containers/si/constants';
import {COVERCROPMETRICS_UNIT_MAP} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.cover_crop.transformation';
import {DROUGHT_METRICS_UNIT_MAP} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.drought_index.transformation';
import {type PrimaryMetricTileProps} from 'containers/si/programs/report/kpi/components/PrimaryMetricTile';
import {COMPARISON_METRIC_TEXT} from 'containers/si/programs/report/kpi/constants';
import {covercropUnitAndMetricHelpersMock} from 'containers/si/programs/report/kpi/helpers/__mocks__/unitAndMetricHelpersMock';

export const droughtPrimaryMetricTilePropsMock: PrimaryMetricTileProps = {
  primaryMetric: {
    value: 2,
    unit: 'drought-index',
    formattedValue: 'D0',
  },
  primaryMetricUnitDetails: DROUGHT_METRICS_UNIT_MAP.droughtIndex(),
  comparisonMetric1: {
    value: 1,
    unit: 'drought-index',
    formattedValue: '1',
  },
  comparisonMetric1Text: COMPARISON_METRIC_TEXT.PREVIOUS_YEAR,
  comparisonMetric2: null,
  comparisonMetric2Text: COMPARISON_METRIC_TEXT.YOY_AVERAGE,
  isDownwardChangePositive: false,
  tooltip: {primaryText: 'D0 classification', subText: ['Source: Field-based']},
};

export const socPrimaryMetricTilePropsMock: PrimaryMetricTileProps = {
  primaryMetric: {
    value: 0.38659931759636,
    unit: 'mt/ha',
    formattedValue: '0.387',
  },
  primaryMetricUnitDetails: {
    unit: 'mt/ha',
    unitName: {
      singular: `metric tonne ${CO2E} / hectare`,
      plural: `metric tonnes ${CO2E} / hectare`,
      abbr: `mt ${CO2E} / ha`,
    },
  },
  comparisonMetric1: {
    value: -0.2202563662163622,
    unit: 'mt/ha',
    formattedValue: '0.22',
  },
  comparisonMetric1Text: COMPARISON_METRIC_TEXT.PREVIOUS_YEAR,
  comparisonMetric2: {
    value: -0.05698334384475073,
    unit: 'mt/ha',
    formattedValue: '0.057',
  },
  comparisonMetric2Text: COMPARISON_METRIC_TEXT.YOY_AVERAGE,
  isDownwardChangePositive: false,
  primaryMetricUncertainty: {
    value: 0.0038659931759636,
    unit: 'mt/ha',
    formattedValue: '0.00387',
  },
};

export const coverCropPrimaryMetricTilePropsMock: PrimaryMetricTileProps = {
  primaryMetric: {
    value: 0.04719789963493147,
    unit: 'unit-interval' as const,
    formattedValue: '4.7%',
  },
  primaryMetricUncertainty: {
    value: 0.003471978996349314,
    unit: 'unit-interval' as const,
    formattedValue: '0.3%',
  },
  primaryMetricUnitDetails: covercropUnitAndMetricHelpersMock.primaryMetricUnitDetails,
  comparisonMetric1: {
    value: 0.0010296308886114588,
    unit: 'unit-interval' as const,
    formattedValue: '0.1%',
  },
  comparisonMetric1Text: COMPARISON_METRIC_TEXT.PREVIOUS_YEAR,
  comparisonMetric2: {
    value: -0.00012118842266662516,
    unit: 'unit-interval' as const,
    formattedValue: '0.01%',
  },
  comparisonMetric2Text: COMPARISON_METRIC_TEXT.YOY_AVERAGE,
  isDownwardChangePositive: false,
  subText: `7M ac ${PLUS_OR_MINUS} 90.8K / 148M ${CROPLAND_SATELLITE_VERIFIED_TEXT} ac`,
  tooltip: {
    primaryText: `4.7% adoption ${PLUS_OR_MINUS} 0.3%`,
    subText: [
      `7M ac ${PLUS_OR_MINUS} 90.8K / 148M ${CROPLAND_SATELLITE_VERIFIED_TEXT} ac`,
      `2.4M ${CROPLAND_SATELLITE_UNVERIFIED_TEXT} ac`,
      `100k ${CROPLAND_NOTAPPLICABLE_TEXT} ac`,
    ],
  },
};

export const coverCropyear2020PrimaryMetricTilePropsMock: PrimaryMetricTileProps = {
  comparisonMetric1: null,
  comparisonMetric1Text: COMPARISON_METRIC_TEXT.PREVIOUS_YEAR,
  comparisonMetric2: {
    formattedValue: '0.1%',
    unit: 'unit-interval' as const,
    value: 0.001272007733944723,
  },
  comparisonMetric2Text: COMPARISON_METRIC_TEXT.YOY_AVERAGE,
  isDownwardChangePositive: false,
  primaryMetric: {
    formattedValue: '4.6%',
    unit: 'unit-interval' as const,
    value: 0.04580470347832012,
  },
  primaryMetricUncertainty: {
    formattedValue: '0.1%',
    unit: 'unit-interval',
    value: 0.001458047034783201,
  },
  primaryMetricUnitDetails: covercropUnitAndMetricHelpersMock.primaryMetricUnitDetails,
  subText: `6.8M ac ${PLUS_OR_MINUS} 33.4K / 149M ${CROPLAND_SATELLITE_VERIFIED_TEXT} ac`,
  tooltip: {
    primaryText: `4.6% adoption ${PLUS_OR_MINUS} 0.1%`,
    subText: [
      `6.8M ac ${PLUS_OR_MINUS} 33.4K / 149M ${CROPLAND_SATELLITE_VERIFIED_TEXT} ac`,
      `0 ${CROPLAND_SATELLITE_UNVERIFIED_TEXT} ac`,
      `1 ${CROPLAND_NOTAPPLICABLE_TEXT} ac`,
    ],
  },
};

export const coverCropEmptyPrimaryMetricTilePropsMock: PrimaryMetricTileProps = {
  comparisonMetric1: null,
  comparisonMetric1Text: COMPARISON_METRIC_TEXT.PREVIOUS_YEAR,
  comparisonMetric2: null,
  comparisonMetric2Text: COMPARISON_METRIC_TEXT.YOY_AVERAGE,
  isDownwardChangePositive: false,
  primaryMetric: null,
  primaryMetricUnitDetails: COVERCROPMETRICS_UNIT_MAP.adoption(MeasurementEnum.MetricUnits),
};

export const coverCropYear2022PrimaryMetricTilePropsMock: PrimaryMetricTileProps = {
  primaryMetric: {
    value: 0.05081105903493605,
    unit: 'unit-interval',
    formattedValue: '5.1%',
  },
  primaryMetricUncertainty: undefined,
  primaryMetricUnitDetails: COVERCROPMETRICS_UNIT_MAP.adoption(MeasurementEnum.MetricUnits),
  comparisonMetric1: {
    value: -0.0030139935257818204,
    unit: 'unit-interval',
    formattedValue: '0.3%',
  },
  comparisonMetric1Text: COMPARISON_METRIC_TEXT.PREVIOUS_YEAR,
  comparisonMetric2: {
    value: -0.010339112524386872,
    unit: 'unit-interval',
    formattedValue: '1%',
  },
  comparisonMetric2Text: COMPARISON_METRIC_TEXT.YOY_AVERAGE,
  isDownwardChangePositive: false,
  subText: `859K ha / 17M ${CROPLAND_SATELLITE_VERIFIED_TEXT} ha`,
  tooltip: {
    primaryText: '5.1% adoption',
    subText: [
      `859K ha / 17M ${CROPLAND_SATELLITE_VERIFIED_TEXT} ha`,
      `37K ${CROPLAND_SATELLITE_UNVERIFIED_TEXT} ha`,
      `676K ${CROPLAND_NOTAPPLICABLE_TEXT} ha`,
    ],
  },
};
