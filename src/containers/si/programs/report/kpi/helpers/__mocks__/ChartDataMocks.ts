import {MeasurementEnum} from '_common/utils/measurement-unit-options';

import {
  CO2E,
  CROPLAND_NOTAPPLICABLE_TEXT,
  CROPLAND_SATELLITE_UNVERIFIED_TEXT,
  CROPLAND_SATELLITE_VERIFIED_TEXT,
  PLUS_OR_MINUS,
} from 'containers/si/constants';
import {
  COVERCROPMETRICS_FORMATTER_MAP,
  COVERCROPMET<PERSON>CS_UNIT_MAP,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.cover_crop.transformation';
import {
  DROUGHT_METRICS_FORMATTER_MAP,
  DROUGHT_METRICS_UNIT_MAP,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.drought_index.transformation';
import {
  SOCMETRICS_FORMATTER_MAP,
  SOCMETRICS_UNIT_MAP,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.soc_kg_per_m2.transformation';
import type {ChartData, SummaryTypeChartProps} from 'containers/si/programs/report/kpi/types';

export const droughtTimeTrendChartDataMock: SummaryTypeChartProps = {
  data: [3, 2],
  formatter: DROUGHT_METRICS_FORMATTER_MAP.droughtIndex,
  labels: ['2021', '2022'],
  tooltipText: [
    {
      primaryMetricWithUncertaintyText: 'D1 classification',
      tierText: 'Source: Global',
    },
    {
      primaryMetricWithUncertaintyText: 'D0 classification',
      tierText: 'Source: Field-based',
    },
  ],
  unitDetail: DROUGHT_METRICS_UNIT_MAP.droughtIndex(),
};

export const socTimeTrendChartDataMock: SummaryTypeChartProps = {
  labels: ['2021', '2022', '2023'],
  data: [0.43590565227846995, 0.16634295137999783, 0.38659931759636],
  formatter: SOCMETRICS_FORMATTER_MAP.dSocMassPerArea,
  unitDetail: SOCMETRICS_UNIT_MAP.dSocMassPerArea(MeasurementEnum.MetricUnits),
  tooltipText: [
    {primaryMetricWithUncertaintyText: `0.436 mt ${CO2E} / ha ${PLUS_OR_MINUS} 0.00436`},
    {primaryMetricWithUncertaintyText: `0.166 mt ${CO2E} / ha ${PLUS_OR_MINUS} 0.00166`},
    {primaryMetricWithUncertaintyText: `0.387 mt ${CO2E} / ha ${PLUS_OR_MINUS} 0.00387`},
  ],
};

export const coverCropTimeTrendChartDataMock: SummaryTypeChartProps = {
  labels: ['2020', '2021', '2022'],
  data: [0.04580470347832012, 0.04822753052354293, 0.04719789963493147],
  formatter: COVERCROPMETRICS_FORMATTER_MAP.adoption,
  unitDetail: COVERCROPMETRICS_UNIT_MAP.adoption(MeasurementEnum.MetricUnits),
  tooltipText: [
    {
      primaryMetricWithUncertaintyText: `4.6% adoption ${PLUS_OR_MINUS} 0.1%`,
      secondaryText: `6.8M ac ${PLUS_OR_MINUS} 33.4K / 149M ${CROPLAND_SATELLITE_VERIFIED_TEXT} ac`,
      unknownText: `0 ${CROPLAND_SATELLITE_UNVERIFIED_TEXT} ac`,
      notApplicableText: `1 ${CROPLAND_NOTAPPLICABLE_TEXT} ac`,
    },
    {
      primaryMetricWithUncertaintyText: `4.8% adoption ${PLUS_OR_MINUS} 0.2%`,
      secondaryText: `7.4M ac ${PLUS_OR_MINUS} 68.7K / 153M ${CROPLAND_SATELLITE_VERIFIED_TEXT} ac`,
      unknownText: `0 ${CROPLAND_SATELLITE_UNVERIFIED_TEXT} ac`,
      notApplicableText: `0 ${CROPLAND_NOTAPPLICABLE_TEXT} ac`,
    },
    {
      primaryMetricWithUncertaintyText: `4.7% adoption ${PLUS_OR_MINUS} 0.3%`,
      secondaryText: `7M ac ${PLUS_OR_MINUS} 90.8K / 148M ${CROPLAND_SATELLITE_VERIFIED_TEXT} ac`,
      unknownText: `2.4M ${CROPLAND_SATELLITE_UNVERIFIED_TEXT} ac`,
      notApplicableText: `100k ${CROPLAND_NOTAPPLICABLE_TEXT} ac`,
    },
  ],
};

export const coverCropSubregionSummaryTypeChartDataMock: SummaryTypeChartProps = {
  labels: ['Great Lakes/Corn Belt', 'Mountain West', 'Northern Plains', 'Southern Plains'],
  data: [null, 0.020068287192039458, null, 0.05331932281914219],
  formatter: COVERCROPMETRICS_FORMATTER_MAP.adoption,
  unitDetail: COVERCROPMETRICS_UNIT_MAP.adoption(MeasurementEnum.MetricUnits),
  tooltipText: [
    {
      primaryMetricWithUncertaintyText: '--',
      secondaryText: undefined,
      unknownText: undefined,
      notApplicableText: undefined,
    },
    {
      primaryMetricWithUncertaintyText: '2% adoption',
      secondaryText: `18K ha / 916K ${CROPLAND_SATELLITE_VERIFIED_TEXT} ha`,
      unknownText: `37K ${CROPLAND_SATELLITE_UNVERIFIED_TEXT} ha`,
      notApplicableText: `676K ${CROPLAND_NOTAPPLICABLE_TEXT} ha`,
    },
    {
      primaryMetricWithUncertaintyText: '--',
      secondaryText: undefined,
      unknownText: undefined,
      notApplicableText: undefined,
    },
    {
      primaryMetricWithUncertaintyText: '5.3% adoption',
      secondaryText: `670K ha / 13M ${CROPLAND_SATELLITE_VERIFIED_TEXT} ha`,
      unknownText: `0 ${CROPLAND_SATELLITE_UNVERIFIED_TEXT} ha`,
      notApplicableText: `0 ${CROPLAND_NOTAPPLICABLE_TEXT} ha`,
    },
  ],
};

export const covercropCropSummaryTypeChartDataMock: SummaryTypeChartProps = {
  labels: ['Corn', 'Soybeans', 'Wheat, Spring', 'Wheat, Winter', 'Sugar Beets', 'Oats'],
  data: [
    0.05852085808761367,
    0.10065828728294149,
    0.020068287192039458,
    0.0053744449119416254,
    null,
    null,
  ],
  formatter: COVERCROPMETRICS_FORMATTER_MAP.adoption,
  unitDetail: COVERCROPMETRICS_UNIT_MAP.adoption(MeasurementEnum.MetricUnits),
  tooltipText: [
    {
      primaryMetricWithUncertaintyText: '5.9% adoption',
      secondaryText: `434K ha / 7.4M ${CROPLAND_SATELLITE_VERIFIED_TEXT} ha`,
      unknownText: `47 ${CROPLAND_SATELLITE_UNVERIFIED_TEXT} ha`,
      notApplicableText: `0 ${CROPLAND_NOTAPPLICABLE_TEXT} ha`,
    },
    {
      primaryMetricWithUncertaintyText: '10.1% adoption',
      secondaryText: `381K ha / 3.8M ${CROPLAND_SATELLITE_VERIFIED_TEXT} ha`,
      unknownText: `0 ${CROPLAND_SATELLITE_UNVERIFIED_TEXT} ha`,
      notApplicableText: `0 ${CROPLAND_NOTAPPLICABLE_TEXT} ha`,
    },
    {
      primaryMetricWithUncertaintyText: '2% adoption',
      secondaryText: `18K ha / 916K ${CROPLAND_SATELLITE_VERIFIED_TEXT} ha`,
      unknownText: `37K ${CROPLAND_SATELLITE_UNVERIFIED_TEXT} ha`,
      notApplicableText: `0 ${CROPLAND_NOTAPPLICABLE_TEXT} ha`,
    },
    {
      primaryMetricWithUncertaintyText: '0.5% adoption',
      secondaryText: `26K ha / 4.8M ${CROPLAND_SATELLITE_VERIFIED_TEXT} ha`,
      unknownText: `0 ${CROPLAND_SATELLITE_UNVERIFIED_TEXT} ha`,
      notApplicableText: `676K ${CROPLAND_NOTAPPLICABLE_TEXT} ha`,
    },
    {
      primaryMetricWithUncertaintyText: '--',
      secondaryText: undefined,
      unknownText: undefined,
      notApplicableText: undefined,
    },
    {
      primaryMetricWithUncertaintyText: '--',
      secondaryText: undefined,
      unknownText: undefined,
      notApplicableText: undefined,
    },
  ],
};

export const LONG_SUMMARY_TYPE_CHART_DATA_MOCK: ChartData = {
  data: [
    ...covercropCropSummaryTypeChartDataMock.data,
    ...covercropCropSummaryTypeChartDataMock.data,
  ],
  labels: [
    ...covercropCropSummaryTypeChartDataMock.labels,
    ...covercropCropSummaryTypeChartDataMock.labels,
  ],
  tooltipText: [
    ...covercropCropSummaryTypeChartDataMock.tooltipText!,
    ...covercropCropSummaryTypeChartDataMock.tooltipText!,
  ],
};
