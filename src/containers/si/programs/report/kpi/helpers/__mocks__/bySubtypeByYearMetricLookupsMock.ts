import type {BySubtypeBySummaryTypeKeyMetricLookups} from 'containers/si/programs/report/kpi/types';

export const droughtBySubtypeByYearMetricLookupsMock: BySubtypeBySummaryTypeKeyMetricLookups = {
  primaryMetrics: {
    '2021': {
      value: 3,
      unit: 'drought-index',
      formattedValue: 'D1',
    },
    '2022': {
      value: 2,
      unit: 'drought-index',
      formattedValue: 'D0',
    },
  },
  tierMetrics: {
    '2021': {
      value: 3,
      unit: 'metric-tier',
      formattedValue: 'global',
    },
    '2022': {
      value: 3,
      unit: 'metric-tier',
      formattedValue: 'field-based',
    },
  },
};

export const socBySubtypeByYearMetricLookupsMock: BySubtypeBySummaryTypeKeyMetricLookups = {
  primaryMetrics: {
    '2021': {
      value: 0.43590565227846995,
      unit: 'mt/ha',
      formattedValue: '0.436',
    },
    '2022': {
      value: 0.16634295137999783,
      unit: 'mt/ha',
      formattedValue: '0.166',
    },
    '2023': {
      value: 0.38659931759636,
      unit: 'mt/ha',
      formattedValue: '0.387',
    },
  },
  uncertaintyMetrics: {
    '2021': {
      value: 0.004359056522784699,
      unit: 'mt/ha',
      formattedValue: '0.00436',
    },
    '2022': {
      value: 0.0016634295137999784,
      unit: 'mt/ha',
      formattedValue: '0.00166',
    },
    '2023': {
      value: 0.0038659931759636,
      unit: 'mt/ha',
      formattedValue: '0.00387',
    },
  },
} as const;

export const allYearsCovercropByYearAdoptionMetricLookupsMock = {
  '2015': {
    value: 0.03055949369722523,
    unit: 'unit-interval',
    formattedValue: '3.1%',
  },
  '2016': {
    value: 0.029629570401500202,
    unit: 'unit-interval',
    formattedValue: '3%',
  },
  '2017': {
    value: 0.0418137221121136,
    unit: 'unit-interval',
    formattedValue: '4.2%',
  },
  '2018': {
    value: 0.038896990631425786,
    unit: 'unit-interval',
    formattedValue: '3.9%',
  },
  '2019': {
    value: 0.04452214333878025,
    unit: 'unit-interval',
    formattedValue: '4.5%',
  },
  '2020': {
    value: 0.039745527359258105,
    unit: 'unit-interval',
    formattedValue: '4%',
  },
  '2021': {
    value: 0.04779706550915423,
    unit: 'unit-interval',
    formattedValue: '4.8%',
  },
  '2022': {
    value: 0.05081105903493605,
    unit: 'unit-interval',
    formattedValue: '5.1%',
  },
  '2023': {
    value: 0.015941906970422373,
    unit: 'unit-interval',
    formattedValue: '1.6%',
  },
};

export const covercropBySubtypeByYearMetricLookupsMock: BySubtypeBySummaryTypeKeyMetricLookups = {
  primaryMetrics: {
    '2020': {
      value: 0.04580470347832012,
      unit: 'unit-interval' as const,
      formattedValue: '4.6%',
    },
    '2021': {
      value: 0.04822753052354293,
      unit: 'unit-interval' as const,
      formattedValue: '4.8%',
    },
    '2022': {
      value: 0.04719789963493147,
      unit: 'unit-interval' as const,
      formattedValue: '4.7%',
    },
  },
  uncertaintyMetrics: {
    '2020': {
      value: 0.001458047034783201,
      unit: 'unit-interval' as const,
      formattedValue: '0.1%',
    },
    '2021': {
      value: 0.0024822753052354293,
      unit: 'unit-interval' as const,
      formattedValue: '0.2%',
    },
    '2022': {
      value: 0.003471978996349314,
      unit: 'unit-interval' as const,
      formattedValue: '0.3%',
    },
  },
  secondaryMetrics: {
    '2020': {
      value: 6833426.632728987,
      unit: 'ac' as const,
      formattedValue: '6.8M',
    },
    '2021': {
      value: 7368662.622728093,
      unit: 'ac' as const,
      formattedValue: '7.4M',
    },
    '2022': {
      value: 6990840.660148549,
      unit: 'ac' as const,
      formattedValue: '7M',
    },
  },
  secondaryUncertaintyMetrics: {
    '2020': {
      value: 33426.632728987,
      unit: 'ac' as const,
      formattedValue: '33.4K',
    },
    '2021': {
      value: 68662.622728093,
      unit: 'ac' as const,
      formattedValue: '68.7K',
    },
    '2022': {
      value: 90840.660148549,
      unit: 'ac' as const,
      formattedValue: '90.8K',
    },
  },
  trackedMetrics: {
    '2020': {
      value: 149186134.03890553,
      unit: 'ac' as const,
      formattedValue: '149M',
    },
    '2021': {
      value: 152789548.6817634,
      unit: 'ac' as const,
      formattedValue: '153M',
    },
    '2022': {
      value: 148117622.05991435,
      unit: 'ac' as const,
      formattedValue: '148M',
    },
  },
  unknownMetrics: {
    '2020': {
      value: 0,
      unit: 'ac' as const,
      formattedValue: '0',
    },
    '2021': {
      value: 0,
      unit: 'ac' as const,
      formattedValue: '0',
    },
    '2022': {
      value: 2422492.9353497857,
      unit: 'ac' as const,
      formattedValue: '2.4M',
    },
  },
  notApplicableMetrics: {
    '2020': {
      value: 1,
      unit: 'ac' as const,
      formattedValue: '1',
    },
    '2021': {
      value: 0,
      unit: 'ac' as const,
      formattedValue: '0',
    },
    '2022': {
      value: 100_000,
      unit: 'ac' as const,
      formattedValue: '100k',
    },
  },
};

export const missingYearCovercropBySubtypeByYearMetricLookupsMock: BySubtypeBySummaryTypeKeyMetricLookups =
  {
    primaryMetrics: {
      ...covercropBySubtypeByYearMetricLookupsMock.primaryMetrics,
      '2021': null,
    },
    uncertaintyMetrics: {
      ...covercropBySubtypeByYearMetricLookupsMock.uncertaintyMetrics,
      '2021': null,
    },
    secondaryMetrics: {
      ...covercropBySubtypeByYearMetricLookupsMock.secondaryMetrics,
      '2021': null,
    },
    secondaryUncertaintyMetrics: {
      ...covercropBySubtypeByYearMetricLookupsMock.secondaryUncertaintyMetrics,
      '2021': null,
    },
    trackedMetrics: {
      ...covercropBySubtypeByYearMetricLookupsMock.trackedMetrics,
      '2021': null,
    },
    unknownMetrics: {
      ...covercropBySubtypeByYearMetricLookupsMock.unknownMetrics,
      '2021': {
        value: 1500000,
        unit: 'ac' as const,
        formattedValue: '1.5M',
      },
    },
    notApplicableMetrics: {
      ...covercropBySubtypeByYearMetricLookupsMock.notApplicableMetrics,
    },
  };

export const zeroRangeCovercropBySubtypeByYearMetricLookupsMock: BySubtypeBySummaryTypeKeyMetricLookups =
  {
    ...covercropBySubtypeByYearMetricLookupsMock,
    primaryMetrics: {
      '2020': {
        value: 0.000004580470347832012,
        unit: 'unit-interval' as const,
        formattedValue: '0.00046%',
      },
      '2021': {
        value: 0.000003580470347832012,
        unit: 'unit-interval' as const,
        formattedValue: '0.00036%',
      },
      '2022': {
        value: 0.000005580470347832012,
        unit: 'unit-interval' as const,
        formattedValue: '0.00056%',
      },
    },
  };
