import {MeasurementEnum} from '_common/utils/measurement-unit-options';

import {
  COVERCROPMETRICS_FORMATTER_MAP,
  COVERCROPMETRICS_UNIT_MAP,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.cover_crop.transformation';
import {
  DROUGHT_METRICS_FORMATTER_MAP,
  DROUGHT_METRICS_UNIT_MAP,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.drought_index.transformation';
import {
  SOCMETRICS_FORMATTER_MAP,
  SOCMETRICS_UNIT_MAP,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.soc_kg_per_m2.transformation';
import type {UnitAndMetricHelpers} from 'containers/si/programs/report/kpi/types';

export const droughtUnitAndMetricHelpersMock: UnitAndMetricHelpers = {
  primaryMetricUnitDetails: DROUGHT_METRICS_UNIT_MAP.droughtIndex(),
  primaryMetricFormatter: DROUGHT_METRICS_FORMATTER_MAP.droughtIndex,
};

export const covercropUnitAndMetricHelpersMock: UnitAndMetricHelpers = {
  primaryMetricUnitDetails: COVERCROPMETRICS_UNIT_MAP.adoption(MeasurementEnum.ImperialUnits),
  secondaryMetricUnitDetails: COVERCROPMETRICS_UNIT_MAP.covercroppedArea(
    MeasurementEnum.ImperialUnits
  ),
  primaryMetricFormatter: COVERCROPMETRICS_FORMATTER_MAP.adoption,
};

export const socUnitAndMetricHelpersMocks: UnitAndMetricHelpers = {
  primaryMetricUnitDetails: SOCMETRICS_UNIT_MAP.dSocMassPerArea(MeasurementEnum.MetricUnits),
  primaryMetricFormatter: SOCMETRICS_FORMATTER_MAP.dSocMassPerArea,
};
