import {MeasurementEnum} from '_common/utils/measurement-unit-options';

import {
  COVERCROPMETRICS_FORMATTER_MAP,
  COVERCROPMETRICS_UNIT_MAP,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.cover_crop.transformation';
import {type TimeTrendTileProps} from 'containers/si/programs/report/kpi/components/TimeTrendTile';

export const coverCropYear2022TimeTrendTilePropsMock: TimeTrendTileProps = {
  labels: ['2015', '2016', '2017', '2018', '2019', '2020', '2021', '2022'],
  data: [
    0.03055949369722523, 0.029629570401500202, 0.0418137221121136, 0.038896990631425786,
    0.04452214333878025, 0.039745527359258105, 0.04779706550915423, 0.05081105903493605,
  ],
  formatter: COVERCROPMETRICS_FORMATTER_MAP.adoption,
  unitDetail: COVERCROPMETRICS_UNIT_MAP.adoption(MeasurementEnum.MetricUnits),
  tooltipText: [
    {
      primaryMetricWithUncertaintyText: '3.1% adoption',
      secondaryText: '522K ha / 17M verified ha',
      unknownText: '76K unverified ha',
      notApplicableText: '1.2M not applicable ha',
    },
    {
      primaryMetricWithUncertaintyText: '3% adoption',
      secondaryText: '528K ha / 18M verified ha',
      unknownText: '70K unverified ha',
      notApplicableText: '1.2M not applicable ha',
    },
    {
      primaryMetricWithUncertaintyText: '4.2% adoption',
      secondaryText: '765K ha / 18M verified ha',
      unknownText: '93K unverified ha',
      notApplicableText: '1M not applicable ha',
    },
    {
      primaryMetricWithUncertaintyText: '3.9% adoption',
      secondaryText: '713K ha / 18M verified ha',
      unknownText: '51K unverified ha',
      notApplicableText: '750K not applicable ha',
    },
    {
      primaryMetricWithUncertaintyText: '4.5% adoption',
      secondaryText: '787K ha / 18M verified ha',
      unknownText: '44K unverified ha',
      notApplicableText: '419K not applicable ha',
    },
    {
      primaryMetricWithUncertaintyText: '4% adoption',
      secondaryText: '729K ha / 18M verified ha',
      unknownText: '64K unverified ha',
      notApplicableText: '1.1M not applicable ha',
    },
    {
      primaryMetricWithUncertaintyText: '4.8% adoption',
      secondaryText: '886K ha / 19M verified ha',
      unknownText: '37K unverified ha',
      notApplicableText: '614K not applicable ha',
    },
    {
      primaryMetricWithUncertaintyText: '5.1% adoption',
      secondaryText: '859K ha / 17M verified ha',
      unknownText: '37K unverified ha',
      notApplicableText: '676K not applicable ha',
    },
  ],
  activePointIndex: 7,
  beginYAxisAtZero: true,
  hideAverageLine: false,
};
