import {getTypedValues} from '_common/utils/object';
import {isNil, isUndefined} from '_common/utils/typeGuards';

import {makeSummaryTypeTooltipContent} from 'containers/si/programs/report/kpi/helpers/makeSummaryTypeTooltipContent';
import type {
  BySubtypeBySummaryTypeKeyMetricLookups,
  SummaryTypeChartProps,
  SummaryTypeLookupPairs,
  UnitAndMetricHelpers,
} from 'containers/si/programs/report/kpi/types';

export type MakeSummaryTypeChartContentArgs = {
  bySubtypeBySummaryTypeKeyMetricLookups: BySubtypeBySummaryTypeKeyMetricLookups;
  unitAndMetricHelpers: UnitAndMetricHelpers;
  nameToIdLookupPairs: SummaryTypeLookupPairs;
};

export const makeSummaryTypeChartContent = (
  args: MakeSummaryTypeChartContentArgs | null
): SummaryTypeChartProps | null => {
  if (isNil(args)) return null; // TODO: remove | null and manage upstream
  const {bySubtypeBySummaryTypeKeyMetricLookups, unitAndMetricHelpers, nameToIdLookupPairs} = args;

  const labels = nameToIdLookupPairs.map(([name, _summaryTypeKey]) => name);
  const summaryTypeKeys = nameToIdLookupPairs.map(([_name, summaryTypeKey]) => summaryTypeKey);

  const data = summaryTypeKeys.map(
    summaryTypeKey =>
      bySubtypeBySummaryTypeKeyMetricLookups.primaryMetrics[summaryTypeKey]?.value ?? null
  );
  const unitDetail = unitAndMetricHelpers.primaryMetricUnitDetails;
  const formatter = unitAndMetricHelpers.primaryMetricFormatter;

  const tooltipText = getTypedValues(bySubtypeBySummaryTypeKeyMetricLookups).some(
    metricLookup => !isUndefined(metricLookup)
  )
    ? makeSummaryTypeTooltipContent({
        summaryTypeKeys,
        bySubtypeBySummaryTypeKeyMetricLookups,
        unitAndMetricHelpers,
      })
    : undefined;

  return {
    formatter,
    labels,
    data,
    unitDetail,
    tooltipText,
  };
};
