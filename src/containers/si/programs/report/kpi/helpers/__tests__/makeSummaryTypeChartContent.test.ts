import {MeasurementEnum} from '_common/utils/measurement-unit-options';

import {
  CROPLAND_NOTAPPLICABLE_TEXT,
  CROPLAND_SATELLITE_UNVERIFIED_TEXT,
  EMPTY_DATA_CELL_VALUE,
} from 'containers/si/constants';
import {
  COVERCROPMETRICS_FORMATTER_MAP,
  COVERCROPMETRICS_UNIT_MAP,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.cover_crop.transformation';
import {
  covercropBySubtypeByYearMetricLookupsMock,
  droughtBySubtypeByYearMetricLookupsMock,
  missingYearCovercropBySubtypeByYearMetricLookupsMock,
} from 'containers/si/programs/report/kpi/helpers/__mocks__/bySubtypeByYearMetricLookupsMock';
import {
  coverCropTimeTrendChartDataMock,
  droughtTimeTrendChartDataMock,
} from 'containers/si/programs/report/kpi/helpers/__mocks__/ChartDataMocks';
import {droughtUnitAndMetricHelpersMock} from 'containers/si/programs/report/kpi/helpers/__mocks__/unitAndMetricHelpersMock';
import {
  makeSummaryTypeChartContent,
  type MakeSummaryTypeChartContentArgs,
} from 'containers/si/programs/report/kpi/helpers/makeSummaryTypeChartContent';
import {type MetricTooltipContent} from 'containers/si/programs/report/kpi/types';
import type {
  SummaryTypeChartProps,
  UnitAndMetricHelpers,
} from 'containers/si/programs/report/kpi/types';

const mockUserUnitsSystem = MeasurementEnum.ImperialUnits;
const primaryMetricUnitDetails = COVERCROPMETRICS_UNIT_MAP.adoption(mockUserUnitsSystem);
const secondaryMetricUnitDetails = COVERCROPMETRICS_UNIT_MAP.covercroppedArea(mockUserUnitsSystem);

const covercropUnitAndMetricHelpersMock: UnitAndMetricHelpers = {
  primaryMetricUnitDetails,
  secondaryMetricUnitDetails,
  primaryMetricFormatter: COVERCROPMETRICS_FORMATTER_MAP.adoption,
};

describe('makeSummaryTypeChartContent', () => {
  const coverCropDefaultProps: MakeSummaryTypeChartContentArgs = {
    nameToIdLookupPairs: [
      ['2020', '2020'],
      ['2021', '2021'],
      ['2022', '2022'],
    ],
    unitAndMetricHelpers: covercropUnitAndMetricHelpersMock,
    bySubtypeBySummaryTypeKeyMetricLookups: covercropBySubtypeByYearMetricLookupsMock,
  };

  it('should return summary type chart content when data is provided', () => {
    const result1 = makeSummaryTypeChartContent(coverCropDefaultProps);
    expect(result1).toEqual(coverCropTimeTrendChartDataMock);

    const result2 = makeSummaryTypeChartContent({
      nameToIdLookupPairs: [
        ['2021', '2021'],
        ['2022', '2022'],
      ],
      unitAndMetricHelpers: droughtUnitAndMetricHelpersMock,
      bySubtypeBySummaryTypeKeyMetricLookups: droughtBySubtypeByYearMetricLookupsMock,
    });
    expect(result2).toEqual(droughtTimeTrendChartDataMock);
  });

  it('should return null when empty type look up or empty data lookup is provided', () => {
    const result = makeSummaryTypeChartContent(null);
    expect(result).toEqual(null);
  });

  it(`should return null and ${EMPTY_DATA_CELL_VALUE} when data is null for a defined type key`, () => {
    const props = {
      ...coverCropDefaultProps,
      bySubtypeBySummaryTypeKeyMetricLookups: missingYearCovercropBySubtypeByYearMetricLookupsMock,
    };

    const data: Array<number | null> = [...coverCropTimeTrendChartDataMock.data];
    data[1] = null;
    const tooltipText: Array<MetricTooltipContent> = [
      ...coverCropTimeTrendChartDataMock.tooltipText!,
    ];
    tooltipText[1] = {
      primaryMetricWithUncertaintyText: EMPTY_DATA_CELL_VALUE,
      secondaryText: EMPTY_DATA_CELL_VALUE,
      unknownText: `1.5M ${CROPLAND_SATELLITE_UNVERIFIED_TEXT} ac`,
      notApplicableText: `0 ${CROPLAND_NOTAPPLICABLE_TEXT} ac`,
    };
    const expectedOutput: SummaryTypeChartProps = {
      ...coverCropTimeTrendChartDataMock,
      data,
      tooltipText,
    };
    const result = makeSummaryTypeChartContent(props);
    expect(result).toEqual(expectedOutput);
  });

  it('should return undefined for tooltip keys when data is undefined for a defined type key', () => {
    const props = {
      ...coverCropDefaultProps,
      bySubtypeBySummaryTypeKeyMetricLookups: {
        primaryMetrics: coverCropDefaultProps.bySubtypeBySummaryTypeKeyMetricLookups.primaryMetrics,
      },
    };
    const expectedOutput: SummaryTypeChartProps = {
      ...coverCropTimeTrendChartDataMock,
      tooltipText: [
        {
          primaryMetricWithUncertaintyText: '4.6% adoption',
          notApplicableText: undefined,
          secondaryText: undefined,
          unknownText: undefined,
        },
        {
          primaryMetricWithUncertaintyText: '4.8% adoption',
          notApplicableText: undefined,
          secondaryText: undefined,
          unknownText: undefined,
        },
        {
          primaryMetricWithUncertaintyText: '4.7% adoption',
          notApplicableText: undefined,
          secondaryText: undefined,
          unknownText: undefined,
        },
      ],
    };
    const result = makeSummaryTypeChartContent(props);
    expect(result).toEqual(expectedOutput);
  });
});
