import {
  EMPTY_DATA_CELL_VALUE,
  EXCLUDING_NOTAPPLICABLE_TEXT,
  PLUS_OR_MINUS,
} from 'containers/si/constants';
import {
  covercropBySubtypeByYearMetricLookupsMock,
  droughtBySubtypeByYearMetricLookupsMock,
  socBySubtypeByYearMetricLookupsMock,
  zeroRangeCovercropBySubtypeByYearMetricLookupsMock,
} from 'containers/si/programs/report/kpi/helpers/__mocks__/bySubtypeByYearMetricLookupsMock';
import {
  coverCropEmptyPrimaryMetricTilePropsMock,
  coverCropPrimaryMetricTilePropsMock,
  coverCropyear2020PrimaryMetricTilePropsMock,
  droughtPrimaryMetricTilePropsMock,
  socPrimaryMetricTilePropsMock,
} from 'containers/si/programs/report/kpi/helpers/__mocks__/PrimaryMetricTilePropsMock';
import {
  covercropUnitAndMetricHelpersMock,
  droughtUnitAndMetricHelpersMock,
  socUnitAndMetricHelpersMocks,
} from 'containers/si/programs/report/kpi/helpers/__mocks__/unitAndMetricHelpersMock';
import {
  makePrimaryMetricTileContent,
  type MakePrimaryMetricTileContentArgs,
} from 'containers/si/programs/report/kpi/helpers/makePrimaryMetricTileContent';

describe('makePrimaryMetricTileContent', () => {
  const droughtDefaultProps: MakePrimaryMetricTileContentArgs = {
    selectedYear: 2022,
    unitAndMetricHelpers: droughtUnitAndMetricHelpersMock,
    bySubtypeByYearMetricLookups: droughtBySubtypeByYearMetricLookupsMock,
  };
  const socEmissionsDefaultProps: MakePrimaryMetricTileContentArgs = {
    selectedYear: 2023,
    unitAndMetricHelpers: socUnitAndMetricHelpersMocks,
    bySubtypeByYearMetricLookups: socBySubtypeByYearMetricLookupsMock,
  };
  const coverCropDefaultProps: MakePrimaryMetricTileContentArgs = {
    selectedYear: 2022,
    unitAndMetricHelpers: covercropUnitAndMetricHelpersMock,
    bySubtypeByYearMetricLookups: covercropBySubtypeByYearMetricLookupsMock,
  };

  it('should return primary metric with standard error content and comparison metric content when there is data for at least the current year and the previous year', () => {
    const result = makePrimaryMetricTileContent(socEmissionsDefaultProps);
    expect(result).toEqual(socPrimaryMetricTilePropsMock);

    const resultDownChangeIsPositive = makePrimaryMetricTileContent({
      ...socEmissionsDefaultProps,
      isDownwardChangePositive: true,
    });
    expect(resultDownChangeIsPositive).toEqual({
      ...socPrimaryMetricTilePropsMock,
      isDownwardChangePositive: true,
    });

    const resultCC = makePrimaryMetricTileContent(coverCropDefaultProps);
    expect(resultCC).toEqual(coverCropPrimaryMetricTilePropsMock);
  });

  it('should return primary metric content without standard error content when standard error is nil', () => {
    const result = makePrimaryMetricTileContent({
      ...coverCropDefaultProps,
      bySubtypeByYearMetricLookups: {
        ...covercropBySubtypeByYearMetricLookupsMock,
        uncertaintyMetrics: undefined,
      },
    });
    expect(result).toEqual({
      ...coverCropPrimaryMetricTilePropsMock,
      primaryMetricUncertainty: undefined,
      tooltip: {
        primaryText: '4.7% adoption',
        subText: [...coverCropPrimaryMetricTilePropsMock.tooltip!.subText!.slice(0)],
      },
    });
  });

  it('should return primary metric content with subtextTooltip when tier is provided', () => {
    const result = makePrimaryMetricTileContent(droughtDefaultProps);
    expect(result).toEqual(droughtPrimaryMetricTilePropsMock);
  });

  it('should return primary metric content and comparison metric content that is 0 when previous year and YoY average values are within zero range of selected year value', () => {
    const result = makePrimaryMetricTileContent({
      ...coverCropDefaultProps,
      bySubtypeByYearMetricLookups: zeroRangeCovercropBySubtypeByYearMetricLookupsMock,
    });
    expect(result).toEqual({
      ...coverCropPrimaryMetricTilePropsMock,
      comparisonMetric1: {formattedValue: '0%', unit: 'unit-interval', value: 0},
      comparisonMetric2: {formattedValue: '0%', unit: 'unit-interval', value: 0},
      primaryMetric: {
        formattedValue: '0.00056%',
        unit: 'unit-interval',
        value: 0.000005580470347832012,
      },
      tooltip: {
        primaryText: `0.00056% adoption ${PLUS_OR_MINUS} 0.3%`,
        subText: [...coverCropPrimaryMetricTilePropsMock.tooltip!.subText!.slice(0)],
      },
    });
  });

  it('should return no data content when args are empty', () => {
    const result = makePrimaryMetricTileContent({
      isDownwardChangePositive: coverCropDefaultProps.isDownwardChangePositive,
      unitAndMetricHelpers: coverCropDefaultProps.unitAndMetricHelpers,
    });
    expect(result).toEqual(coverCropEmptyPrimaryMetricTilePropsMock);
  });

  it('should return no data content when there is not data for the current year', () => {
    const result = makePrimaryMetricTileContent({
      ...coverCropDefaultProps,
      selectedYear: 2023,
    });
    expect(result).toEqual(coverCropEmptyPrimaryMetricTilePropsMock);
  });

  it('should return primary metric content and only average comparison metric content when there is no data for the comparison year', () => {
    const result = makePrimaryMetricTileContent({
      ...coverCropDefaultProps,
      selectedYear: 2020,
    });
    expect(result).toEqual(coverCropyear2020PrimaryMetricTilePropsMock);
  });

  it('should return primary metric content and only previous year comparison metric content when metric unit type is classification', () => {
    const result = makePrimaryMetricTileContent({
      ...droughtDefaultProps,
      selectedYear: 2022,
    });
    expect(result).toEqual(droughtPrimaryMetricTilePropsMock);
  });

  it('should return only primary metric content when there is only data for the current year', () => {
    const bySubtypeByYearMetricLookupsOverrides = {
      primaryMetrics: {
        [coverCropDefaultProps.selectedYear!]:
          coverCropDefaultProps.bySubtypeByYearMetricLookups!.primaryMetrics[
            coverCropDefaultProps.selectedYear!
          ] ?? null,
      },
      uncertaintyMetrics: {
        [coverCropDefaultProps.selectedYear!]:
          coverCropDefaultProps.bySubtypeByYearMetricLookups!.uncertaintyMetrics![
            coverCropDefaultProps.selectedYear!
          ] ?? null,
      },
      secondaryMetrics: {
        [coverCropDefaultProps.selectedYear!]:
          coverCropDefaultProps.bySubtypeByYearMetricLookups!.secondaryMetrics![
            coverCropDefaultProps.selectedYear!
          ] ?? null,
      },
      secondaryUncertaintyMetrics: {
        [coverCropDefaultProps.selectedYear!]:
          coverCropDefaultProps.bySubtypeByYearMetricLookups!.secondaryUncertaintyMetrics![
            coverCropDefaultProps.selectedYear!
          ] ?? null,
      },
      trackedMetrics: {
        [coverCropDefaultProps.selectedYear!]:
          coverCropDefaultProps.bySubtypeByYearMetricLookups!.trackedMetrics![
            coverCropDefaultProps.selectedYear!
          ] ?? null,
      },
      unknownMetrics: {
        [coverCropDefaultProps.selectedYear!]:
          coverCropDefaultProps.bySubtypeByYearMetricLookups!.unknownMetrics![
            coverCropDefaultProps.selectedYear!
          ] ?? null,
      },
      notApplicableMetrics: {
        [coverCropDefaultProps.selectedYear!]:
          coverCropDefaultProps.bySubtypeByYearMetricLookups!.notApplicableMetrics![
            coverCropDefaultProps.selectedYear!
          ] ?? null,
      },
    };

    const result = makePrimaryMetricTileContent({
      ...coverCropDefaultProps,
      bySubtypeByYearMetricLookups: bySubtypeByYearMetricLookupsOverrides,
    });
    expect(result).toEqual({
      ...coverCropPrimaryMetricTilePropsMock,
      comparisonMetric1: null,
      comparisonMetric2: null,
    });
  });

  it('should return tooltip without subtext when secondary metric data is not provided', () => {
    const bySubtypeByYearMetricLookupsOverrides = {
      ...coverCropDefaultProps.bySubtypeByYearMetricLookups!,
      secondaryMetrics: undefined,
      secondaryUncertaintyMetrics: undefined,
    };

    const result = makePrimaryMetricTileContent({
      ...coverCropDefaultProps,
      bySubtypeByYearMetricLookups: bySubtypeByYearMetricLookupsOverrides,
    });

    expect(result).toEqual({
      ...coverCropPrimaryMetricTilePropsMock,
      subText: undefined,
      tooltip: {
        ...coverCropPrimaryMetricTilePropsMock.tooltip,
        subText: [...coverCropPrimaryMetricTilePropsMock.tooltip!.subText!.toSpliced(0, 1)],
      },
    });
  });

  it(`should return ${EMPTY_DATA_CELL_VALUE} for subtext and subTextTooltip when secondary metric is provided but is null`, () => {
    const bySubtypeByYearMetricLookupsOverrides = {
      secondaryMetrics: {
        '2022': null,
      },
      secondaryUncertaintyMetrics: undefined,
    };

    const result = makePrimaryMetricTileContent({
      ...coverCropDefaultProps,
      bySubtypeByYearMetricLookups: {
        ...coverCropDefaultProps.bySubtypeByYearMetricLookups!,
        ...bySubtypeByYearMetricLookupsOverrides,
      },
    });

    expect(result).toEqual({
      ...coverCropPrimaryMetricTilePropsMock,
      subText: EMPTY_DATA_CELL_VALUE,
      tooltip: {
        ...coverCropPrimaryMetricTilePropsMock.tooltip,
        subText: [
          ...coverCropPrimaryMetricTilePropsMock.tooltip!.subText!.toSpliced(
            0,
            1,
            EMPTY_DATA_CELL_VALUE
          ),
        ],
      },
    });
  });

  it('should return a tooltip with primary metric alt text when primary alt metric data is provided', () => {
    const bySubtypeByYearMetricLookupsOverrides = {
      ...coverCropDefaultProps.bySubtypeByYearMetricLookups!,
      primaryAltMetrics: coverCropDefaultProps.bySubtypeByYearMetricLookups!['primaryMetrics'],
      uncertaintyAltMetrics:
        coverCropDefaultProps.bySubtypeByYearMetricLookups!['uncertaintyMetrics'],
    };

    const result = makePrimaryMetricTileContent({
      ...coverCropDefaultProps,
      bySubtypeByYearMetricLookups: bySubtypeByYearMetricLookupsOverrides,
    });

    expect(result).toEqual({
      ...coverCropPrimaryMetricTilePropsMock,
      tooltip: {
        ...coverCropPrimaryMetricTilePropsMock.tooltip,
        subText: [
          ...coverCropPrimaryMetricTilePropsMock.tooltip!.subText!.toSpliced(
            0,
            0,
            `${
              coverCropPrimaryMetricTilePropsMock.tooltip!.primaryText
            } (${EXCLUDING_NOTAPPLICABLE_TEXT})`
          ),
        ],
      },
    });
  });

  it(`should return a tooltip with primary metric alt text as ${EMPTY_DATA_CELL_VALUE} when primary alt metric data is null`, () => {
    const bySubtypeByYearMetricLookupsOverrides = {
      ...coverCropDefaultProps.bySubtypeByYearMetricLookups!,
      primaryAltMetrics: {
        ...coverCropDefaultProps.bySubtypeByYearMetricLookups!['primaryMetrics'],
        '2022': null,
      },
      uncertaintyAltMetrics:
        coverCropDefaultProps.bySubtypeByYearMetricLookups!['uncertaintyMetrics'],
    };

    const result = makePrimaryMetricTileContent({
      ...coverCropDefaultProps,
      bySubtypeByYearMetricLookups: bySubtypeByYearMetricLookupsOverrides,
    });

    expect(result).toEqual({
      ...coverCropPrimaryMetricTilePropsMock,
      tooltip: {
        ...coverCropPrimaryMetricTilePropsMock.tooltip,
        subText: [
          ...coverCropPrimaryMetricTilePropsMock.tooltip!.subText!.toSpliced(
            0,
            0,
            `${EMPTY_DATA_CELL_VALUE} (${EXCLUDING_NOTAPPLICABLE_TEXT})`
          ),
        ],
      },
    });
  });

  it('should not return a tooltip when primaryAlt, unknown, notApplicable or tier metrics are not provided', () => {
    const bySubtypeByYearMetricLookupsOverrides = {
      ...coverCropDefaultProps.bySubtypeByYearMetricLookups!,
      primaryAltMetrics: undefined,
      unknownMetrics: undefined,
      notApplicableMetrics: undefined,
    };

    const result = makePrimaryMetricTileContent({
      ...coverCropDefaultProps,
      bySubtypeByYearMetricLookups: bySubtypeByYearMetricLookupsOverrides,
    });

    expect(result).toEqual({
      ...coverCropPrimaryMetricTilePropsMock,
      subText: `7M ac ${PLUS_OR_MINUS} 90.8K / 148M ac`,
      tooltip: undefined,
    });
  });
});
