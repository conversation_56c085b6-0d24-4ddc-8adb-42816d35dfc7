import filter from 'lodash/filter';

import {getTypedValues} from '_common/utils/object';
import {isDefined, isNil, isNonEmptyArray, isUndefined} from '_common/utils/typeGuards';

import {
  EXCLUDING_NOTAPPLICABLE_TEXT,
  LARGE_NUMBER_THRESHOLD,
  ZERO_WITH_PRECISION_FOR_LARGE_NUMBERS,
  ZERO_WITH_PRECISION_FOR_SMALL_NUMBERS,
} from 'containers/si/constants';
import {
  isMetricZeroValueThenGetZeroMetricElseGetMetric,
  makeMetricDifference,
  makeMetricMean,
} from 'containers/si/programs/helpers/metric.helpers';
import {type PrimaryMetricTileProps} from 'containers/si/programs/report/kpi/components/PrimaryMetricTile';
import {COMPARISON_METRIC_TEXT} from 'containers/si/programs/report/kpi/constants';
import {
  makePrimaryAltMetricWithUncertaintyTooltipContent,
  makePrimaryMetricWithUncertaintyTooltipContent,
  makeSecondaryMetricTypeTooltipContent,
  makeTierMetricTypeTooltipContent,
} from 'containers/si/programs/report/kpi/helpers/makeSummaryTypeTooltipContent';
import type {
  BySubtypeBySummaryTypeKeyMetricLookups,
  UnitAndMetricHelpers,
} from 'containers/si/programs/report/kpi/types';
import {formatToNearestInteger} from 'containers/si/utils/formatters/number.format';
import {isClassificationUnit} from 'containers/si/utils/value.types';

export type MakePrimaryMetricTileContentArgs = {
  selectedYear?: number;
  unitAndMetricHelpers: UnitAndMetricHelpers;
  bySubtypeByYearMetricLookups?: BySubtypeBySummaryTypeKeyMetricLookups;
  isDownwardChangePositive?: boolean;
};

export const makePrimaryMetricTileContent = ({
  selectedYear: selectedYearNumber,
  unitAndMetricHelpers,
  bySubtypeByYearMetricLookups,
  isDownwardChangePositive,
}: MakePrimaryMetricTileContentArgs): PrimaryMetricTileProps => {
  const {primaryMetricUnitDetails, primaryMetricFormatter} = unitAndMetricHelpers;
  const defaultContent: PrimaryMetricTileProps = {
    primaryMetric: null,
    primaryMetricUnitDetails,
    comparisonMetric1: null,
    comparisonMetric1Text: COMPARISON_METRIC_TEXT.PREVIOUS_YEAR,
    comparisonMetric2: null,
    comparisonMetric2Text: COMPARISON_METRIC_TEXT.YOY_AVERAGE,
    isDownwardChangePositive: isDownwardChangePositive ?? false,
  };

  if (isNil(selectedYearNumber) || isNil(bySubtypeByYearMetricLookups)) return defaultContent;

  const previousYear = String(selectedYearNumber - 1);
  const selectedYear = String(selectedYearNumber);

  const selectedYearPrimaryMetric =
    bySubtypeByYearMetricLookups['primaryMetrics'][selectedYear] ?? null;

  const selectedYearUncertainty =
    bySubtypeByYearMetricLookups['uncertaintyMetrics']?.[selectedYear];

  const selectedYearPrimaryAltMetric = isDefined(bySubtypeByYearMetricLookups['primaryAltMetrics']) // undefined will not be rendered, null will be rendered as no data
    ? bySubtypeByYearMetricLookups['primaryAltMetrics'][selectedYear] ?? null
    : undefined;

  const selectedYearUncertaintyAlt =
    bySubtypeByYearMetricLookups['uncertaintyAltMetrics']?.[selectedYear];

  const selectedYearSecondaryMetric = isDefined(bySubtypeByYearMetricLookups['secondaryMetrics']) // undefined will not be rendered, null will be rendered as no data
    ? bySubtypeByYearMetricLookups['secondaryMetrics'][selectedYear] ?? null
    : undefined;

  const selectedYearSecondaryUncertaintyMetric =
    bySubtypeByYearMetricLookups['secondaryUncertaintyMetrics']?.[selectedYear]; // both null and undefined will not be rendered

  const selectedYearTrackedMetric = bySubtypeByYearMetricLookups['trackedMetrics']?.[selectedYear]; // both null and undefined will not be rendered

  const selectedYearUnknownMetric = bySubtypeByYearMetricLookups['unknownMetrics']?.[selectedYear]; // both null and undefined will not be rendered

  const selectedYearNotApplicableMetric =
    bySubtypeByYearMetricLookups['notApplicableMetrics']?.[selectedYear]; // both null and undefined will not be rendered

  const selectedYearTierMetric = bySubtypeByYearMetricLookups['tierMetrics']?.[selectedYear]; // both null and undefined will not be rendered

  const previousYearPrimaryMetric =
    bySubtypeByYearMetricLookups['primaryMetrics'][previousYear] ?? null;

  const nonNilPrimaryMetrics = filter(bySubtypeByYearMetricLookups['primaryMetrics'], isDefined);
  const meanYOYPrimaryMetric =
    nonNilPrimaryMetrics.length > 1 // ensure we have data beyond the current year
      ? makeMetricMean(nonNilPrimaryMetrics, primaryMetricUnitDetails.unit, primaryMetricFormatter)
      : null;

  if (isNil(selectedYearPrimaryMetric)) return defaultContent;

  const primaryMetrics = {
    primaryMetric: selectedYearPrimaryMetric,
    uncertaintyMetric: selectedYearUncertainty,
  };

  const primaryMetricWithUncertaintyText = makePrimaryMetricWithUncertaintyTooltipContent(
    primaryMetrics,
    primaryMetricUnitDetails
  );

  const primaryAltMetricWithUncertaintyText = makePrimaryAltMetricWithUncertaintyTooltipContent(
    {
      primaryAltMetric: selectedYearPrimaryAltMetric,
      uncertaintyAltMetric: selectedYearUncertaintyAlt,
    },
    primaryMetricUnitDetails,
    EXCLUDING_NOTAPPLICABLE_TEXT // TODO: This is very specific to cover crop metrics.
    // Should there be time in the future we should allow developers to compose these helpers per kpi
  );

  const secondaryTypeMetrics = {
    secondaryMetric: selectedYearSecondaryMetric,
    secondaryUncertaintyMetric: selectedYearSecondaryUncertaintyMetric,
    trackedMetric: selectedYearTrackedMetric,
    unknownMetric: selectedYearUnknownMetric,
    notApplicableMetric: selectedYearNotApplicableMetric, // TODO: This is very specific to cover crop metrics.
    // Should there be time in the future we should allow developers to compose these helpers per kpi
  };

  const {secondaryText, unknownText, notApplicableText} =
    getTypedValues(secondaryTypeMetrics).some(
      secondaryTypeMetric => !isUndefined(secondaryTypeMetric)
    ) && !isUndefined(unitAndMetricHelpers.secondaryMetricUnitDetails)
      ? makeSecondaryMetricTypeTooltipContent(
          secondaryTypeMetrics,
          unitAndMetricHelpers.secondaryMetricUnitDetails
        )
      : {};

  const tierText = makeTierMetricTypeTooltipContent({tierMetric: selectedYearTierMetric});

  // show tooltip if unexposed data values are set
  // primary and secondary values are exposed,
  // primaryAlt, unknown, notApplicable, tier are unexposed
  const tooltipSubtext = [
    primaryAltMetricWithUncertaintyText,
    unknownText,
    notApplicableText,
    tierText,
  ].some(isDefined)
    ? [
        primaryAltMetricWithUncertaintyText,
        secondaryText,
        unknownText,
        notApplicableText,
        tierText,
      ].filter(isDefined)
    : [];

  const tooltip =
    isDefined(primaryMetricWithUncertaintyText) && isNonEmptyArray(tooltipSubtext)
      ? {
          primaryText: primaryMetricWithUncertaintyText,
          subText: tooltipSubtext,
        }
      : undefined;

  const contentWithPrimaryMetric: PrimaryMetricTileProps = {
    ...defaultContent,
    primaryMetric: selectedYearPrimaryMetric,
    primaryMetricUncertainty: selectedYearUncertainty,
    subText: secondaryText,
    tooltip,
  };

  const primaryMetricDifferenceFormatter = isClassificationUnit(primaryMetricUnitDetails.unit)
    ? (v: number) => formatToNearestInteger(Math.abs(v))
    : (v: number) => primaryMetricFormatter(Math.abs(v));

  const comparisonMetric1 = isDefined(previousYearPrimaryMetric)
    ? makeMetricDifference(
        [previousYearPrimaryMetric, selectedYearPrimaryMetric],
        primaryMetricUnitDetails.unit,
        primaryMetricDifferenceFormatter
      )
    : null;

  const comparisonMetric2 =
    !isClassificationUnit(selectedYearPrimaryMetric.unit) && isDefined(meanYOYPrimaryMetric)
      ? makeMetricDifference(
          [meanYOYPrimaryMetric, selectedYearPrimaryMetric],
          primaryMetricUnitDetails.unit,
          primaryMetricDifferenceFormatter
        )
      : null;

  /**
   * TODO: remove zeroValue and usage of isMetricZeroValueThenGetZeroMetricElseGetMetric
   * as part of SI-2405 when introducing transform level rounding
   */
  const zeroValue =
    selectedYearPrimaryMetric.value < LARGE_NUMBER_THRESHOLD
      ? ZERO_WITH_PRECISION_FOR_SMALL_NUMBERS
      : ZERO_WITH_PRECISION_FOR_LARGE_NUMBERS;

  const primaryMetricTileContent = {
    ...contentWithPrimaryMetric,
    comparisonMetric1: isMetricZeroValueThenGetZeroMetricElseGetMetric(
      comparisonMetric1,
      zeroValue
    ),
    comparisonMetric2: isMetricZeroValueThenGetZeroMetricElseGetMetric(
      comparisonMetric2,
      zeroValue
    ),
  };

  return primaryMetricTileContent;
};
