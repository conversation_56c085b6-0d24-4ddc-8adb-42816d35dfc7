import {useMemo} from 'react';

import type {Theme} from '@regrow-internal/design-system';

import {isNil} from '_common/utils/typeGuards';

import {curriedMakeBySummaryTypeKeyMetricLookups} from 'containers/si/programs/report/kpi/helpers/curriedMakeBySummaryTypeKeyMetricLookups';
import {makeMetricMapLookups} from 'containers/si/programs/report/kpi/helpers/makeMetricMapLookups';
import {makeSummaryTypeChartContent} from 'containers/si/programs/report/kpi/helpers/makeSummaryTypeChartContent';
import {useCropLookupPairs} from 'containers/si/programs/report/kpi/hooks/useCropLookupPairs';
import {useUnitAndMetricHelpers} from 'containers/si/programs/report/kpi/hooks/useUnitAndMetricHelpers';
import type {
  ColoredSummaryTypeChartProps,
  FormatterLookupMap,
  KpiMetrics,
  MetricLookupMap,
  SummaryTypeChartProps,
  UnitLookupMap,
} from 'containers/si/programs/report/kpi/types';
import {isValidCommonKpiFiltersState} from 'containers/si/typeGuards';
import type {KPIDataFiltersState} from 'containers/si/types';

export const useCropSummaryTypeBarChartContent = <T extends KpiMetrics>({
  byCropMetricsPairs,
  categoryColorKey,
  filtersState,
  isLoading,
  formatterMap,
  metricMap,
  unitMap,
}: {
  byCropMetricsPairs: Array<[string, T | null]> | null | undefined;
  categoryColorKey?: keyof Theme['palette']['categoryPalette'];
  filtersState: KPIDataFiltersState;
  isLoading: boolean;
  formatterMap: FormatterLookupMap<T>;
  metricMap: MetricLookupMap<T>;
  unitMap: UnitLookupMap<T>;
}): SummaryTypeChartProps | null => {
  const hasValidFilters = isValidCommonKpiFiltersState(filtersState);

  const selectedCropsLookupPairs = useCropLookupPairs({filtersState});

  const bySubtypeByCropMetricLookups = useMemo(() => {
    if (
      !hasValidFilters ||
      isLoading ||
      isNil(byCropMetricsPairs) ||
      byCropMetricsPairs.every(([_cropId, metric]) => isNil(metric))
    )
      return null;

    const makeLookup = curriedMakeBySummaryTypeKeyMetricLookups(byCropMetricsPairs);

    return makeMetricMapLookups({makeLookup, metricMap});
  }, [byCropMetricsPairs, hasValidFilters, isLoading, metricMap]);

  const unitAndMetricHelpers = useUnitAndMetricHelpers({
    unitMap,
    metricMap,
    formatterMap,
  });

  const cropSummaryTypeBarChartContent: ColoredSummaryTypeChartProps | null = useMemo(() => {
    if (!hasValidFilters || isNil(bySubtypeByCropMetricLookups) || isNil(selectedCropsLookupPairs))
      return null;

    const summaryTypeChartContent_ = makeSummaryTypeChartContent({
      bySubtypeBySummaryTypeKeyMetricLookups: bySubtypeByCropMetricLookups,
      nameToIdLookupPairs: selectedCropsLookupPairs,
      unitAndMetricHelpers,
    });

    if (isNil(summaryTypeChartContent_)) return null;

    return {
      ...summaryTypeChartContent_,
      categoryColorKey,
    };
  }, [
    bySubtypeByCropMetricLookups,
    categoryColorKey,
    selectedCropsLookupPairs,
    hasValidFilters,
    unitAndMetricHelpers,
  ]);

  return cropSummaryTypeBarChartContent;
};
