import {useMemo} from 'react';

import {type Theme} from '@regrow-internal/design-system';

import {isNil} from '_common/utils/typeGuards';

import {type PrimaryMetricTileProps} from 'containers/si/programs/report/kpi/components/PrimaryMetricTile';
import {type TimeTrendTileProps} from 'containers/si/programs/report/kpi/components/TimeTrendTile';
import {curriedMakeBySummaryTypeKeyMetricLookups} from 'containers/si/programs/report/kpi/helpers/curriedMakeBySummaryTypeKeyMetricLookups';
import {makeMetricMapLookups} from 'containers/si/programs/report/kpi/helpers/makeMetricMapLookups';
import {makePrimaryMetricTileContent} from 'containers/si/programs/report/kpi/helpers/makePrimaryMetricTileContent';
import {makeSummaryTypeChartContent} from 'containers/si/programs/report/kpi/helpers/makeSummaryTypeChartContent';
import {useUnitAndMetricHelpers} from 'containers/si/programs/report/kpi/hooks/useUnitAndMetricHelpers';
import {useYearLookupPairs} from 'containers/si/programs/report/kpi/hooks/useYearLookupPairs';
import type {
  FormatterLookupMap,
  KpiMetrics,
  MetricLookupMap,
  UnitLookupMap,
} from 'containers/si/programs/report/kpi/types';
import {isValidCommonKpiFiltersState} from 'containers/si/typeGuards';
import type {KPIDataFiltersState} from 'containers/si/types';
import {isClassificationUnit} from 'containers/si/utils/value.types';

export const usePrimaryMetricTileAndTimeTrendTileContent = <T extends KpiMetrics>({
  byYearMetricsPairs,
  categoryColorKey,
  isLoading,
  filtersState,
  formatterMap,
  metricMap,
  unitMap,
  isDownwardChangePositive,
}: {
  byYearMetricsPairs: Array<[string, T | null]> | null | undefined;
  categoryColorKey?: keyof Theme['palette']['categoryPalette'];
  isLoading: boolean;
  filtersState: KPIDataFiltersState;
  formatterMap: FormatterLookupMap<T>;
  metricMap: MetricLookupMap<T>;
  unitMap: UnitLookupMap<T>;
  isDownwardChangePositive?: boolean;
}): {
  primaryMetricTileContent: PrimaryMetricTileProps | null;
  timeTrendTileContent: TimeTrendTileProps | null;
} => {
  const hasValidFilters = isValidCommonKpiFiltersState(filtersState);

  const programYearsLookupPairs = useYearLookupPairs({filtersState});

  const bySubtypeByYearMetricLookups = useMemo(() => {
    if (
      !hasValidFilters ||
      isLoading ||
      isNil(programYearsLookupPairs) ||
      isNil(byYearMetricsPairs) ||
      byYearMetricsPairs.every(([_year, metric]) => isNil(metric))
    )
      return null;

    const programYears = programYearsLookupPairs.map(([_, year]) => year);

    const byYearMetricsPairsConfiguredYearsOnly = byYearMetricsPairs.filter(([year, _metrics]) =>
      programYears.includes(year)
    );

    const makeLookup = curriedMakeBySummaryTypeKeyMetricLookups(
      byYearMetricsPairsConfiguredYearsOnly
    );

    return makeMetricMapLookups({makeLookup, metricMap});
  }, [byYearMetricsPairs, hasValidFilters, isLoading, metricMap, programYearsLookupPairs]);

  const unitAndMetricHelpers = useUnitAndMetricHelpers({
    unitMap,
    metricMap,
    formatterMap,
  });

  const primaryMetricTileContent: PrimaryMetricTileProps | null = useMemo(() => {
    if (!hasValidFilters || isNil(bySubtypeByYearMetricLookups))
      return makePrimaryMetricTileContent({unitAndMetricHelpers, isDownwardChangePositive});

    const selectedYear = filtersState.year;

    return makePrimaryMetricTileContent({
      selectedYear,
      unitAndMetricHelpers,
      bySubtypeByYearMetricLookups,
      isDownwardChangePositive,
    });
  }, [
    bySubtypeByYearMetricLookups,
    filtersState.year,
    hasValidFilters,
    isDownwardChangePositive,
    unitAndMetricHelpers,
  ]);

  const timeTrendTileContent: TimeTrendTileProps | null = useMemo(() => {
    if (!hasValidFilters || isNil(bySubtypeByYearMetricLookups) || isNil(programYearsLookupPairs))
      return null;

    const summaryTypeChartContent_ = makeSummaryTypeChartContent({
      bySubtypeBySummaryTypeKeyMetricLookups: bySubtypeByYearMetricLookups,
      nameToIdLookupPairs: programYearsLookupPairs,
      unitAndMetricHelpers,
    });

    if (isNil(summaryTypeChartContent_)) return null;

    const selectedYear = filtersState.year;
    const activePointIndex = summaryTypeChartContent_.labels.indexOf(String(selectedYear));
    const beginYAxisAtZero = unitAndMetricHelpers.primaryMetricUnitDetails.unit === 'unit-interval';
    const hideAverageLine = isClassificationUnit(
      unitAndMetricHelpers.primaryMetricUnitDetails.unit
    );

    return {
      ...summaryTypeChartContent_,
      activePointIndex,
      beginYAxisAtZero,
      categoryColorKey,
      hideAverageLine,
    };
  }, [
    bySubtypeByYearMetricLookups,
    categoryColorKey,
    filtersState.year,
    hasValidFilters,
    programYearsLookupPairs,
    unitAndMetricHelpers,
  ]);

  return {timeTrendTileContent, primaryMetricTileContent};
};
