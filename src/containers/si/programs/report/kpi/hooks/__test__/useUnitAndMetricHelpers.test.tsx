import {combineReducers, configureStore} from '@reduxjs/toolkit';
import {renderHook} from '@testing-library/react-hooks';
import pick from 'lodash/pick';
import React from 'react';
import {Provider} from 'react-redux';

import {MeasurementEnum} from '_common/utils/measurement-unit-options';

import loginReducer, {initialState} from 'containers/login/reducer';
import {
  COVERCROPMETRICS_FORMATTER_MAP,
  COVERCROPMETRICS_UNIT_MAP,
  type CoverCropMetrics,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.cover_crop.transformation';
import {
  TILLAGEMETRICS_FORMATTER_MAP,
  TILLAGEMETRICS_UNIT_MAP,
  type TillageMetrics,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.tillage.transformation';
import {useUnitAndMetricHelpers} from 'containers/si/programs/report/kpi/hooks/useUnitAndMetricHelpers';
import {type MetricLookupMap} from 'containers/si/programs/report/kpi/types';

const store = configureStore({
  reducer: combineReducers({
    login: loginReducer,
  }),
  preloadedState: {
    login: {
      ...initialState,
      user: {
        ...initialState.user,
        settings: {
          ...initialState.user.settings,
          measurement: MeasurementEnum.MetricUnits,
        },
      },
    },
  },
});

const wrapper: React.FC = ({children}) => <Provider store={store}>{children}</Provider>;

const metricMap: MetricLookupMap<CoverCropMetrics> = {
  primaryMetricKey: 'adoption',
  secondaryMetricKey: 'covercroppedArea',
  trackedMetricKey: 'totalTrackedArea',
  unknownMetricKey: 'unknownArea',
  notApplicableMetricKey: 'notApplicableArea',
};
const formatterMap = COVERCROPMETRICS_FORMATTER_MAP;
const unitMap = COVERCROPMETRICS_UNIT_MAP;

const unsupportedMetricMap: MetricLookupMap<TillageMetrics> = {
  primaryMetricKey: 'adoption',
  secondaryMetricKey: 'conservationTillageArea',
  trackedMetricKey: 'totalTrackedArea',
  unknownMetricKey: 'unknownArea',
};
const unsupportedUnitMap = TILLAGEMETRICS_UNIT_MAP;
const unsupportedFormatterMap = TILLAGEMETRICS_FORMATTER_MAP;
describe('useUnitAndMetricHelpers', () => {
  /**
   * Type assertion "tests"
   * These are covering several type test cases that can go wrong if the types are messed up.
   * Typescript should catch if the unitMap, metricMap and formatterMap arguments do not have consistent kpi metric types.
   * Equivalent "should not raise ts errors" cases are also below.
   * If the "should pass" cases raise typescript errors
   * or the "should fail" cases start to pass, something has gone wrong!
   */

  it('should have consistent types', () => {
    const _unsupportedUsage1 = () =>
      useUnitAndMetricHelpers({
        metricMap: unsupportedMetricMap,
        // @ts-expect-error This should error for mismatch on metricMap and other maps (DO NOT REMOVE! IF THIS IS NO LONGER NEEDED, THE TYPES ARE BROKEN)
        formatterMap,
        // @ts-expect-error This should error for mismatch on metricMap and other maps (DO NOT REMOVE! IF THIS IS NO LONGER NEEDED, THE TYPES ARE BROKEN)
        unitMap,
      });

    expect(_unsupportedUsage1).toThrow(Error); // prevents the test from crashing because of bad argument

    const _unsupportedUsage2 = () =>
      useUnitAndMetricHelpers({
        metricMap,
        formatterMap,
        // @ts-expect-error This should error for mismatch on unitMap and other maps (DO NOT REMOVE! IF THIS IS NO LONGER NEEDED, THE TYPES ARE BROKEN)
        unitMap: unsupportedUnitMap,
      });

    expect(_unsupportedUsage2).toThrow(Error); // prevents the test from crashing because of bad arguments

    const _unsupportedUsage3 = () =>
      useUnitAndMetricHelpers({
        metricMap,
        // @ts-expect-error This should error for mismatch on formatterMap and other maps (DO NOT REMOVE! IF THIS IS NO LONGER NEEDED, THE TYPES ARE BROKEN)
        formatterMap: unsupportedFormatterMap,
        unitMap,
      });

    expect(_unsupportedUsage3).toThrow(Error); // prevents the test from crashing because of bad arguments

    const _supportedUsage = renderHook(
      () =>
        useUnitAndMetricHelpers({
          formatterMap,
          metricMap,
          unitMap,
        }),
      {
        wrapper,
      }
    );
  });

  it('should return unit and metric helpers', () => {
    const {result} = renderHook(
      () =>
        useUnitAndMetricHelpers({
          metricMap,
          formatterMap,
          unitMap,
        }),
      {
        wrapper,
      }
    );

    const mockUserUnitsSystem = MeasurementEnum.MetricUnits;

    const expected = {
      primaryMetricUnitDetails: unitMap.adoption(mockUserUnitsSystem),
      secondaryMetricUnitDetails: unitMap.covercroppedArea(mockUserUnitsSystem),
      primaryMetricFormatter: formatterMap.adoption,
    };

    expect(result.current).toEqual(expected);
  });

  it('should return unit and metric helpers without a secondaryMetricUnitDetails if a secondary metric is not defined in metric map', () => {
    const {result} = renderHook(
      () =>
        useUnitAndMetricHelpers({
          metricMap: pick(metricMap, 'primaryMetricKey'),
          formatterMap: pick(formatterMap, 'adoption'),
          unitMap: pick(unitMap, 'adoption'),
        }),
      {
        wrapper,
      }
    );

    const mockUserUnitsSystem = MeasurementEnum.MetricUnits;

    const expected = {
      primaryMetricUnitDetails: unitMap.adoption(mockUserUnitsSystem),
      primaryMetricFormatter: formatterMap.adoption,
    };

    expect(result.current).toEqual(expected);
    expect(result.current.secondaryMetricUnitDetails).toBeUndefined();
  });
});
