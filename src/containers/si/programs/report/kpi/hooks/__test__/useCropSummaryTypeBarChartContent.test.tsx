import {combineReducers, configureStore} from '@reduxjs/toolkit';
import {renderHook} from '@testing-library/react-hooks';
import React from 'react';
import {Provider} from 'react-redux';

import global from '_common/modules/global/reducer';

import {COVER_CROP_BY_CROP_METRICS_PAIRS} from 'containers/si/programs/helpers/__mocks__/covercropMock';
import {
  COVERCROPMETRICS_FORMATTER_MAP,
  COVERCROPMETRICS_UNIT_MAP,
  type CoverCropMetrics,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.cover_crop.transformation';
import {covercropCropSummaryTypeChartDataMock} from 'containers/si/programs/report/kpi/helpers/__mocks__/ChartDataMocks';
import {useCropSummaryTypeBarChartContent} from 'containers/si/programs/report/kpi/hooks/useCropSummaryTypeBarChartContent';
import type {MetricLookupMap} from 'containers/si/programs/report/kpi/types';
import type {KPIDataFiltersState} from 'containers/si/types';

jest.mock(`containers/si/programs/report/kpi/hooks/useCropLookupPairs`);
jest.mock(`containers/si/programs/report/kpi/hooks/useUnitAndMetricHelpers`);

const mockStore = configureStore({
  reducer: combineReducers({global}),
  preloadedState: {},
});

const wrapper: React.FC = ({children}) => <Provider store={mockStore}>{children}</Provider>;

const metricMap: MetricLookupMap<CoverCropMetrics> = {
  primaryMetricKey: 'adoption',
  secondaryMetricKey: 'covercroppedArea',
  trackedMetricKey: 'totalTrackedArea',
  unknownMetricKey: 'unknownArea',
  notApplicableMetricKey: 'notApplicableArea',
};
const formatterMap = COVERCROPMETRICS_FORMATTER_MAP;
const unitMap = COVERCROPMETRICS_UNIT_MAP;

const defaultInput = {
  byCropMetricsPairs: COVER_CROP_BY_CROP_METRICS_PAIRS,
  isLoading: false,
  filtersState: {
    cropIds: [1, 5, 23, 24, 41, 28],
    subsectionIds: [16648, 872, 873, 874, 875],
    year: 2022,
  },
  formatterMap,
  metricMap,
  unitMap,
};

describe('useCropSummaryTypeBarChartContent', () => {
  it('should return crop data formatted to be used in a summary type bar chart component', () => {
    const {result} = renderHook(() => useCropSummaryTypeBarChartContent(defaultInput), {wrapper});
    expect(result.current).toEqual(covercropCropSummaryTypeChartDataMock);
  });

  it('should return null if data is loading', () => {
    const input = {...defaultInput, isLoading: true};
    const {result} = renderHook(() => useCropSummaryTypeBarChartContent(input), {wrapper});
    expect(result.current).toEqual(null);
  });

  it('should return null if no by subtype by crop lookup is provided (no kpi fetch data is returned)', () => {
    const input = {...defaultInput, byCropMetricsPairs: null};
    const {result} = renderHook(() => useCropSummaryTypeBarChartContent(input), {wrapper});
    expect(result.current).toEqual(null);
  });

  it('should return null if all metrics are null', () => {
    const input = {
      ...defaultInput,
      byCropMetricsPairs: defaultInput.byCropMetricsPairs.map<[string, null]>(([id, _metrics]) => [
        id,
        null,
      ]),
    };
    const {result} = renderHook(() => useCropSummaryTypeBarChartContent(input), {wrapper});
    expect(result.current).toEqual(null);
  });

  it('should return null if invalid filters are provided (empty filter state)', () => {
    const invalidFilters0: KPIDataFiltersState = {
      ...defaultInput.filtersState,
      cropIds: [],
    };
    const {result: result0} = renderHook(
      () =>
        useCropSummaryTypeBarChartContent({
          ...defaultInput,
          filtersState: invalidFilters0,
        }),
      {
        wrapper,
      }
    );
    expect(result0.current).toEqual(null);

    const invalidFilters1: KPIDataFiltersState = {
      ...defaultInput.filtersState,
      subsectionIds: [],
    };
    const {result: result1} = renderHook(
      () =>
        useCropSummaryTypeBarChartContent({
          ...defaultInput,
          filtersState: invalidFilters1,
        }),
      {
        wrapper,
      }
    );
    expect(result1.current).toEqual(null);

    const invalidFilters2: KPIDataFiltersState = {
      ...defaultInput.filtersState,
      year: undefined,
    };

    const {result: result2} = renderHook(
      () =>
        useCropSummaryTypeBarChartContent({
          ...defaultInput,
          filtersState: invalidFilters2,
        }),
      {
        wrapper,
      }
    );
    expect(result2.current).toEqual(null);
  });
});
