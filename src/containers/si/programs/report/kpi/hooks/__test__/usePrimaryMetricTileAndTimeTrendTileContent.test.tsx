import {combineReducers, configureStore} from '@reduxjs/toolkit';
import {renderHook} from '@testing-library/react-hooks';
import React from 'react';
import {Provider} from 'react-redux';

import global from '_common/modules/global/reducer';

import {COVER_CROP_BY_YEAR_METRICS_PAIRS} from 'containers/si/programs/helpers/__mocks__/covercropMock';
import {
  COVERCROPMETRICS_FORMATTER_MAP,
  COVERCROPMETRICS_UNIT_MAP,
  type CoverCropMetrics,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.cover_crop.transformation';
import {type PrimaryMetricTileProps} from 'containers/si/programs/report/kpi/components/PrimaryMetricTile';
import {type TimeTrendTileProps} from 'containers/si/programs/report/kpi/components/TimeTrendTile';
import {
  coverCropEmptyPrimaryMetricTilePropsMock,
  coverCropYear2022PrimaryMetricTilePropsMock,
} from 'containers/si/programs/report/kpi/helpers/__mocks__/PrimaryMetricTilePropsMock';
import {coverCropYear2022TimeTrendTilePropsMock} from 'containers/si/programs/report/kpi/helpers/__mocks__/TimeTrendTilePropsMock';
import {usePrimaryMetricTileAndTimeTrendTileContent} from 'containers/si/programs/report/kpi/hooks/usePrimaryMetricTileAndTimeTrendTileContent';
import type {MetricLookupMap} from 'containers/si/programs/report/kpi/types';
import type {KPIDataFiltersState} from 'containers/si/types';

jest.mock(`containers/si/programs/report/kpi/hooks/useYearLookupPairs`);
jest.mock(`containers/si/programs/report/kpi/hooks/useUnitAndMetricHelpers`);

const mockStore = configureStore({
  reducer: combineReducers({global}),
  preloadedState: {},
});

const wrapper: React.FC = ({children}) => <Provider store={mockStore}>{children}</Provider>;

const metricMap: MetricLookupMap<CoverCropMetrics> = {
  primaryMetricKey: 'adoption',
  secondaryMetricKey: 'covercroppedArea',
  trackedMetricKey: 'totalTrackedArea',
  unknownMetricKey: 'unknownArea',
  notApplicableMetricKey: 'notApplicableArea',
};
const formatterMap = COVERCROPMETRICS_FORMATTER_MAP;
const unitMap = COVERCROPMETRICS_UNIT_MAP;

const defaultInput = {
  byYearMetricsPairs: COVER_CROP_BY_YEAR_METRICS_PAIRS,
  isLoading: false,
  filtersState: {
    cropIds: [1, 5, 23, 24, 41, 28],
    subsectionIds: [16648, 872, 873, 874, 875],
    year: 2022,
  },
  formatterMap,
  metricMap,
  unitMap,
};

const defaultOutput: {
  timeTrendTileContent: TimeTrendTileProps | null;
  primaryMetricTileContent: PrimaryMetricTileProps;
} = {
  timeTrendTileContent: coverCropYear2022TimeTrendTilePropsMock,
  primaryMetricTileContent: coverCropYear2022PrimaryMetricTilePropsMock,
};

const nodataOutput: {
  timeTrendTileContent: TimeTrendTileProps | null;
  primaryMetricTileContent: PrimaryMetricTileProps;
} = {
  timeTrendTileContent: null,
  primaryMetricTileContent: coverCropEmptyPrimaryMetricTilePropsMock,
};

describe('usePrimaryMetricTileAndTimeTrendTileContent', () => {
  it('should return primary metric tile formatted to be used in primary metric tile component', () => {
    const {result: result1} = renderHook(
      () => usePrimaryMetricTileAndTimeTrendTileContent(defaultInput),
      {
        wrapper,
      }
    );
    expect(result1.current.primaryMetricTileContent).toEqual(
      defaultOutput.primaryMetricTileContent
    );
    const {result: result2} = renderHook(
      () =>
        usePrimaryMetricTileAndTimeTrendTileContent({
          ...defaultInput,
          isDownwardChangePositive: true,
        }),
      {
        wrapper,
      }
    );
    expect(result2.current.primaryMetricTileContent).toEqual({
      ...defaultOutput.primaryMetricTileContent,
      isDownwardChangePositive: true,
    });
  });

  it('should return time trend data formatted to be used in the time trend chart component', () => {
    const {result} = renderHook(() => usePrimaryMetricTileAndTimeTrendTileContent(defaultInput), {
      wrapper,
    });
    expect(result.current.timeTrendTileContent).toEqual(defaultOutput.timeTrendTileContent);
  });

  it('should return primary metric tile static text if data is loading', () => {
    const input = {...defaultInput, isLoading: true};
    const {result} = renderHook(() => usePrimaryMetricTileAndTimeTrendTileContent(input), {
      wrapper,
    });
    expect(result.current.primaryMetricTileContent).toEqual(nodataOutput.primaryMetricTileContent);
  });

  it('should return no data for time trend chart if data is loading', () => {
    const input = {...defaultInput, isLoading: true};
    const {result} = renderHook(() => usePrimaryMetricTileAndTimeTrendTileContent(input), {
      wrapper,
    });
    expect(result.current.timeTrendTileContent).toEqual(nodataOutput.timeTrendTileContent);
  });

  it('should return primary metric tile static text if no by subtype by year lookup is provided (no kpi fetch data is returned)', () => {
    const input = {...defaultInput, byYearMetricsPairs: null};
    const {result} = renderHook(() => usePrimaryMetricTileAndTimeTrendTileContent(input), {
      wrapper,
    });
    expect(result.current.primaryMetricTileContent).toEqual(nodataOutput.primaryMetricTileContent);
  });

  it('should return no data for time trend chart if no by subtype by year lookup is provided (no kpi fetch data is returned)', () => {
    const input = {...defaultInput, byYearMetricsPairs: null};
    const {result} = renderHook(() => usePrimaryMetricTileAndTimeTrendTileContent(input), {
      wrapper,
    });
    expect(result.current.timeTrendTileContent).toEqual(nodataOutput.timeTrendTileContent);
  });

  it('should return null if all metrics are null', () => {
    const input = {
      ...defaultInput,
      byYearMetricsPairs: defaultInput.byYearMetricsPairs.map<[string, null]>(([id, _metrics]) => [
        id,
        null,
      ]),
    };
    const {result} = renderHook(() => usePrimaryMetricTileAndTimeTrendTileContent(input), {
      wrapper,
    });
    expect(result.current.timeTrendTileContent).toEqual(nodataOutput.timeTrendTileContent);
  });

  it('should return primary metric tile static text and no data for time trend chart if invalid filters are provided (empty filter state)', () => {
    const invalidFilters0: KPIDataFiltersState = {
      ...defaultInput.filtersState,
      cropIds: [],
    };
    const {result: result0} = renderHook(
      () =>
        usePrimaryMetricTileAndTimeTrendTileContent({
          ...defaultInput,
          filtersState: invalidFilters0,
        }),
      {
        wrapper,
      }
    );
    expect(result0.current).toEqual(nodataOutput);

    const invalidFilters1: KPIDataFiltersState = {
      ...defaultInput.filtersState,
      subsectionIds: [],
    };
    const {result: result1} = renderHook(
      () =>
        usePrimaryMetricTileAndTimeTrendTileContent({
          ...defaultInput,
          filtersState: invalidFilters1,
        }),
      {
        wrapper,
      }
    );
    expect(result1.current).toEqual(nodataOutput);

    const invalidFilters2: KPIDataFiltersState = {
      ...defaultInput.filtersState,
      year: undefined,
    };

    const {result: result2} = renderHook(
      () =>
        usePrimaryMetricTileAndTimeTrendTileContent({
          ...defaultInput,
          filtersState: invalidFilters2,
        }),
      {
        wrapper,
      }
    );
    expect(result2.current).toEqual(nodataOutput);
  });
});
