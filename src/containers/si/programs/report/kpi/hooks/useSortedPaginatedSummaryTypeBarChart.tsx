import React, {useCallback, useEffect, useMemo, useState} from 'react';

import {Pagination, Stack, SvgIcon} from '@regrow-internal/design-system';

import {isDefined, isNil} from '_common/utils/typeGuards';
import deepEqual from 'fast-deep-equal/es6';

import {getChartDataRange} from 'containers/si/programs/helpers/chart.helpers';
import {ColoredSummaryTypeBarChart} from 'containers/si/programs/report/kpi/components/ColoredSummaryTypeBarChart';
import {NoData} from 'containers/si/programs/report/kpi/components/NoData';
import {SubTypeStackedAndGroupedOverSummaryTypeBarChart} from 'containers/si/programs/report/kpi/components/SubTypeStackedAndGroupedOverSummaryTypeBarChart';
import {BAR_CHART_ROWS_PER_PAGE} from 'containers/si/programs/report/kpi/constants';
import type {SortBy, SortDirection} from 'containers/si/programs/report/kpi/constants';
import {
  makePaginatedChartData,
  makePaginatedChartDatasetData,
} from 'containers/si/programs/report/kpi/helpers/makePaginatedChartData';
import {
  makeSortedChartData,
  makeSortedChartDatasetData,
} from 'containers/si/programs/report/kpi/helpers/makeSortedChartData';
import {getStackedChartDatasetsRange} from 'containers/si/programs/report/kpi/helpers/makeSummaryTypeChartRangeConfig';
import type {
  SubTypeStackedAndGroupedOverSummaryTypeBarChartProps,
  SummaryTypeChartProps,
} from 'containers/si/programs/report/kpi/types';

type ChartProps = {
  isLoading: boolean;
  summaryTypeBarChartProps: SummaryTypeChartProps | null;
  defaultSort: {by: SortBy; direction: SortDirection; valueSortDirection: SortDirection};
  defaultRowsPerPage?: number;
};
type StackedChartProps = {
  isLoading: boolean;
  summaryTypeBarChartProps: SubTypeStackedAndGroupedOverSummaryTypeBarChartProps | null;
  defaultSort: {
    by: SortBy;
    direction: SortDirection;
    groupName: SubTypeStackedAndGroupedOverSummaryTypeBarChartProps['datasets'][number]['stack'];
    valueSortDirection: SortDirection;
  };
  defaultRowsPerPage?: number;
};

export const useSortedPaginatedSummaryTypeBarChart = (
  chartProps: ChartProps | StackedChartProps
) => {
  const [page, setPage] = useState(0);
  const [rowLabels, setRowLabels] = useState<Array<string> | undefined>(undefined);
  const [sortBy, setSortBy] = useState<SortBy>(chartProps.defaultSort.by);
  const [sortDirection, setSortDirection] = useState<SortDirection>(
    chartProps.defaultSort.direction
  );

  useEffect(() => {
    setPage(0);
  }, [sortBy, sortDirection]);

  const rowsPerPage = useMemo(
    () => chartProps.defaultRowsPerPage ?? BAR_CHART_ROWS_PER_PAGE,
    [chartProps.defaultRowsPerPage]
  );

  const handleSortByClick = useCallback(
    (sortBy_: SortBy) => {
      setSortBy(prevSortBy => {
        if (prevSortBy === sortBy_) {
          setSortDirection(prevSortDirection => (prevSortDirection === 'ASC' ? 'DESC' : 'ASC'));
        } else if (sortBy_ === 'LABEL') {
          setSortDirection('ASC');
        } else if (sortBy_ === 'VALUE') {
          setSortDirection(chartProps.defaultSort.valueSortDirection);
        }
        return sortBy_;
      });
    },
    [chartProps.defaultSort.valueSortDirection]
  );

  const sortedChartProps: ChartProps | StackedChartProps = useMemo(() => {
    if (isNil(chartProps.summaryTypeBarChartProps)) return chartProps;

    if (isStackedChartProps(chartProps))
      return {
        ...chartProps,
        summaryTypeBarChartProps: {
          ...chartProps.summaryTypeBarChartProps,
          ...makeSortedChartDatasetData({
            datasets: chartProps.summaryTypeBarChartProps.datasets,
            groupNameForSortByValue: chartProps.defaultSort.groupName,
            labels: chartProps.summaryTypeBarChartProps.labels,
            sortBy,
            sortDirection,
          }),
        },
      };

    return {
      ...chartProps,
      summaryTypeBarChartProps: {
        ...chartProps.summaryTypeBarChartProps,
        ...makeSortedChartData({
          data: chartProps.summaryTypeBarChartProps.data,
          labels: chartProps.summaryTypeBarChartProps.labels,
          tooltipText: chartProps.summaryTypeBarChartProps.tooltipText,
          sortBy,
          sortDirection,
        }),
      },
    };
  }, [chartProps, sortBy, sortDirection]);

  useEffect(() => {
    // currently isLoading state alone doesn't reflect when a filter changes to make a new kpi request
    // (there's a brief instance where isLoading is false, and response is undefined immediately after a filter change -- I think b/c of our debounce strategy and using a single fetcher hook for all kpi requests)
    // as such we have to check for a null response as well
    if (sortedChartProps.isLoading || isNil(sortedChartProps.summaryTypeBarChartProps?.labels))
      return;

    if (!deepEqual(rowLabels, sortedChartProps.summaryTypeBarChartProps?.labels)) {
      setRowLabels(sortedChartProps.summaryTypeBarChartProps?.labels);
      setPage(0);
    }
  }, [sortedChartProps.isLoading, rowLabels, sortedChartProps.summaryTypeBarChartProps?.labels]);

  const sortedAndPaginatedChartProps: ChartProps | StackedChartProps = useMemo(() => {
    if (isNil(sortedChartProps.summaryTypeBarChartProps)) return sortedChartProps;

    if (isStackedChartProps(sortedChartProps))
      return {
        ...sortedChartProps,
        summaryTypeBarChartProps: {
          ...sortedChartProps.summaryTypeBarChartProps,
          ...makePaginatedChartDatasetData({
            page,
            rowsPerPage,
            datasets: sortedChartProps.summaryTypeBarChartProps.datasets,
            labels: sortedChartProps.summaryTypeBarChartProps.labels,
          }),
          range:
            sortedChartProps.summaryTypeBarChartProps.range ??
            getStackedChartDatasetsRange(sortedChartProps.summaryTypeBarChartProps.datasets),
        },
      };

    return {
      ...sortedChartProps,
      summaryTypeBarChartProps: {
        ...sortedChartProps.summaryTypeBarChartProps,
        ...makePaginatedChartData({
          page,
          rowsPerPage,
          data: sortedChartProps.summaryTypeBarChartProps.data,
          labels: sortedChartProps.summaryTypeBarChartProps.labels,
          tooltipText: sortedChartProps.summaryTypeBarChartProps.tooltipText,
        }),
        range:
          sortedChartProps.summaryTypeBarChartProps.range ??
          getChartDataRange(sortedChartProps.summaryTypeBarChartProps.data),
      },
    };
  }, [sortedChartProps, page, rowsPerPage]);

  const {ChartComponent, PaginationComponent, SortComponent} = useMemo(() => {
    if (
      isNil(chartProps.summaryTypeBarChartProps?.labels) ||
      isNil(sortedAndPaginatedChartProps?.summaryTypeBarChartProps)
    ) {
      return {
        ChartComponent: <NoData />,
        PaginationComponent: undefined,
        SortComponent: undefined,
      };
    }

    const rowCount = chartProps.summaryTypeBarChartProps.labels.length;

    const PaginationComponent_ =
      rowCount > rowsPerPage ? (
        <Pagination count={rowCount} rowsPerPage={rowsPerPage} page={page} onSetPage={setPage} />
      ) : undefined;

    const SortComponent_ = [
      {
        label: (
          <Stack direction="row" gap={1} alignItems="center">
            <SvgIcon type="sort" fontSize="body2" /> Sort by value
          </Stack>
        ),
        onClick: () => handleSortByClick('VALUE'),
      },
      {
        label: (
          <Stack direction="row" gap={1} alignItems="center">
            <SvgIcon type="sort" fontSize="body2" /> Sort by label
          </Stack>
        ),
        onClick: () => handleSortByClick('LABEL'),
      },
    ];

    const ChartComponent_ = isStackedChartProps(sortedAndPaginatedChartProps) ? (
      <SubTypeStackedAndGroupedOverSummaryTypeBarChart
        {...sortedAndPaginatedChartProps.summaryTypeBarChartProps}
      />
    ) : (
      <ColoredSummaryTypeBarChart {...sortedAndPaginatedChartProps.summaryTypeBarChartProps} />
    );

    return {
      PaginationComponent: PaginationComponent_,
      SortComponent: SortComponent_,
      ChartComponent: ChartComponent_,
    };
  }, [chartProps, handleSortByClick, page, rowsPerPage, sortedAndPaginatedChartProps]);

  return {ChartComponent, PaginationComponent, SortComponent};
};

const isStackedChartProps = (
  chartProps: StackedChartProps | ChartProps
): chartProps is StackedChartProps =>
  chartProps.defaultSort.hasOwnProperty('groupName') &&
  isDefined(chartProps.summaryTypeBarChartProps) &&
  chartProps.summaryTypeBarChartProps.hasOwnProperty('datasets');
