import {MeasurementEnum} from '_common/utils/measurement-unit-options';

import {
  COVERCROPMETRICS_FORMATTER_MAP,
  COVERCROPMETRICS_UNIT_MAP,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.cover_crop.transformation';
import {type useUnitAndMetricHelpers} from 'containers/si/programs/report/kpi/hooks/useUnitAndMetricHelpers';

const mockUserUnitsSystem = MeasurementEnum.MetricUnits;

const mockUnitAndMetricHelpers: ReturnType<typeof useUnitAndMetricHelpers> = {
  primaryMetricUnitDetails: COVERCROPMETRICS_UNIT_MAP.adoption(mockUserUnitsSystem),
  secondaryMetricUnitDetails: COVERCROPMETRICS_UNIT_MAP.covercroppedArea(mockUserUnitsSystem),
  primaryMetricFormatter: COVERCROPMETRICS_FORMATTER_MAP.adoption,
};

module.exports = {useUnitAndMetricHelpers: jest.fn().mockReturnValue(mockUnitAndMetricHelpers)};
