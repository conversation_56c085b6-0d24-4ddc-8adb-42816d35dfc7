import {useMemo} from 'react';

import {useAppSelector} from 'store/useRedux';

import {isDefined} from '_common/utils/typeGuards';

import {selectMeasurement} from 'containers/login/login-selectors';
import type {
  FormatterLookupMap,
  KpiMetrics,
  MetricLookupMap,
  UnitAndMetricHelpers,
  UnitLookupMap,
} from 'containers/si/programs/report/kpi/types';

/**
 * This hook returns the necessary unitDetails and formatters for a set of kpi metrics
 * NOTE:
 * Primary, Uncertainty, PrimaryAlt and Uncertainty Alt MUST have the same unit type as Primary
 * as they will all utilize the primaryMetricUnitDetails
 * Secondary, Secondary uncertainty, Tracked, Unknown and Not Applicable metrics MUST have the same unit type as Secondary
 * as they will all utilize the secondaryMetricUnitDetails
 */
export const useUnitAndMetricHelpers = <T extends KpiMetrics>({
  formatterMap,
  metricMap,
  unitMap,
}: {
  formatterMap: FormatterLookupMap<T>;
  metricMap: MetricLookupMap<T>;
  unitMap: UnitLookupMap<T>;
}): UnitAndMetricHelpers => {
  const userUnitsSystem = useAppSelector(selectMeasurement);
  const unitAndMetricHelpers = useMemo(() => {
    const primaryMetricFormatter = formatterMap[metricMap.primaryMetricKey];

    const primaryMetricUnitDetails = unitMap[metricMap.primaryMetricKey](userUnitsSystem);

    const secondaryMetricUnitDetails = isDefined(metricMap?.secondaryMetricKey)
      ? unitMap[metricMap.secondaryMetricKey](userUnitsSystem)
      : undefined;

    return {
      primaryMetricUnitDetails,
      secondaryMetricUnitDetails,
      primaryMetricFormatter,
    };
  }, [formatterMap, metricMap, unitMap, userUnitsSystem]);

  return unitAndMetricHelpers;
};
