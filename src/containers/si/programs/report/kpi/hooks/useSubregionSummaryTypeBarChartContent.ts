import filter from 'lodash/filter';
import {useMemo} from 'react';

import {type Theme} from '@regrow-internal/design-system';

import {getTypedValues} from '_common/utils/object';
import {isNil} from '_common/utils/typeGuards';

import {useFetchSupplySheds} from 'containers/si/api/swr/hooks/useFetchSupplySheds';
import {makeSupplyShedAndSubsectionBoundaryLookup} from 'containers/si/programs/helpers/supplysheds.helpers';
import {curriedMakeBySummaryTypeKeyMetricLookups} from 'containers/si/programs/report/kpi/helpers/curriedMakeBySummaryTypeKeyMetricLookups';
import {makeMetricMapLookups} from 'containers/si/programs/report/kpi/helpers/makeMetricMapLookups';
import {makeSummaryTypeChartContent} from 'containers/si/programs/report/kpi/helpers/makeSummaryTypeChartContent';
import {useUnitAndMetricHelpers} from 'containers/si/programs/report/kpi/hooks/useUnitAndMetricHelpers';
import type {
  ColoredSummaryTypeChartProps,
  FormatterLookupMap,
  KpiMetrics,
  MetricLookupMap,
  UnitLookupMap,
} from 'containers/si/programs/report/kpi/types';
import {isValidCommonKpiFiltersState} from 'containers/si/typeGuards';
import type {KPIDataFiltersState} from 'containers/si/types';

export const useSubregionSummaryTypeBarChartContent = <T extends KpiMetrics>({
  bySubregionMetricsPairs,
  categoryColorKey,
  filtersState,
  isLoading,
  formatterMap,
  metricMap,
  unitMap,
}: {
  bySubregionMetricsPairs: Array<[string, T | null | undefined]> | null | undefined;
  categoryColorKey?: keyof Theme['palette']['categoryPalette'];
  filtersState: KPIDataFiltersState;
  isLoading: boolean;
  formatterMap: FormatterLookupMap<T>;
  metricMap: MetricLookupMap<T>;
  unitMap: UnitLookupMap<T>;
}) => {
  const hasValidFilters = isValidCommonKpiFiltersState(filtersState);

  const {isLoading: isSupplyShedFetchLoading, data: supplyShedsResponse} = useFetchSupplySheds();

  const programSubsectionsPairs = useMemo(() => {
    if (isSupplyShedFetchLoading || isNil(supplyShedsResponse)) return null;

    const {subsections} = makeSupplyShedAndSubsectionBoundaryLookup(supplyShedsResponse, {
      filterTo: {
        subsections: {status: undefined},
      },
    });

    return getTypedValues(subsections).map<[string, string]>(({id, name}) => [name, id]);
  }, [isSupplyShedFetchLoading, supplyShedsResponse]);

  const selectedSubregionsLookupPairs = useMemo(() => {
    if (!hasValidFilters) return null;

    const subsectionIdsString = filtersState.subsectionIds.map(id => String(id));

    return filter(programSubsectionsPairs, ([_name, id]) => subsectionIdsString.includes(id));
  }, [programSubsectionsPairs, filtersState.subsectionIds, hasValidFilters]);

  const bySubtypeBySubregionMetricLookups = useMemo(() => {
    if (
      !hasValidFilters ||
      isLoading ||
      isNil(bySubregionMetricsPairs) ||
      bySubregionMetricsPairs.every(([_subregionId, metric]) => isNil(metric))
    )
      return null;

    const makeLookup = curriedMakeBySummaryTypeKeyMetricLookups(bySubregionMetricsPairs);

    return makeMetricMapLookups({makeLookup, metricMap});
  }, [hasValidFilters, isLoading, metricMap, bySubregionMetricsPairs]);

  const unitAndMetricHelpers = useUnitAndMetricHelpers({
    unitMap,
    metricMap,
    formatterMap,
  });

  const subregionSummaryTypeBarChartContent: ColoredSummaryTypeChartProps | null = useMemo(() => {
    if (
      !hasValidFilters ||
      isNil(bySubtypeBySubregionMetricLookups) ||
      isNil(selectedSubregionsLookupPairs)
    )
      return null;

    const summaryTypeChartContent_ = makeSummaryTypeChartContent({
      bySubtypeBySummaryTypeKeyMetricLookups: bySubtypeBySubregionMetricLookups,
      nameToIdLookupPairs: selectedSubregionsLookupPairs,
      unitAndMetricHelpers,
    });

    if (isNil(summaryTypeChartContent_)) return null;

    return {
      ...summaryTypeChartContent_,
      categoryColorKey,
    };
  }, [
    bySubtypeBySubregionMetricLookups,
    categoryColorKey,
    hasValidFilters,
    selectedSubregionsLookupPairs,
    unitAndMetricHelpers,
  ]);

  return subregionSummaryTypeBarChartContent;
};
