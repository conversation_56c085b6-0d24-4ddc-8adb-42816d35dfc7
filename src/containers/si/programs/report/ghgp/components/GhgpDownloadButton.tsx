import React, {useCallback, useMemo} from 'react';

import {useAppSelector} from 'store/useRedux';

import {downloadFile, MimeType} from '_common/utils/download';
import {isDefined} from '_common/utils/typeGuards';

import type {SourcingRegionId} from 'containers/si/api/apiTypes';
import SIApi from 'containers/si/api/si';
import {DownloadButton} from 'containers/si/components/dialogs/DownloadButton/DownloadButton';
import {useProgramId} from 'containers/si/hooks/useProgramId';
import {selectGhgpFilters} from 'containers/si/module/selectors/ghgp.selectors';
import {isNonNullFilterState} from 'containers/si/programs/report/ghgp/typeGuards';

export const GhgpDownloadButton = () => {
  const {programId} = useProgramId();

  const filterState = useAppSelector(selectGhgpFilters);

  const isDisabled = useMemo(() => !isNonNullFilterState(filterState), [filterState]);
  const onDownload = useCallback(async () => {
    if (isDefined(programId) && isNonNullFilterState(filterState)) {
      try {
        const response = await SIApi.postGhgpReportExport(programId, {
          sourcing_region_ids: filterState.selectedSubregionIds.map(
            (id): SourcingRegionId => ({unit_id: id, unit_type: 'subsection'})
          ),
          crop_types: filterState.selectedCropIds,
          year: filterState.selectedYear,
        });

        const filename = `ghgp_report_${filterState.selectedYear}.xlsx`;

        return Promise.resolve(downloadFile(response.data, filename, MimeType.ApplicationXlsx));
      } catch (error) {
        return Promise.reject(error);
      }
    } else {
      return Promise.reject('Invalid request');
    }
  }, [filterState, programId]);

  return (
    <DownloadButton onDownload={onDownload} disabled={isDisabled}>
      Download .xlsx
    </DownloadButton>
  );
};
