import isNil from 'lodash/isNil';
import React, {useCallback, useEffect, useMemo, useState} from 'react';

import {Box, Button, styled, SvgIcon, Tooltip} from '@regrow-internal/design-system';

import {useAppDispatch, useAppSelector} from 'store/useRedux';
import type {Option} from 'types';

import {isDefined, isNonEmptyArray} from '_common/utils/typeGuards';

import {selectMeasurement} from 'containers/login/login-selectors';
import type {CommonFilters, ComparisonColumn} from 'containers/si/api/apiTypes';
import {useFetchProgram} from 'containers/si/api/swr/hooks/useFetchProgram';
import {useFetchSupplySheds} from 'containers/si/api/swr/hooks/useFetchSupplySheds';
import {CropTypeSelect} from 'containers/si/components/Legacy/CropTypeSelect';
import {DataScenariosSelect} from 'containers/si/components/Legacy/DataScenariosSelect';
import {RegionsSelect} from 'containers/si/components/Legacy/regions-filter';
import {YearsSelect} from 'containers/si/components/Legacy/years-filter';
import {EMPTY_DATA_CELL_VALUE} from 'containers/si/constants';
import {useSICropTypeOptions} from 'containers/si/hooks/legacy/useGetSICropTypeOptions';
import {useFetchAndTransformKPI} from 'containers/si/hooks/useFetchAndTransformKPI';
import {useGetDataScenarioById} from 'containers/si/hooks/useGetDataScenarioById';
import {useGetSourcingRegionsText} from 'containers/si/hooks/useSourcingRegionsText';
import {useTabAccess} from 'containers/si/hooks/useTabAccess';
import {getAllProgramYears} from 'containers/si/module/helpers/program.helpers';
import {
  extractSubsectionIdsFromSupplySheds,
  filterSupplyShedsBySubsectionIds,
  getAllProgramCropsFilteredBySubsection,
  getSIRegionOptions,
} from 'containers/si/module/helpers/supply-shed-helpers';
import {removeCompareFilter, updateFilter} from 'containers/si/module/reducer';
import {selectCompareColumnById} from 'containers/si/module/selectors/compare.selectors';
import {makeCropAreaMetrics} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.crop_area.transformation';
import {
  CellContainer,
  ColumnWrapper,
} from 'containers/si/programs/report/comparison-dashboard/components/compare.styled';
import {transformCompareFilterStateToCommonFilters} from 'containers/si/programs/report/comparison-dashboard/request.helpers';
import {formatCropRotationsToUniqueSupportedCropIds} from 'containers/si/utils/scenarios.format';

type DirtyFiltersState = Omit<ComparisonColumn, 'id' | 'supply_shed_ids'>;

export const FilterColumn = ({filterId, canRemove}: {filterId: string; canRemove: boolean}) => {
  const dispatch = useAppDispatch();

  const {data: program} = useFetchProgram();
  const {data: programSupplySheds} = useFetchSupplySheds();
  const cropTypes = useSICropTypeOptions();
  const allAvailableYears = getAllProgramYears(program ?? undefined);

  const {getSourcingRegionsText} = useGetSourcingRegionsText();
  const {getDataScenarioById} = useGetDataScenarioById();
  const {getHasTabAccess} = useTabAccess();
  const userUnitsSystem = useAppSelector(selectMeasurement);

  const filterState = useAppSelector(s => selectCompareColumnById(s, filterId));
  const [dirtyFiltersState, setDirtyFiltersState] = useState<DirtyFiltersState>(
    makeDirtyFilterStateFromReduxFilterState(filterState)
  );
  const [isDirty, setIsDirty] = useState(false);

  const setPartialDirtyFiltersState = useCallback((newFiltersState: Partial<DirtyFiltersState>) => {
    setIsDirty(true);
    setDirtyFiltersState((previousDirtyFiltersState: DirtyFiltersState) => ({
      ...previousDirtyFiltersState,
      ...newFiltersState,
    }));
  }, []);

  useEffect(() => {
    setDirtyFiltersState(makeDirtyFilterStateFromReduxFilterState(filterState));
    setIsDirty(false);

    // This is to keep redux filters in sync with local filters as
    // redux filters are currently responsible for triggering kpi fetches
    // via the useGetKpiDashboardCommonFilters hook
    // TODO: remove usage of global redux state to manage SR filters and requests
    //   const newReduxFilterState = transformKpiDataFilterStateToReduxKpiFilters({
    //     ...filtersState,
    //     cropIds: validCropIds,
    //     subsectionIds: validSubsectionIds,
    //   });
    //   dispatch(setPartialKPIFilter(newReduxFilterState));
  }, [filterState]);

  const commonFilters: CommonFilters | undefined = useMemo(
    () => transformCompareFilterStateToCommonFilters(filterState),
    [filterState]
  );

  const {topLevel: topLevelCropAreaMetric} = useFetchAndTransformKPI({
    kpi: 'crop_area',
    summaries: [],
    commonFilters,
    kpiTransformer: makeCropAreaMetrics(userUnitsSystem),
  });

  // Data Scenario

  const dirtySelectedDataScenario = useMemo(
    () => getDataScenarioById(dirtyFiltersState?.data_scenario_id),
    [getDataScenarioById, dirtyFiltersState?.data_scenario_id]
  );

  // Region filter options

  const filteredSupplySheds = useMemo(
    () =>
      isDefined(dirtySelectedDataScenario) && isNonEmptyArray(dirtySelectedDataScenario.regions)
        ? filterSupplyShedsBySubsectionIds(
            programSupplySheds ?? [],
            dirtySelectedDataScenario.regions
          )
        : programSupplySheds ?? [],
    [dirtySelectedDataScenario, programSupplySheds]
  );

  const availableSubsectionIds = extractSubsectionIdsFromSupplySheds(filteredSupplySheds);

  const regionOptions = React.useMemo(
    () => getSIRegionOptions(filteredSupplySheds),
    [filteredSupplySheds]
  );

  const selectedSubregionsTooltipText = useMemo(
    () => getSourcingRegionsText(dirtyFiltersState?.subsection_ids ?? []),
    [dirtyFiltersState?.subsection_ids, getSourcingRegionsText]
  );

  // Crop filter options

  const filteredCropTypes = React.useMemo(() => {
    if (isNil(dirtySelectedDataScenario?.crops) && isNil(dirtySelectedDataScenario?.cropRotations))
      return cropTypes;

    const cropIds =
      dirtySelectedDataScenario?.crops ??
      formatCropRotationsToUniqueSupportedCropIds(dirtySelectedDataScenario?.cropRotations ?? null);

    return isNonEmptyArray(cropIds)
      ? cropTypes.filter(crop => cropIds.includes(Number(crop.value)))
      : cropTypes;
  }, [cropTypes, dirtySelectedDataScenario?.cropRotations, dirtySelectedDataScenario?.crops]);

  // TODO: update to use useProgramCrops and remove usage of useSICropTypeOptions
  const {cropOptions} = React.useMemo(
    () =>
      getAllProgramCropsFilteredBySubsection(
        programSupplySheds ?? [],
        filteredCropTypes,
        dirtyFiltersState?.subsection_ids
      ),
    [filteredCropTypes, dirtyFiltersState?.subsection_ids, programSupplySheds]
  );

  const selectedCropsTooltipText = React.useMemo(
    () =>
      (dirtyFiltersState?.crop_types ?? []).length <= 1
        ? ''
        : dirtyFiltersState?.crop_types
            .map(
              (id: number) =>
                cropOptions.find((option: Option) => String(option.value) === String(id))?.label
            )
            .filter(isDefined)
            .join('; '),
    [cropOptions, dirtyFiltersState?.crop_types]
  );

  const handleDataScenarioChange = useCallback(
    (selectedId: ComparisonColumn['data_scenario_id']) => {
      setPartialDirtyFiltersState({
        data_scenario_id: selectedId,
        subsection_ids: [],
        crop_types: [],
        years: [],
      });
    },
    [setPartialDirtyFiltersState]
  );

  const handleRegionFilterChange = useCallback(
    (selectedIds: ComparisonColumn['subsection_ids']) => {
      setPartialDirtyFiltersState({
        subsection_ids: selectedIds,
      });
    },
    [setPartialDirtyFiltersState]
  );

  const handleCropFilterChange = useCallback(
    (selectedIds: ComparisonColumn['crop_types']) => {
      setPartialDirtyFiltersState({
        crop_types: selectedIds,
      });
    },
    [setPartialDirtyFiltersState]
  );

  const handleYearFilterChange = useCallback(
    (selectedIds: ComparisonColumn['years']) => {
      setPartialDirtyFiltersState({
        years: selectedIds,
      });
    },
    [setPartialDirtyFiltersState]
  );

  const handleApply = useCallback(() => {
    dispatch(
      updateFilter({
        id: filterId,
        ...dirtyFiltersState,
      })
    );
  }, [dirtyFiltersState, dispatch, filterId]);

  const handleCancel = useCallback(() => {
    setPartialDirtyFiltersState(makeDirtyFilterStateFromReduxFilterState(filterState));
    setIsDirty(false);
  }, [setPartialDirtyFiltersState, filterState]);

  return (
    <FilterColumnWrapper>
      {getHasTabAccess('data_scenarios') && (
        <CellContainer height={39} justifyContent="flex-start">
          <DataScenariosSelect
            onChange={handleDataScenarioChange}
            selectedId={dirtyFiltersState?.data_scenario_id}
          />
        </CellContainer>
      )}
      <Tooltip
        title={selectedSubregionsTooltipText}
        id={`selected-subregions-tooltip-${filterId}`}
        disableInteractive
      >
        <CellContainer height={39}>
          <RegionsSelect
            selectedIds={dirtyFiltersState?.subsection_ids ?? []}
            availableSubsectionIds={availableSubsectionIds}
            options={regionOptions}
            onChange={handleRegionFilterChange}
          />
        </CellContainer>
      </Tooltip>

      <Tooltip
        title={selectedCropsTooltipText}
        id={`selected-crops-tooltip-${filterId}`}
        disableInteractive
      >
        <CellContainer height={39}>
          <CropTypeSelect
            selectedCropIds={dirtyFiltersState?.crop_types ?? []}
            cropTypeOptions={cropOptions}
            onChange={handleCropFilterChange}
          />
        </CellContainer>
      </Tooltip>
      <CellContainer height={39}>
        <YearsSelect
          selectedYears={dirtyFiltersState?.years ?? []}
          allAvailableYears={allAvailableYears}
          onChange={handleYearFilterChange}
        />
      </CellContainer>

      <CellContainer height={39} justifyContent="space-between">
        {isDefined(topLevelCropAreaMetric?.crop_area)
          ? `${topLevelCropAreaMetric.crop_area.formattedValue} ${topLevelCropAreaMetric.crop_area.unit}`
          : EMPTY_DATA_CELL_VALUE}
        <Box display={'flex'} gap={2} alignItems={'center'}>
          <Button
            size="small"
            onClick={handleApply}
            disabled={!isDirty || !hasValidFilterState(dirtyFiltersState)}
          >
            Apply
          </Button>
          {isDirty && (
            <Button variant="outlined" color="secondary" size="small" onClick={handleCancel}>
              Cancel
            </Button>
          )}
          {canRemove && (
            <Button
              variant="outlined"
              color="secondary"
              size="small"
              onClick={() => dispatch(removeCompareFilter({id: filterId}))}
            >
              <SvgIcon type="delete" color="secondary" fontSize="inherit" />
            </Button>
          )}
        </Box>
      </CellContainer>
    </FilterColumnWrapper>
  );
};

const FilterColumnWrapper = styled(ColumnWrapper)`
  //Overriding styles for fluro-chip
  .area-select-menu {
    .fluro-chip {
      border: none;
      border-bottom: 1px solid ${({theme}) => theme.palette.semanticPalette.stroke.main};
      height: 39px;
      border-radius: 0;
      width: 100%;
      background-color: ${({theme}) => theme.palette.semanticPalette.surface.secondary};
    }
    &.divider {
      .fluro-chip {
        border-bottom: 4px solid ${({theme}) => theme.palette.semanticPalette.stroke.main};
      }
    }
  }
`;

const EMPTY_FILTER_STATE = {
  years: [],
  data_scenario_id: undefined,
  subsection_ids: [],
  crop_types: [],
};

const makeDirtyFilterStateFromReduxFilterState = (
  reduxFilterState: ComparisonColumn | undefined
) => {
  if (isNil(reduxFilterState)) return EMPTY_FILTER_STATE;

  const {years, data_scenario_id, subsection_ids, crop_types} = reduxFilterState;

  return {
    years,
    data_scenario_id,
    subsection_ids,
    crop_types,
  };
};

const hasValidFilterState = (filtersState: DirtyFiltersState) =>
  isNonEmptyArray(filtersState.subsection_ids) &&
  isNonEmptyArray(filtersState.crop_types) &&
  isNonEmptyArray(filtersState.years);
