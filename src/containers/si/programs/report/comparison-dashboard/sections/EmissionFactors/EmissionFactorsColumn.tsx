import head from 'lodash/head';
import React, {useMemo} from 'react';

import {useAppSelector} from 'store/useRedux';

import {kpiToDataProductMap} from 'containers/si/constants';
import {useFetchAndTransformKPI} from 'containers/si/hooks/useFetchAndTransformKPI';
import {useKPIAccess} from 'containers/si/hooks/useKPIAccess';
import {
  selectCompareColumnById,
  selectIsInitialFilterStateById,
} from 'containers/si/module/selectors/compare.selectors';
import {makeFertEFMetrics} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.fert_emissions_factor.transformation';
import {makeGhgEFMetrics} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.ghg_emissions_factor.transformation';
import {makeNetGhgEFMetrics} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.net_ghg_emissions_factor.transformation';
import {makeSocEFMetrics} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.soc_emissions_factor.transformation';
import {ColumnWrapper} from 'containers/si/programs/report/comparison-dashboard/components/compare.styled';
import {transformCompareFilterStateToCommonFilters} from 'containers/si/programs/report/comparison-dashboard/request.helpers';

import {EmissionsFactorCell, type EmissionsFactorCellProps} from './EmissionsFactorCell';

export const EmissionFactorsColumn = ({filterId}: {filterId: string}) => {
  const filterState = useAppSelector(s => selectCompareColumnById(s, filterId));
  const isInitialState = useAppSelector(s => selectIsInitialFilterStateById(s, filterId));

  const {isKPIAvailable} = useKPIAccess();

  const hasBookValueAccess = useMemo(() => isKPIAvailable('ef_book_values'), [isKPIAvailable]);
  const [isOneCropSelected] = useMemo(() => {
    const isOneCrop = filterState?.crop_types.length === 1;

    return [isOneCrop, isOneCrop ? head<number>(filterState?.crop_types) : undefined];
  }, [filterState?.crop_types]);

  const commonFilters = useMemo(
    () => transformCompareFilterStateToCommonFilters(filterState),
    [filterState]
  );

  const {isLoading, topLevel: ghgMetrics} = useFetchAndTransformKPI({
    kpi: 'ghg_emissions_factor',
    summaries: [],
    commonFilters,
    kpiTransformer: makeGhgEFMetrics,
    shouldFetch: isOneCropSelected,
  });
  const {topLevel: socMetrics} = useFetchAndTransformKPI({
    kpi: 'soc_emissions_factor',
    summaries: [],
    commonFilters,
    kpiTransformer: makeSocEFMetrics,
    shouldFetch: isOneCropSelected,
  });
  const {topLevel: netMetrics} = useFetchAndTransformKPI({
    kpi: 'net_ghg_emissions_factor',
    summaries: [],
    commonFilters,
    kpiTransformer: makeNetGhgEFMetrics,
    shouldFetch: isOneCropSelected,
  });
  const {topLevel: fertMetrics} = useFetchAndTransformKPI({
    kpi: 'fert_emissions_factor',
    summaries: [],
    commonFilters,
    kpiTransformer: makeFertEFMetrics,
    shouldFetch: isOneCropSelected,
  });

  const commonEFCellParams: Omit<
    EmissionsFactorCellProps,
    'efMetric' | 'efUncertaintyMetric' | 'bookValue' | 'tooltipTitle'
  > = useMemo(
    () => ({
      year: head(filterState?.years),
      isOneCropSelected,
      isLoading: isLoading || isInitialState,
      hasBookValueAccess,
    }),
    [filterState?.years, isInitialState, hasBookValueAccess, isLoading, isOneCropSelected]
  );

  return (
    <ColumnWrapper>
      {isKPIAvailable(kpiToDataProductMap.ghg_emissions_factor) && (
        <EmissionsFactorCell
          efMetric={ghgMetrics?.ghgEmissionsPerYield}
          efUncertaintyMetric={ghgMetrics?.ghgEFStdErr}
          tooltipTitle="Field GHG EF"
          {...commonEFCellParams}
        />
      )}
      {isKPIAvailable(kpiToDataProductMap.soc_emissions_factor) && (
        <EmissionsFactorCell
          efMetric={socMetrics?.socEmissionsPerYield}
          efUncertaintyMetric={socMetrics?.socEFStdErr}
          tooltipTitle="Field SOC EF"
          {...commonEFCellParams}
        />
      )}
      {isKPIAvailable(kpiToDataProductMap.net_ghg_emissions_factor) && (
        <EmissionsFactorCell
          efMetric={netMetrics?.netGhgEmissionsPerYield}
          efUncertaintyMetric={netMetrics?.netGhgEFStdErr}
          tooltipTitle="Field net EF"
          {...commonEFCellParams}
        />
      )}
      {isKPIAvailable(kpiToDataProductMap.fert_emissions_factor) && (
        <EmissionsFactorCell
          efMetric={fertMetrics?.fertEmissionsPerYield}
          tooltipTitle="Fertilizer EF"
          {...commonEFCellParams}
        />
      )}
    </ColumnWrapper>
  );
};
