import {useFeatureFlagEnabled} from 'posthog-js/react';
import React, {useMemo} from 'react';

import {Stack, Tooltip, Typography} from '@regrow-internal/design-system';

import {useAppSelector} from 'store/useRedux';

import {isDefined, isNil, isUndefined} from '_common/utils/typeGuards';

import {selectMeasurement} from 'containers/login/login-selectors';
import type {CommonFilters} from 'containers/si/api/apiTypes';
import {
  CROPLAND_NOTAPPLICABLE_TEXT,
  EMPTY_DATA_CELL_VALUE,
  EXCLUDING_NOTAPPLICABLE_TEXT,
  PLUS_OR_MINUS,
} from 'containers/si/constants';
import {useFetchAndTransformCommonRotations} from 'containers/si/hooks/useFetchAndTransformCommonRotations';
import {useFetchAndTransformKPI} from 'containers/si/hooks/useFetchAndTransformKPI';
import {useGetCropLabelById} from 'containers/si/hooks/useGetCropLabelById';
import {
  selectCompareColumnById,
  selectIsInitialFilterStateById,
} from 'containers/si/module/selectors/compare.selectors';
import {
  makeCoverCropExpectedMetrics,
  makeCoverCropExpectedWithNaExclusiveMetrics,
  makeCoverCropMetrics,
  type CoverCropMetrics,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.cover_crop.transformation';
import {makeFarmCountMetrics} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.farm_count.transformation';
import {makeFertPerAreaMetrics} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.fertilizer_per_area.transformation';
import {makeG2cLulcMetrics} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.grassland_to_cropland_lulc.transformation';
import {makeLivingRootCoverMetrics} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.living_root_cover.transformation';
import {
  makeTillageExpectedMetrics,
  makeTillageMetrics,
  type TillageMetrics,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.tillage.transformation';
import {makeMetricPercentage} from 'containers/si/programs/helpers/metric.helpers';
import {ColumnWrapper} from 'containers/si/programs/report/comparison-dashboard/components/compare.styled';
import {getKPICell} from 'containers/si/programs/report/comparison-dashboard/components/KPICell';
import {KPIWithUncertainty} from 'containers/si/programs/report/comparison-dashboard/components/KPIWithUncertainty';
import {NoDataMessage} from 'containers/si/programs/report/comparison-dashboard/components/NoData';
import {transformCompareFilterStateToCommonFilters} from 'containers/si/programs/report/comparison-dashboard/request.helpers';

export const AgriculturalPracticesColumn = ({filterId}: {filterId: string}) => {
  // TODO: SI-3446 remove feature flag and hard code transformer as constant
  const isCoverCropNaInclusiveEnabled = useFeatureFlagEnabled('si-covercrop-na-inclusive');
  // TODO: SI-3064 remove feature flags and adjust transformers
  const isExpectedValuesEnabled = useFeatureFlagEnabled('si-expected-values');
  const isMosaicEnabled = useFeatureFlagEnabled('si-mosaic-be');

  const filterState = useAppSelector(s => selectCompareColumnById(s, filterId));
  const isInitialState = useAppSelector(s => selectIsInitialFilterStateById(s, filterId));
  const userUnitsSystem = useAppSelector(selectMeasurement);

  const commonFilters: CommonFilters | undefined = useMemo(
    () => transformCompareFilterStateToCommonFilters(filterState),
    [filterState]
  );

  const {isLoading: isGetCropLabelLoading, getSISupportedCropRotationLabelById} =
    useGetCropLabelById();

  const {isLoading: commonCropRotationsIsLoading, rotationsSummary: cropRotationSummary} =
    useFetchAndTransformCommonRotations({
      commonFilters,
      topN: 5,
    });

  const {isLoading: livingRootLoading, topLevel: topLevelLivingRootCoverMetrics} =
    useFetchAndTransformKPI({
      kpi: 'living_root_cover',
      summaries: [],
      commonFilters,
      kpiTransformer: makeLivingRootCoverMetrics,
    });

  const {isLoading: tillageLoading, topLevel: topLevelTillageMetrics} = useFetchAndTransformKPI({
    kpi: 'tillage',
    summaries: [],
    commonFilters,
    kpiTransformer:
      isExpectedValuesEnabled && isMosaicEnabled // TODO: SI-3064 remove feature flags
        ? makeTillageExpectedMetrics(userUnitsSystem)
        : makeTillageMetrics(userUnitsSystem),
  });

  const {
    isLoading: grasslandToCroplandLUCIsLoading,
    topLevel: topLevelGrasslandToCroplandLUCMetrics,
  } = useFetchAndTransformKPI({
    kpi: 'grassland_to_cropland_lulc',
    summaries: [],
    commonFilters,
    kpiTransformer: makeG2cLulcMetrics(userUnitsSystem),
  });

  const {isLoading: fertilizerPerAreaLoading, topLevel: topLevelFertilizerPerAreaMetrics} =
    useFetchAndTransformKPI({
      kpi: 'fertilizer_per_area',
      summaries: [],
      commonFilters,
      kpiTransformer: makeFertPerAreaMetrics(userUnitsSystem),
      shouldFetch: filterState?.crop_types.length === 1,
    });

  const {isLoading: farmCountLoading, topLevel: farmCountMetric} = useFetchAndTransformKPI({
    kpi: 'farm_count',
    summaries: [],
    commonFilters,
    kpiTransformer: makeFarmCountMetrics,
  });

  // TODO: SI-3064 SI-3446 remove useMemo and reference transformer in useFetchAndTransformKPI directly
  const coverCropKpiTransformer = useMemo(() => {
    if (isExpectedValuesEnabled && isMosaicEnabled) {
      if (isCoverCropNaInclusiveEnabled) {
        return makeCoverCropExpectedWithNaExclusiveMetrics(userUnitsSystem);
      } else {
        return makeCoverCropExpectedMetrics(userUnitsSystem);
      }
    } else {
      return makeCoverCropMetrics(userUnitsSystem);
    }
  }, [isCoverCropNaInclusiveEnabled, isExpectedValuesEnabled, isMosaicEnabled, userUnitsSystem]);

  const {topLevel: topLevelCoverCropMetric, isLoading: coverCropLoading} = useFetchAndTransformKPI({
    kpi: 'cover_crop',
    summaries: [],
    commonFilters,
    kpiTransformer: coverCropKpiTransformer,
  });

  const KPICell = useMemo(() => getKPICell(isInitialState), [isInitialState]);

  return (
    <ColumnWrapper>
      <KPICell kpi="farm_ownership" isLoading={farmCountLoading}>
        {farmCountMetric?.farm_count.formattedValue}
      </KPICell>
      <KPICell
        kpi="crop_rotation"
        isLoading={commonCropRotationsIsLoading || isGetCropLabelLoading}
        height={290}
      >
        {isDefined(cropRotationSummary) ? (
          <Stack>
            {cropRotationSummary
              .toSorted((a, b) =>
                isDefined(a?.[1]) && isDefined(b?.[1])
                  ? b[1].rotationArea.value - a[1].rotationArea.value
                  : 0
              )
              .map(([cropId, cropRotationMetrics]) => (
                <Stack key={cropId} alignItems="flex-end">
                  <Typography>{getSISupportedCropRotationLabelById(cropId)}</Typography>
                  <Typography variant="body2" color="secondary">
                    {isDefined(cropRotationMetrics) ? (
                      `${cropRotationMetrics.rotationArea.formattedValue} ${cropRotationMetrics.rotationArea.unit}`
                    ) : (
                      <NoDataMessage />
                    )}
                  </Typography>
                </Stack>
              ))}
          </Stack>
        ) : null}
      </KPICell>
      <KPICell kpi="living_root" isLoading={livingRootLoading}>
        {topLevelLivingRootCoverMetrics?.yearAvg?.formattedValue}
      </KPICell>
      <KPICell kpi="cover_cropping" isLoading={coverCropLoading}>
        <CoverCropUncertainty coverCropMetrics={topLevelCoverCropMetric} />
      </KPICell>
      {/* TODO, if percentages will be used broadly, make standardized tillage transform and test that would output percentage based metrics with standardized formatting */}
      <KPICell kpi="tillage" isLoading={tillageLoading}>
        <TillageUncertainty
          isLoading={tillageLoading}
          metrics={topLevelTillageMetrics}
          valueKey="conventionalTillageArea"
        />
      </KPICell>
      <KPICell kpi="tillage" isLoading={tillageLoading}>
        <TillageUncertainty
          isLoading={tillageLoading}
          metrics={topLevelTillageMetrics}
          valueKey="reducedTillageArea"
        />
      </KPICell>
      <KPICell kpi="tillage" isLoading={tillageLoading}>
        <TillageUncertainty
          isLoading={tillageLoading}
          metrics={topLevelTillageMetrics}
          valueKey="noTillageArea"
        />
      </KPICell>
      <KPICell kpi="lulc" isLoading={grasslandToCroplandLUCIsLoading}>
        {topLevelGrasslandToCroplandLUCMetrics?.conversion.formattedValue}
      </KPICell>
      <KPICell kpi="fertilizer_usage" isLoading={fertilizerPerAreaLoading}>
        {filterState?.crop_types.length === 1 ? (
          topLevelFertilizerPerAreaMetrics?.fertMassPerArea.formattedValue
        ) : (
          <Typography variant="body1" color={'semanticPalette.text.secondary'} fontStyle={'italic'}>
            Select one crop to see fertilizer application
          </Typography>
        )}
      </KPICell>
    </ColumnWrapper>
  );
};

const CoverCropUncertainty = ({coverCropMetrics}: {coverCropMetrics: CoverCropMetrics | null}) => {
  let tooltip: string | undefined;
  if (
    isDefined(coverCropMetrics) &&
    !isUndefined(coverCropMetrics.adoptionNotApplicableExclusive) &&
    coverCropMetrics.notApplicableArea.value !== 0
  ) {
    if (isNil(coverCropMetrics.adoptionNotApplicableExclusive)) {
      tooltip = EMPTY_DATA_CELL_VALUE;
    } else {
      tooltip = `${coverCropMetrics.adoptionNotApplicableExclusive.formattedValue}`;
      if (!isNil(coverCropMetrics.adoptionStdDevNotApplicableExclusive)) {
        tooltip = `${tooltip} ${PLUS_OR_MINUS} ${coverCropMetrics.adoptionStdDevNotApplicableExclusive.formattedValue}`;
      }
      tooltip = `${tooltip} (${EXCLUDING_NOTAPPLICABLE_TEXT}) \n${coverCropMetrics.notApplicableArea.formattedValue} ${CROPLAND_NOTAPPLICABLE_TEXT} ${coverCropMetrics.notApplicableArea.unit}`;
    }
  }

  return (
    <Tooltip title={tooltip ?? ''} placement="right">
      <span>
        <KPIWithUncertainty
          kpiMetric={coverCropMetrics?.adoption}
          uncertaintyMetric={coverCropMetrics?.adoptionStdDev}
        />
      </span>
    </Tooltip>
  );
};

const TillageUncertainty = ({
  isLoading,
  metrics,
  valueKey,
}: {
  isLoading: boolean;
  metrics: TillageMetrics | null;
  valueKey: Extract<
    keyof TillageMetrics,
    | 'adoption'
    | 'conservationTillageArea'
    | 'noTillageArea'
    | 'reducedTillageArea'
    | 'conventionalTillageArea'
  >;
}) => {
  if (isLoading) return <Typography>{EMPTY_DATA_CELL_VALUE}</Typography>;
  if (isNil(metrics) || isNil(metrics[valueKey]) || isNil(metrics.totalTrackedArea))
    return <NoDataMessage />;

  const value = metrics[valueKey];
  const uncertainty = metrics[`${valueKey}StdDev`];

  const percentageValue = makeMetricPercentage(value, metrics.totalTrackedArea, {sigDigits: 1})
    ?.formattedValue;

  return (
    <Stack alignItems="center">
      <Typography variant="body1">{percentageValue}</Typography>
      {isDefined(uncertainty) && (
        <Typography variant="body2" color="secondary">
          {`${value.formattedValue} ${value.unit} ${PLUS_OR_MINUS} ${uncertainty.formattedValue}`}
        </Typography>
      )}
    </Stack>
  );
};
