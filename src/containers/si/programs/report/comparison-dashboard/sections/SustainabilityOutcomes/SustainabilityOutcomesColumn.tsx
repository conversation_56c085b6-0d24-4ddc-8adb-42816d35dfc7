import React, {useMemo} from 'react';

import {useAppSelector} from 'store/useRedux';

import {selectMeasurement} from 'containers/login/login-selectors';
import {kpiToDataProductMap} from 'containers/si/constants';
import {useFetchAndTransformKPI} from 'containers/si/hooks/useFetchAndTransformKPI';
import {useKPIAccess} from 'containers/si/hooks/useKPIAccess';
import {
  selectCompareColumnById,
  selectIsInitialFilterStateById,
} from 'containers/si/module/selectors/compare.selectors';
import {makeGhgMetrics} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.ghg_kg_per_m2.transformation';
import {makeNetImpactMetrics} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.net_impact_kg_per_m2.transformation';
import {makeSocMetrics} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.soc_kg_per_m2.transformation';
import {ColumnWrapper} from 'containers/si/programs/report/comparison-dashboard/components/compare.styled';
import {getKPICell} from 'containers/si/programs/report/comparison-dashboard/components/KPICell';
import {KPIWithUncertainty} from 'containers/si/programs/report/comparison-dashboard/components/KPIWithUncertainty';
import {transformCompareFilterStateToCommonFilters} from 'containers/si/programs/report/comparison-dashboard/request.helpers';
import {YieldPerAreaCell} from 'containers/si/programs/report/comparison-dashboard/sections/SustainabilityOutcomes/YieldPerAreaCell';

interface SustainabilityOutcomesColumnProps {
  filterId: string;
}

export const SustainabilityOutcomesColumn: React.FC<SustainabilityOutcomesColumnProps> = ({
  filterId,
}) => {
  const filterState = useAppSelector(s => selectCompareColumnById(s, filterId));
  const userUnitsSystem = useAppSelector(selectMeasurement);
  const isInitialState = useAppSelector(s => selectIsInitialFilterStateById(s, filterId));
  const {isKPIAvailable} = useKPIAccess();

  const commonFilters = useMemo(
    () => transformCompareFilterStateToCommonFilters(filterState),
    [filterState]
  );

  const {isLoading: ghgLoading, topLevel: ghgMetrics} = useFetchAndTransformKPI({
    kpi: 'ghg_kg_per_m2',
    summaries: [],
    commonFilters,
    kpiTransformer: makeGhgMetrics(userUnitsSystem),
  });
  const {isLoading: socLoading, topLevel: socMetrics} = useFetchAndTransformKPI({
    kpi: 'soc_kg_per_m2',
    summaries: [],
    commonFilters,
    kpiTransformer: makeSocMetrics(userUnitsSystem),
  });
  const {isLoading: netLoading, topLevel: netMetrics} = useFetchAndTransformKPI({
    kpi: 'net_impact_kg_per_m2',
    summaries: [],
    commonFilters,
    kpiTransformer: makeNetImpactMetrics(userUnitsSystem),
  });

  const KPICell = useMemo(() => getKPICell(isInitialState), [isInitialState]);

  return (
    <ColumnWrapper>
      <KPICell kpi={kpiToDataProductMap.ghg_kg_per_m2} isLoading={ghgLoading}>
        <KPIWithUncertainty
          kpiMetric={ghgMetrics?.ghgMassPerArea}
          uncertaintyMetric={ghgMetrics?.ghgStdErr}
        />
      </KPICell>
      <KPICell kpi={kpiToDataProductMap.soc_kg_per_m2} isLoading={socLoading}>
        <KPIWithUncertainty
          kpiMetric={socMetrics?.dSocMassPerArea}
          uncertaintyMetric={socMetrics?.dSocStdErr}
        />
      </KPICell>
      <KPICell kpi={kpiToDataProductMap.net_impact_kg_per_m2} isLoading={netLoading}>
        <KPIWithUncertainty
          kpiMetric={netMetrics?.netImpact}
          uncertaintyMetric={netMetrics?.netImpactStdErr}
        />
      </KPICell>
      {isKPIAvailable(kpiToDataProductMap.yield_per_area) && (
        <YieldPerAreaCell
          filterState={filterState}
          measurement={userUnitsSystem}
          isInitialState={isInitialState}
        />
      )}
    </ColumnWrapper>
  );
};
