import React, {useMemo} from 'react';

import {Typography} from '@regrow-internal/design-system';

import type {MeasurementEnum} from '_common/utils/measurement-unit-options';

import type {ComparisonColumn} from 'containers/si/api/apiTypes';
import {useFetchAndTransformKPI} from 'containers/si/hooks/useFetchAndTransformKPI';
import {makeYieldPerAreaMetrics} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.yield_per_area.transformation';
import {getKPICell} from 'containers/si/programs/report/comparison-dashboard/components/KPICell';
import {transformCompareFilterStateToCommonFilters} from 'containers/si/programs/report/comparison-dashboard/request.helpers';

export const YieldPerAreaCell = ({
  filterState,
  measurement,
  isInitialState,
}: {
  filterState: ComparisonColumn | undefined;
  measurement: MeasurementEnum;
  isInitialState: boolean;
}) => {
  const commonFilters = useMemo(
    () => transformCompareFilterStateToCommonFilters(filterState),
    [filterState]
  );

  // Get Yield per area
  const {isLoading, topLevel: yieldPerAreaMetric} = useFetchAndTransformKPI({
    kpi: 'yield_per_area',
    summaries: [],
    commonFilters,
    kpiTransformer: makeYieldPerAreaMetrics(measurement),
    shouldFetch: filterState?.crop_types.length === 1,
  });

  const KPICell = useMemo(() => getKPICell(isInitialState), [isInitialState]);

  const cropYieldCellContent = useMemo(() => {
    if ((filterState?.crop_types.length ?? 0) !== 1) {
      return (
        <Typography variant="body1" color={'semanticPalette.text.secondary'} fontStyle={'italic'}>
          Select one crop to see yield
        </Typography>
      );
    } else {
      return yieldPerAreaMetric?.yieldPerArea.formattedValue;
    }
  }, [filterState?.crop_types.length, yieldPerAreaMetric]);

  return (
    <KPICell kpi="yield" isLoading={isLoading}>
      {cropYieldCellContent}
    </KPICell>
  );
};
