import {type MeasurementEnum} from '_common/utils/measurement-unit-options';

import type {OutcomeKPI} from 'containers/si/api/apiTypes';
import {bookValueEFUnit} from 'containers/si/programs/helpers/book_values.transformation';
import {FERT_EF_METRICS_UNIT_MAP} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.fert_emissions_factor.transformation';
import {GHG_EF_METRICS_UNIT_MAP} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.ghg_emissions_factor.transformation';
import {GHGMETRICS_UNIT_MAP} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.ghg_kg_per_m2.transformation';
import {NET_GHG_EF_METRICS_UNIT_MAP} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.net_ghg_emissions_factor.transformation';
import {NET_IMPACT_METRICS_UNIT_MAP} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.net_impact_kg_per_m2.transformation';
import {SOC_EF_METRICS_UNIT_MAP} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.soc_emissions_factor.transformation';
import {SOCMETRICS_UNIT_MAP} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.soc_kg_per_m2.transformation';
import {YIELD_PER_AREA_METRICS_UNIT_MAP} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.yield_per_area.transformation';
import type {UnitDetail, UnitType} from 'containers/si/utils/value.types';

export const MAX_COMPARE_ITEMS = 5;

export const kpiToUnitMap: Record<
  OutcomeKPI,
  (unitsSystem: MeasurementEnum) => UnitDetail<UnitType>
> = {
  ghg_emission: GHGMETRICS_UNIT_MAP.ghgMassPerArea,
  soc_sequestration: SOCMETRICS_UNIT_MAP.dSocMassPerArea,
  net_emission: NET_IMPACT_METRICS_UNIT_MAP.netImpact,
  ghg_emissions_factor: GHG_EF_METRICS_UNIT_MAP.ghgEmissionsPerYield,
  net_ghg_emissions_factor: NET_GHG_EF_METRICS_UNIT_MAP.netGhgEmissionsPerYield,
  fert_emissions_factor: FERT_EF_METRICS_UNIT_MAP.fertEmissionsPerYield,
  soc_emissions_factor: SOC_EF_METRICS_UNIT_MAP.socEmissionsPerYield,
  ef_book_values: bookValueEFUnit,
  yield: YIELD_PER_AREA_METRICS_UNIT_MAP.yieldPerArea,
};
