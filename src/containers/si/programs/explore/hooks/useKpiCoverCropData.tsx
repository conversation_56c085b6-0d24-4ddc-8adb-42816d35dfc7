import pick from 'lodash/pick';
import {useFeatureFlagEnabled} from 'posthog-js/react';
import {useMemo} from 'react';

import {useAppSelector} from 'store/useRedux';

import {selectMeasurement} from 'containers/login/login-selectors';
import {useFetchAndTransformKPI} from 'containers/si/hooks/useFetchAndTransformKPI';
import {KPI_FETCH_PARAMS_SUMMARIZE_BY_TIMETREND_ENABLED} from 'containers/si/programs/explore/constants';
import {CONTENT} from 'containers/si/programs/explore/content';
import {transformIMFilterStateToCommonFilters} from 'containers/si/programs/explore/helpers/request.helpers';
import {useKpiAnnualizedLookup} from 'containers/si/programs/explore/hooks/useKpiAnnualizedLookup';
import {useKpiBoundaryLevelLookup} from 'containers/si/programs/explore/hooks/useKpiBoundaryLevelLookup';
import {useKpiTopLevelLookup} from 'containers/si/programs/explore/hooks/useKpiTopLevelLookup';
import {useKpiUnitAndFormatterLookup} from 'containers/si/programs/explore/hooks/useKpiUnitAndFormatterLookup';
import type {
  FiltersState,
  KPIData,
  KPIReturnType,
  MetricLookupMap,
} from 'containers/si/programs/explore/types';
import {
  COVERCROPMETRICS_FORMATTER_MAP,
  COVERCROPMETRICS_UNIT_MAP,
  makeCoverCropExpectedMetrics,
  makeCoverCropExpectedWithNaExclusiveMetrics,
  makeCoverCropMetrics,
  type CoverCropMetrics,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.cover_crop.transformation';

const KPI = 'cover_crop';

const KPI_WITH_ALT_MAPPED_METRIC_KEYS: Array<keyof MetricLookupMap<CoverCropMetrics>> = [
  'primaryMetricKey',
  'uncertaintyMetricKey',
  'primaryAltMetricKey',
  'uncertaintyAltMetricKey',
  'secondaryMetricKey',
  'secondaryUncertaintyMetricKey',
  'trackedMetricKey',
  'unknownMetricKey',
  'notApplicableMetricKey',
];

const KPI_WITHOUT_ALT_MAPPED_METRIC_KEYS: Array<keyof MetricLookupMap<CoverCropMetrics>> = [
  'primaryMetricKey',
  'uncertaintyMetricKey',
  'secondaryMetricKey',
  'secondaryUncertaintyMetricKey',
  'trackedMetricKey',
  'unknownMetricKey',
  'notApplicableMetricKey',
];

const KPI_CONTENT = CONTENT[KPI];
const KPI_UNIT_MAP = COVERCROPMETRICS_UNIT_MAP;
const KPI_FORMATTER_MAP = COVERCROPMETRICS_FORMATTER_MAP;

export const useKpiCoverCropData = (filtersState: FiltersState): KPIReturnType => {
  const userUnitsSystem = useAppSelector(selectMeasurement);

  // TODO: SI-3446 remove feature flag and hard code transformer as constant
  const isCoverCropNaInclusiveEnabled = useFeatureFlagEnabled('si-covercrop-na-inclusive');
  // TODO: SI-3064 remove feature flag and hard code transformer as constant with constants above
  const isExpectedValuesEnabled = useFeatureFlagEnabled('si-expected-values');
  const isMosaicEnabled = useFeatureFlagEnabled('si-mosaic-be');

  // TODO: SI-3446 remove feature flag and hard code KPI_METRIC_MAP as constant
  const KPI_METRIC_MAP = useMemo(
    () =>
      isCoverCropNaInclusiveEnabled
        ? pick(KPI_CONTENT, KPI_WITH_ALT_MAPPED_METRIC_KEYS)
        : pick(KPI_CONTENT, KPI_WITHOUT_ALT_MAPPED_METRIC_KEYS),
    [isCoverCropNaInclusiveEnabled]
  );

  // TODO: SI-3446 remove feature flag and hard code kpiTransformer as constant
  const kpiTransformer = useMemo(() => {
    if (isExpectedValuesEnabled && isMosaicEnabled) {
      if (isCoverCropNaInclusiveEnabled) {
        return makeCoverCropExpectedWithNaExclusiveMetrics(userUnitsSystem);
      } else {
        return makeCoverCropExpectedMetrics(userUnitsSystem);
      }
    } else {
      return makeCoverCropMetrics(userUnitsSystem);
    }
  }, [isCoverCropNaInclusiveEnabled, isExpectedValuesEnabled, isMosaicEnabled, userUnitsSystem]);

  const commonFilters = transformIMFilterStateToCommonFilters(filtersState);

  const {isLoading, topLevel, annualizedSummary, boundarySummary, boundaryAnnualizedSummary} =
    useFetchAndTransformKPI({
      kpi: KPI,
      summaries: [
        'boundary',
        ...(filtersState.isTimeTrendEnabled ? KPI_FETCH_PARAMS_SUMMARIZE_BY_TIMETREND_ENABLED : []),
      ],
      commonFilters,
      kpiTransformer,
      shouldFetch: filtersState.kpi === KPI, // will not fire request if shouldFetch is false
    });

  const {unitLookup, formatterLookup} = useKpiUnitAndFormatterLookup({
    formatterMap: KPI_FORMATTER_MAP,
    metricMap: KPI_METRIC_MAP,
    unitMap: KPI_UNIT_MAP,
  });

  const annualizedLookup = useKpiAnnualizedLookup({
    annualizedSummary,
    boundaryAnnualizedSummary,
    metricMap: KPI_METRIC_MAP,
  });

  const boundaryLevelLookup = useKpiBoundaryLevelLookup({
    boundarySummary,
    metricMap: KPI_METRIC_MAP,
  });

  const topLevelLookup = useKpiTopLevelLookup({topLevel, metricMap: KPI_METRIC_MAP});

  const data: KPIData = {
    boundaryLevelLookup,
    topLevelLookup,
    annualizedLookup,
    unitLookup,
    formatterLookup,
  };

  return {
    isLoading,
    data,
  };
};
