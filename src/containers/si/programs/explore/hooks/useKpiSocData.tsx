import pick from 'lodash/pick';
import {useMemo} from 'react';

import {useAppSelector} from 'store/useRedux';

import {selectMeasurement} from 'containers/login/login-selectors';
import {useFetchAndTransformKPI} from 'containers/si/hooks/useFetchAndTransformKPI';
import {KPI_FETCH_PARAMS_SUMMARIZE_BY_TIMETREND_ENABLED} from 'containers/si/programs/explore/constants';
import {CONTENT} from 'containers/si/programs/explore/content';
import {transformIMFilterStateToCommonFilters} from 'containers/si/programs/explore/helpers/request.helpers';
import {useKpiAnnualizedLookup} from 'containers/si/programs/explore/hooks/useKpiAnnualizedLookup';
import {useKpiBoundaryLevelLookup} from 'containers/si/programs/explore/hooks/useKpiBoundaryLevelLookup';
import {useKpiTopLevelLookup} from 'containers/si/programs/explore/hooks/useKpiTopLevelLookup';
import {useKpiUnitAndFormatterLookup} from 'containers/si/programs/explore/hooks/useKpiUnitAndFormatterLookup';
import type {
  FiltersState,
  KPIData,
  KPIReturnType,
  MetricLookupMap,
} from 'containers/si/programs/explore/types';
import {
  makeSocMetrics,
  SOCMETRICS_FORMATTER_MAP,
  SOCMETRICS_UNIT_MAP,
  type SocMetrics,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.soc_kg_per_m2.transformation';

const KPI = 'soc_kg_per_m2';
const KPI_TRANSFORMER = makeSocMetrics;
const KPI_MAPPED_METRIC_KEYS: Array<keyof MetricLookupMap<SocMetrics>> = [
  'primaryMetricKey',
  'uncertaintyMetricKey',
];
const KPI_CONTENT = CONTENT[KPI];
const KPI_UNIT_MAP = SOCMETRICS_UNIT_MAP;
const KPI_FORMATTER_MAP = SOCMETRICS_FORMATTER_MAP;
const KPI_METRIC_MAP = pick(KPI_CONTENT, KPI_MAPPED_METRIC_KEYS);

export const useKpiSocData = (filtersState: FiltersState): KPIReturnType => {
  const commonFilters = transformIMFilterStateToCommonFilters(filtersState);
  const userUnitsSystem = useAppSelector(selectMeasurement);
  const kpiTransformer = useMemo(() => KPI_TRANSFORMER(userUnitsSystem), [userUnitsSystem]);

  const {isLoading, topLevel, annualizedSummary, boundarySummary, boundaryAnnualizedSummary} =
    useFetchAndTransformKPI({
      kpi: KPI,
      summaries: [
        'boundary',
        ...(filtersState.isTimeTrendEnabled ? KPI_FETCH_PARAMS_SUMMARIZE_BY_TIMETREND_ENABLED : []),
      ],
      commonFilters,
      kpiTransformer,
      shouldFetch: filtersState.kpi === KPI, // will not fire request if shouldFetch is false
    });

  const {unitLookup, formatterLookup} = useKpiUnitAndFormatterLookup({
    formatterMap: KPI_FORMATTER_MAP,
    metricMap: KPI_METRIC_MAP,
    unitMap: KPI_UNIT_MAP,
  });

  const annualizedLookup = useKpiAnnualizedLookup({
    annualizedSummary,
    boundaryAnnualizedSummary,
    metricMap: KPI_METRIC_MAP,
  });

  const boundaryLevelLookup = useKpiBoundaryLevelLookup({
    boundarySummary,
    metricMap: KPI_METRIC_MAP,
  });

  const topLevelLookup = useKpiTopLevelLookup({topLevel, metricMap: KPI_METRIC_MAP});

  const data: KPIData = {
    boundaryLevelLookup,
    topLevelLookup,
    annualizedLookup,
    unitLookup,
    formatterLookup,
  };

  return {
    isLoading,
    data,
  };
};
