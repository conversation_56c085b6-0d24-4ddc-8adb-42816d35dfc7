import pick from 'lodash/pick';

import {useFetchAndTransformKPI} from 'containers/si/hooks/useFetchAndTransformKPI';
import {KPI_FETCH_PARAMS_SUMMARIZE_BY_TIMETREND_ENABLED} from 'containers/si/programs/explore/constants';
import {CONTENT} from 'containers/si/programs/explore/content';
import {transformIMFilterStateToCommonFilters} from 'containers/si/programs/explore/helpers/request.helpers';
import {useKpiAnnualizedLookup} from 'containers/si/programs/explore/hooks/useKpiAnnualizedLookup';
import {useKpiBoundaryLevelLookup} from 'containers/si/programs/explore/hooks/useKpiBoundaryLevelLookup';
import {useKpiTopLevelLookup} from 'containers/si/programs/explore/hooks/useKpiTopLevelLookup';
import {useKpiUnitAndFormatterLookup} from 'containers/si/programs/explore/hooks/useKpiUnitAndFormatterLookup';
import type {
  FiltersState,
  KPIData,
  KPIReturnType,
  MetricLookupMap,
} from 'containers/si/programs/explore/types';
import {
  HEAT_STRESS_METRICS_FORMATTER_MAP,
  HEAT_STRESS_METRICS_UNIT_MAP,
  makeHeatStressMetrics,
  type HeatStressMetrics,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.heat_stress.transformation';

const KPI = 'days_of_heat_stress';
const KPI_heat_stress = 'heat_stress';
// ^^ This must be done for heat_stress as we're treating KPI subtypes as independent kpis
// to render them as separate items in our kpi dropdown

const KPI_MAPPED_METRIC_KEYS: Array<keyof MetricLookupMap<HeatStressMetrics>> = [
  'primaryMetricKey',
  'tierMetricKey',
];
const KPI_CONTENT = CONTENT[KPI];
const KPI_UNIT_MAP = HEAT_STRESS_METRICS_UNIT_MAP;
const KPI_FORMATTER_MAP = HEAT_STRESS_METRICS_FORMATTER_MAP;
const KPI_METRIC_MAP = pick(KPI_CONTENT, KPI_MAPPED_METRIC_KEYS);

export const useKpiDaysOfHeatStressData = (filtersState: FiltersState): KPIReturnType => {
  const commonFilters = transformIMFilterStateToCommonFilters(filtersState);

  const {isLoading, topLevel, annualizedSummary, boundarySummary, boundaryAnnualizedSummary} =
    useFetchAndTransformKPI({
      kpi: KPI_heat_stress,
      summaries: [
        'boundary',
        ...(filtersState.isTimeTrendEnabled ? KPI_FETCH_PARAMS_SUMMARIZE_BY_TIMETREND_ENABLED : []),
      ],
      commonFilters,
      kpiTransformer: makeHeatStressMetrics,
      shouldFetch: filtersState.kpi === KPI, // will not fire request if shouldFetch is false
    });

  const {unitLookup, formatterLookup} = useKpiUnitAndFormatterLookup({
    formatterMap: KPI_FORMATTER_MAP,
    metricMap: KPI_METRIC_MAP,
    unitMap: KPI_UNIT_MAP,
  });

  const annualizedLookup = useKpiAnnualizedLookup({
    annualizedSummary,
    boundaryAnnualizedSummary,
    metricMap: KPI_METRIC_MAP,
  });

  const boundaryLevelLookup = useKpiBoundaryLevelLookup({
    boundarySummary,
    metricMap: KPI_METRIC_MAP,
  });

  const topLevelLookup = useKpiTopLevelLookup({topLevel, metricMap: KPI_METRIC_MAP});

  const data: KPIData = {
    boundaryLevelLookup,
    topLevelLookup,
    annualizedLookup,
    unitLookup,
    formatterLookup,
  };

  return {
    isLoading,
    data,
  };
};
