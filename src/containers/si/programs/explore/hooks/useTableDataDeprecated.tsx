import React from 'react';

import {Stack, Typography, type Lab} from '@regrow-internal/design-system';

import {getTypedEntries} from '_common/utils/object';
import {capitalizeFirstLetter} from '_common/utils/string';
import {isDefined, isNil, isNonEmptyArray, isNonEmptyString} from '_common/utils/typeGuards';

import {KpiMetricsTableCell} from 'containers/si/programs/explore/components/TablePanel/KpiMetricsTableCell';
import {RegionTableCell} from 'containers/si/programs/explore/components/TablePanel/RegionTableCell';
import {UNAVAILABLE_DATA_CHOROPLETH_GROUP_LABEL} from 'containers/si/programs/explore/constants';
import {getNumberOrNullSortComparator} from 'containers/si/programs/explore/helpers/datagrid.helpers';
import {makeTooltipContent} from 'containers/si/programs/explore/helpers/metric.helpers';
import {type BoundaryData} from 'containers/si/programs/explore/hooks/useBoundaryData';
import type {
  ChoroplethLookup,
  FiltersState,
  KpiContent,
  KPIData,
  KpiUnitLookup,
  RowModelDeprecated,
  TablePanelContent,
  TopLevelTimeTrendChartContent,
} from 'containers/si/programs/explore/types';

type UseTableDataArgs = {
  annualizedKpiLookup: KPIData['annualizedLookup'];
  boundaryLookup: BoundaryData['data']['boundaryLevelLookup'];
  timeTrendLineChartColorLookup: TopLevelTimeTrendChartContent['lineChartColorLookup'] | null;
  choroplethLookup: ChoroplethLookup | null;
  content: KpiContent;
  filtersState: FiltersState;
  boundaryKpiLookup: KPIData['boundaryLevelLookup'];
  unitLookup: KPIData['unitLookup'];
};

/** @deprecated remove in SI-3290 */
export const useTableDataDeprecated = ({
  annualizedKpiLookup,
  boundaryKpiLookup,
  boundaryLookup,
  choroplethLookup,
  content,
  filtersState: {isTimeTrendEnabled, startYear, year: selectedYear},
  timeTrendLineChartColorLookup,
  unitLookup,
}: UseTableDataArgs): TablePanelContent | null => {
  const rows: Array<RowModelDeprecated> | null = React.useMemo(() => {
    if (isNil(boundaryLookup)) return null;

    return getTypedEntries(boundaryLookup).map(([boundaryId, boundaryData]) => {
      return {
        id: boundaryId,
        boundaryName: boundaryData['name'],
        boundaryTimeTrendColor: timeTrendLineChartColorLookup?.get(boundaryId) ?? null,
        choroplethColor: choroplethLookup?.choroplethLookup[boundaryId] ?? null,
        kpiMetrics: isTimeTrendEnabled
          ? annualizedKpiLookup?.boundaryLevel[boundaryId]?.[String(selectedYear)] ?? null
          : boundaryKpiLookup?.[boundaryId]?.kpiMetrics ?? null,
        startYearKpiMetrics: isTimeTrendEnabled
          ? annualizedKpiLookup?.boundaryLevel[boundaryId]?.[String(startYear)] ?? null
          : undefined,
      };
    });
  }, [
    annualizedKpiLookup?.boundaryLevel,
    boundaryKpiLookup,
    boundaryLookup,
    choroplethLookup?.choroplethLookup,
    isTimeTrendEnabled,
    selectedYear,
    startYear,
    timeTrendLineChartColorLookup,
  ]);

  const columns = React.useMemo(() => {
    const showTwoTimeTrendColumns =
      isTimeTrendEnabled && isDefined(startYear) && startYear !== selectedYear;

    const startYearKpiColumn = makeKpiColumn({
      headerName: `${startYear}`,
      field: 'startYearKpiMetrics',
      sortDirection: content?.sortDirection,
      unitLookup,
      width: 100,
    });

    const selectedYearKpiColumn = makeKpiColumn({
      headerName: isTimeTrendEnabled
        ? `${selectedYear}`
        : capitalizeFirstLetter(content?.label ?? ''),
      field: 'kpiMetrics',
      sortDirection: content?.sortDirection,
      unitLookup,
      width: showTwoTimeTrendColumns ? 100 : 200,
    });

    return [
      {
        headerName: 'Region name',
        field: 'boundaryName',
        flex: 1,
        minWidth: 125,
        renderCell: ({row: {boundaryName, boundaryTimeTrendColor}}) => (
          <RegionTableCell boundaryName={boundaryName} color={boundaryTimeTrendColor} />
        ),
        cellClassName: ({row: {choroplethColor}}) =>
          `choropleth--${choroplethColor?.cssSelector ?? UNAVAILABLE_DATA_CHOROPLETH_GROUP_LABEL}`,
      },
      ...(showTwoTimeTrendColumns
        ? [startYearKpiColumn, selectedYearKpiColumn]
        : [selectedYearKpiColumn]),
    ];
  }, [content, isTimeTrendEnabled, selectedYear, startYear, unitLookup]);

  const sorting = React.useMemo(() => {
    const _sorting: Lab.GridSortingInitialState = isNil(content)
      ? {sortModel: [{field: 'boundaryName', sort: 'asc'}]}
      : {sortModel: [{field: 'kpiMetrics', sort: content.sortDirection}]};

    return _sorting;
  }, [content]);

  if (isNil(rows)) return null;

  return {
    rows,
    columns,
    sorting,
    choroplethGroups: choroplethLookup?.choroplethGroups ?? null,
  };
};

const makeKpiColumn = ({
  headerName,
  field,
  sortDirection,
  unitLookup,
  width,
}: {
  headerName: Lab.GridColDef['headerName'];
  field: 'kpiMetrics' | 'startYearKpiMetrics';
  sortDirection: Lab.GridSortDirection;
  unitLookup: KpiUnitLookup | null;
  width?: Lab.GridColDef['width'];
}): Lab.GridColDef<RowModelDeprecated, number | null> => ({
  headerName,
  renderHeader: () => (
    <Stack alignItems="flex-end" whiteSpace="pre-wrap" textAlign="right">
      <Typography variant="h5">{headerName}</Typography>
      <Typography variant="body2" color="secondary" component="span">
        (
        {isNonEmptyString(unitLookup?.primary.unitName.abbr)
          ? unitLookup.primary.unitName.abbr
          : unitLookup?.primary.unitName.singular}
        )
      </Typography>
    </Stack>
  ),
  field,
  flex: 1,
  minWidth: width ?? 150,
  type: 'number',
  valueGetter: ({row}) => row[field]?.primary.value ?? null,
  renderCell: ({row}) => {
    const boundaryName = row['boundaryName'];
    const primaryMetric = row[field]?.primary;
    const uncertaintyMetric = row[field]?.uncertainty;
    const {
      primaryMetricWithUncertaintyText,
      primaryAltMetricWithUncertaintyText,
      secondaryText,
      secondaryWithoutTrackedText,
      unknownText,
      notApplicableText,
      tierText,
    } =
      makeTooltipContent({
        kpiMetrics: row[field],
        unitLookup,
      }) ?? {};

    // show tooltip if unexposed data values are set
    // primary and secondary values are exposed,
    // primaryAlt, unknown, notApplicable, tier are unexposed
    const tooltipSubtext = [
      primaryAltMetricWithUncertaintyText,
      unknownText,
      notApplicableText,
      tierText,
    ].some(isDefined)
      ? [
          primaryAltMetricWithUncertaintyText,
          secondaryText,
          unknownText,
          notApplicableText,
          tierText,
        ].filter(isDefined)
      : [];

    const tooltip =
      isDefined(primaryMetricWithUncertaintyText) && isNonEmptyArray(tooltipSubtext)
        ? {
            title: `${boundaryName}`,
            primaryText: primaryMetricWithUncertaintyText,
            subText: tooltipSubtext,
          }
        : undefined;

    return (
      <KpiMetricsTableCell
        metric={primaryMetric}
        uncertainty={uncertaintyMetric}
        subText={secondaryWithoutTrackedText}
        tooltip={tooltip}
      />
    );
  },
  // sorts null to bottom on default sort.
  // TODO: When DS is upgraded to MUI 7, use getSortComparator which exposes sortOrder so null can always be sorted to the bottom
  sortComparator: getNumberOrNullSortComparator(sortDirection),
});
