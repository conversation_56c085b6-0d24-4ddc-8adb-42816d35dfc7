import pick from 'lodash/pick';
import {useMemo} from 'react';

import {useAppSelector} from 'store/useRedux';

import {selectMeasurement} from 'containers/login/login-selectors';
import {useFetchAndTransformKPI} from 'containers/si/hooks/useFetchAndTransformKPI';
import {KPI_FETCH_PARAMS_SUMMARIZE_BY_TIMETREND_ENABLED} from 'containers/si/programs/explore/constants';
import {CONTENT} from 'containers/si/programs/explore/content';
import {transformIMFilterStateToCommonFilters} from 'containers/si/programs/explore/helpers/request.helpers';
import {useKpiAnnualizedLookup} from 'containers/si/programs/explore/hooks/useKpiAnnualizedLookup';
import {useKpiBoundaryLevelLookup} from 'containers/si/programs/explore/hooks/useKpiBoundaryLevelLookup';
import {useKpiTopLevelLookup} from 'containers/si/programs/explore/hooks/useKpiTopLevelLookup';
import {useKpiUnitAndFormatterLookup} from 'containers/si/programs/explore/hooks/useKpiUnitAndFormatterLookup';
import type {
  FiltersState,
  KPIData,
  KPIReturnType,
  MetricLookupMap,
} from 'containers/si/programs/explore/types';
import {
  GREENNESSLEVEL_FORMATTER_MAP,
  GREENNESSLEVEL_UNIT_MAP,
  makeGreennessLevelMetrics,
  type GreennessLevelMetrics,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.greenness_level_area_m2.transformation';

const KPI = 'greenness_level_area_m2_low';
const KPI_greencover = 'greenness_level_area_m2';
// ^^ This must be done for green cover as we're treating KPI subtypes as independent kpis
// to render them as separate items in our kpi dropdown

const KPI_MAPPED_METRIC_KEYS: Array<keyof MetricLookupMap<GreennessLevelMetrics>> = [
  'primaryMetricKey',
  'unknownMetricKey',
  'notApplicableMetricKey',
];
const KPI_CONTENT = CONTENT[KPI];
const KPI_UNIT_MAP = GREENNESSLEVEL_UNIT_MAP;
const KPI_FORMATTER_MAP = GREENNESSLEVEL_FORMATTER_MAP;
const KPI_METRIC_MAP = pick(KPI_CONTENT, KPI_MAPPED_METRIC_KEYS);

export const useKpiGreenCoverLowData = (filtersState: FiltersState): KPIReturnType => {
  const userUnitsSystem = useAppSelector(selectMeasurement);

  const kpiTransformer = useMemo(
    () => makeGreennessLevelMetrics(userUnitsSystem),
    [userUnitsSystem]
  );

  const commonFilters = transformIMFilterStateToCommonFilters(filtersState);

  const {isLoading, topLevel, annualizedSummary, boundarySummary, boundaryAnnualizedSummary} =
    useFetchAndTransformKPI({
      kpi: KPI_greencover,
      summaries: [
        'boundary',
        ...(filtersState.isTimeTrendEnabled ? KPI_FETCH_PARAMS_SUMMARIZE_BY_TIMETREND_ENABLED : []),
      ],
      commonFilters,
      kpiTransformer,
      shouldFetch: filtersState.kpi === KPI, // will not fire request if shouldFetch is false
    });

  const {unitLookup, formatterLookup} = useKpiUnitAndFormatterLookup({
    metricMap: {
      primaryMetricKey: KPI_METRIC_MAP.primaryMetricKey,
      // This hook requires secondary, tracked, unknown and not applicable to have the same unit details and formatter functions.
      // As such, we can manually set the secondary metric key, as the high green cover kpi does not have an explicit secondary metric,
      // but does have unknown and not applicable metric keys which require the secondary unit detail and formatter functions to be defined.
      secondaryMetricKey: KPI_METRIC_MAP.unknownMetricKey,
    },
    formatterMap: KPI_FORMATTER_MAP,
    unitMap: KPI_UNIT_MAP,
  });

  const annualizedLookup = useKpiAnnualizedLookup({
    annualizedSummary,
    boundaryAnnualizedSummary,
    metricMap: KPI_METRIC_MAP,
  });

  const boundaryLevelLookup = useKpiBoundaryLevelLookup({
    boundarySummary,
    metricMap: KPI_METRIC_MAP,
  });

  const topLevelLookup = useKpiTopLevelLookup({topLevel, metricMap: KPI_METRIC_MAP});

  const data: KPIData = {
    boundaryLevelLookup,
    topLevelLookup,
    annualizedLookup,
    unitLookup,
    formatterLookup,
  };

  return {
    isLoading,
    data,
  };
};
