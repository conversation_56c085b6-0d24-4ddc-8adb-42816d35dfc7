import pick from 'lodash/pick';

import {useFetchAndTransformKPI} from 'containers/si/hooks/useFetchAndTransformKPI';
import {KPI_FETCH_PARAMS_SUMMARIZE_BY_TIMETREND_ENABLED} from 'containers/si/programs/explore/constants';
import {CONTENT} from 'containers/si/programs/explore/content';
import {transformIMFilterStateToCommonFilters} from 'containers/si/programs/explore/helpers/request.helpers';
import {useKpiAnnualizedLookup} from 'containers/si/programs/explore/hooks/useKpiAnnualizedLookup';
import {useKpiBoundaryLevelLookup} from 'containers/si/programs/explore/hooks/useKpiBoundaryLevelLookup';
import {useKpiTopLevelLookup} from 'containers/si/programs/explore/hooks/useKpiTopLevelLookup';
import {useKpiUnitAndFormatterLookup} from 'containers/si/programs/explore/hooks/useKpiUnitAndFormatterLookup';
import type {
  FiltersState,
  KPIData,
  KPIReturnType,
  MetricLookupMap,
} from 'containers/si/programs/explore/types';
import {
  makeVolumeWeightedGhgMetrics,
  VOLUMEWEIGHTEDGHGMETRICS_FORMATTER_MAP,
  VOLUMEWEIGHTEDGHGMETRICS_UNIT_MAP,
  type VolumeWeightedGhgMetrics,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.volume_weighted_ghg_kg.transformation';

const KPI = 'volume_weighted_ghg_kg';
const KPI_TRANSFORMER = makeVolumeWeightedGhgMetrics;
const KPI_MAPPED_METRIC_KEYS: Array<keyof MetricLookupMap<VolumeWeightedGhgMetrics>> = [
  'primaryMetricKey',
];
const KPI_CONTENT = CONTENT[KPI];
const KPI_UNIT_MAP = VOLUMEWEIGHTEDGHGMETRICS_UNIT_MAP;
const KPI_FORMATTER_MAP = VOLUMEWEIGHTEDGHGMETRICS_FORMATTER_MAP;
const KPI_METRIC_MAP = pick(KPI_CONTENT, KPI_MAPPED_METRIC_KEYS);

export const useKpiVolumeCappedGhgData = (filtersState: FiltersState): KPIReturnType => {
  const kpiTransformer = KPI_TRANSFORMER;
  const commonFilters = transformIMFilterStateToCommonFilters(filtersState);

  const {isLoading, topLevel, annualizedSummary, boundarySummary, boundaryAnnualizedSummary} =
    useFetchAndTransformKPI({
      kpi: KPI,
      summaries: [
        'boundary',
        ...(filtersState.isTimeTrendEnabled ? KPI_FETCH_PARAMS_SUMMARIZE_BY_TIMETREND_ENABLED : []),
      ],

      commonFilters,
      kpiTransformer,
      shouldFetch: filtersState.kpi === KPI, // will not fire request if shouldFetch is false
    });

  const {unitLookup, formatterLookup} = useKpiUnitAndFormatterLookup({
    formatterMap: KPI_FORMATTER_MAP,
    metricMap: KPI_METRIC_MAP,
    unitMap: KPI_UNIT_MAP,
  });

  const annualizedLookup = useKpiAnnualizedLookup({
    annualizedSummary,
    boundaryAnnualizedSummary,
    metricMap: KPI_METRIC_MAP,
  });

  const boundaryLevelLookup = useKpiBoundaryLevelLookup({
    boundarySummary,
    metricMap: KPI_METRIC_MAP,
  });

  const topLevelLookup = useKpiTopLevelLookup({topLevel, metricMap: KPI_METRIC_MAP});

  const data: KPIData = {
    boundaryLevelLookup,
    topLevelLookup,
    annualizedLookup,
    unitLookup,
    formatterLookup,
  };

  return {
    isLoading,
    data,
  };
};
