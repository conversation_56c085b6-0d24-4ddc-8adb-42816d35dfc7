import fromPairs from 'lodash/fromPairs';
import pick from 'lodash/pick';
import {useCallback, useMemo} from 'react';

import {getTypedEntries, getTypedValues} from '_common/utils/object';
import {isNil} from '_common/utils/typeGuards';

import {useFetchAndTransformKPI} from 'containers/si/hooks/useFetchAndTransformKPI';
import {KPI_FETCH_PARAMS_SUMMARIZE_BY_TIMETREND_ENABLED} from 'containers/si/programs/explore/constants';
import {CONTENT} from 'containers/si/programs/explore/content';
import {transformIMFilterStateToCommonFilters} from 'containers/si/programs/explore/helpers/request.helpers';
import {useKpiAnnualizedLookup} from 'containers/si/programs/explore/hooks/useKpiAnnualizedLookup';
import {useKpiBoundaryLevelLookup} from 'containers/si/programs/explore/hooks/useKpiBoundaryLevelLookup';
import {useKpiTopLevelLookup} from 'containers/si/programs/explore/hooks/useKpiTopLevelLookup';
import {useKpiUnitAndFormatterLookup} from 'containers/si/programs/explore/hooks/useKpiUnitAndFormatterLookup';
import type {
  FiltersState,
  KPIData,
  KPIReturnType,
  MapContent,
  MetricLookupMap,
  TopLevelTimeTrendChartContent,
} from 'containers/si/programs/explore/types';
import {getChartDataRange} from 'containers/si/programs/helpers/chart.helpers';
import {
  BE_TO_FE_DROUGHT_INDEX_MAP,
  DROUGHT_METRICS_FORMATTER_MAP,
  DROUGHT_METRICS_UNIT_MAP,
  makeDroughtMetrics,
  type DroughtMetrics,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.drought_index.transformation';

const KPI = 'drought_index';
const KPI_TRANSFORMER = makeDroughtMetrics;
const KPI_MAPPED_METRIC_KEYS: Array<keyof MetricLookupMap<DroughtMetrics>> = [
  'primaryMetricKey',
  'tierMetricKey',
];
const KPI_CONTENT = CONTENT[KPI];
const KPI_UNIT_MAP = DROUGHT_METRICS_UNIT_MAP;
const KPI_FORMATTER_MAP = DROUGHT_METRICS_FORMATTER_MAP;
const KPI_METRIC_MAP = pick(KPI_CONTENT, KPI_MAPPED_METRIC_KEYS);

export const useKpiDroughtIndexData = (filtersState: FiltersState): KPIReturnType => {
  const commonFilters = transformIMFilterStateToCommonFilters(filtersState);

  const {isLoading, topLevel, annualizedSummary, boundarySummary, boundaryAnnualizedSummary} =
    useFetchAndTransformKPI({
      kpi: KPI,
      summaries: [
        'boundary',
        ...(filtersState.isTimeTrendEnabled ? KPI_FETCH_PARAMS_SUMMARIZE_BY_TIMETREND_ENABLED : []),
      ],
      commonFilters,
      kpiTransformer: KPI_TRANSFORMER,
      shouldFetch: filtersState.kpi === KPI, // will not fire request if shouldFetch is false
    });

  const {unitLookup, formatterLookup} = useKpiUnitAndFormatterLookup({
    formatterMap: KPI_FORMATTER_MAP,
    metricMap: KPI_METRIC_MAP,
    unitMap: KPI_UNIT_MAP,
  });

  const annualizedLookup = useKpiAnnualizedLookup({
    annualizedSummary,
    boundaryAnnualizedSummary,
    metricMap: KPI_METRIC_MAP,
  });

  const boundaryLevelLookup = useKpiBoundaryLevelLookup({
    boundarySummary,
    metricMap: KPI_METRIC_MAP,
  });

  const topLevelLookup = useKpiTopLevelLookup({topLevel, metricMap: KPI_METRIC_MAP});

  const data: KPIData = {
    boundaryLevelLookup,
    topLevelLookup,
    annualizedLookup,
    unitLookup,
    formatterLookup,
  };

  const topLevelTimeTrendContentOverrideCallback = useCallback(
    (content: TopLevelTimeTrendChartContent) => {
      const droughtIndexClassifications = getTypedValues(BE_TO_FE_DROUGHT_INDEX_MAP);
      const chartRange = getChartDataRange(droughtIndexClassifications);

      return {
        ...content,
        range: chartRange,
      };
    },
    []
  );

  const mapLayersContentOverrideCallback = useCallback(
    (content: MapContent['mapLayersContent']) => {
      if (isNil(content.choroplethGroups)) return content;

      const fe_to_be_lookup = fromPairs(
        getTypedEntries(BE_TO_FE_DROUGHT_INDEX_MAP).map(([backendValue, frontendValue]) => [
          frontendValue,
          Number(backendValue),
        ])
      );

      const choroplethGroups = content.choroplethGroups
        .filter(({inclusiveUpperBound}) => inclusiveUpperBound > BE_TO_FE_DROUGHT_INDEX_MAP[-1]) // remove normal/wet classification
        .map(
          // these values should not be undefined as content.choroplethGroups is derived from the values from BE_TO_FE_DROUGHT_INDEX_MAP
          // HOWEVER, if the choroplethGroup definition is changed, this may no longer be the case
          // this is set in CONTENT[drought_index].fixedChoroplethGroupValues and via useChoroplethLookup hook
          ({exclusiveLowerBound, inclusiveUpperBound, ...rest}, i) => ({
            exclusiveLowerBound:
              i === 0 // first lower bound is inclusive
                ? // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
                  fe_to_be_lookup[inclusiveUpperBound]!
                : // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
                  fe_to_be_lookup[exclusiveLowerBound]!,
            // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
            inclusiveUpperBound: fe_to_be_lookup[inclusiveUpperBound]!,
            ...rest,
          })
        );

      return {
        ...content,
        choroplethGroups,
      };
    },
    []
  );

  const contentOverrides: KPIReturnType['contentOverrides'] = useMemo(
    () => ({
      topLevelTimeTrendChartContent: topLevelTimeTrendContentOverrideCallback,
      mapLayersContent: mapLayersContentOverrideCallback,
    }),
    [mapLayersContentOverrideCallback, topLevelTimeTrendContentOverrideCallback]
  );

  return {
    isLoading,
    data,
    contentOverrides,
  };
};
