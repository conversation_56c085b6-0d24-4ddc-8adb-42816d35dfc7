import {useMemo} from 'react';

import {capitalizeFirstLetter} from '_common/utils/string';
import {isDefined, isNil} from '_common/utils/typeGuards';

import {
  CONTENT,
  TOP_PANEL_NOTIFICATION_ALL_MATCH_CONDITIONS,
} from 'containers/si/programs/explore/content';
import {useBoundaryData} from 'containers/si/programs/explore/hooks/useBoundaryData';
import {useChoroplethLookup} from 'containers/si/programs/explore/hooks/useChoroplethLookup';
import {useKpiAvgMaxTemperatureData} from 'containers/si/programs/explore/hooks/useKpiAvgMaxTemperatureData';
import {useKpiAvgTemperatureData} from 'containers/si/programs/explore/hooks/useKpiAvgTemperatureData';
import {useKpiCoverCropData} from 'containers/si/programs/explore/hooks/useKpiCoverCropData';
import {useKpiCroplandToGrasslandLulcData} from 'containers/si/programs/explore/hooks/useKpiCroplandToGrasslandLulcData';
import {useKpiDaysOfHeatStressData} from 'containers/si/programs/explore/hooks/useKpiDaysOfHeatStressData';
import {useKpiDroughtIndexData} from 'containers/si/programs/explore/hooks/useKpiDroughtIndexData';
import {useKpiGhgData} from 'containers/si/programs/explore/hooks/useKpiGhgData';
import {useKpiGhgEFData} from 'containers/si/programs/explore/hooks/useKpiGhgEFData';
import {useKpiGrasslandToCroplandLulcData} from 'containers/si/programs/explore/hooks/useKpiGrasslandToCroplandLulcData';
import {useKpiGreenCoverBareData} from 'containers/si/programs/explore/hooks/useKpiGreenCoverBareData';
import {useKpiGreenCoverHighData} from 'containers/si/programs/explore/hooks/useKpiGreenCoverHighData';
import {useKpiGreenCoverLowData} from 'containers/si/programs/explore/hooks/useKpiGreenCoverLowData';
import {useKpiGreenCoverModerateData} from 'containers/si/programs/explore/hooks/useKpiGreenCoverModerateData';
import {useKpiHeatStressOccurrencesData} from 'containers/si/programs/explore/hooks/useKpiHeatStressOccurrencesData';
import {useKpiNetGhgEFData} from 'containers/si/programs/explore/hooks/useKpiNetGhgEFData';
import {useKpiSocData} from 'containers/si/programs/explore/hooks/useKpiSocData';
import {useKpiSocEFData} from 'containers/si/programs/explore/hooks/useKpiSocEFData';
import {useKpiTillageData} from 'containers/si/programs/explore/hooks/useKpiTillageData';
import {useKpiVolumeCappedGhgData} from 'containers/si/programs/explore/hooks/useKpiVolumeCappedGhg';
import {useKpiVolumeCappedNetData} from 'containers/si/programs/explore/hooks/useKpiVolumeCappedNet';
import {useKpiVolumeCappedSocData} from 'containers/si/programs/explore/hooks/useKpiVolumeCappedSoc';
import {useKpiYieldPerAreaData} from 'containers/si/programs/explore/hooks/useKpiYieldPerAreaData';
import {useKpiYoyTemperatureData} from 'containers/si/programs/explore/hooks/useKpiYoyTemperatureData';
import {useMapTooltipContentLookup} from 'containers/si/programs/explore/hooks/useMapTooltipContentLookup';
import {useTableDataDeprecated} from 'containers/si/programs/explore/hooks/useTableDataDeprecated';
import {useTopLevelNumberContent} from 'containers/si/programs/explore/hooks/useTopLevelNumberContent';
import {useTopLevelTimeTrendChartContent} from 'containers/si/programs/explore/hooks/useTopLevelTimeTrendChartContent';
import type {
  AVAILABLE_KPIS,
  FiltersState,
  KpiContent,
  KPIReturnType,
  MapContent,
  MapLayersContent,
  MapLegendContent,
  TopLevelPanelContent,
  TopLevelTimeTrendChartContent,
} from 'containers/si/programs/explore/types';

// Note, this is filtersStateForFetch which only includes the valid subsection ids and valid crop ids (rather than the disabled id inclusive lists)
/** @deprecated remove in SI-3290 */
export const useDataDeprecated = (filtersState: FiltersState) => {
  const {kpi: selectedKpi} = filtersState;

  const {isLoading: isLoadingSubregion, data: boundary} = useBoundaryData(filtersState);
  const {isLoading: isLoadingCoverCrop, data: cover_crop} = useKpiCoverCropData(filtersState);
  const {isLoading: isLoadingGreenCoverHigh, data: greenness_level_area_m2_high} =
    useKpiGreenCoverHighData(filtersState);
  const {isLoading: isLoadingGreenCoverModerate, data: greenness_level_area_m2_moderate} =
    useKpiGreenCoverModerateData(filtersState);
  const {isLoading: isLoadingGreenCoverLow, data: greenness_level_area_m2_low} =
    useKpiGreenCoverLowData(filtersState);
  const {isLoading: isLoadingGreenCoverBare, data: greenness_level_area_m2_bare} =
    useKpiGreenCoverBareData(filtersState);
  const {isLoading: isLoadingGhgPerArea, data: ghg_kg_per_m2} = useKpiGhgData(filtersState);
  const {isLoading: isLoadingSocPerArea, data: soc_kg_per_m2} = useKpiSocData(filtersState);
  const {isLoading: isLoadingGhgEf, data: ghg_emissions_factor} = useKpiGhgEFData(filtersState);
  const {isLoading: isLoadingSocEf, data: soc_emissions_factor} = useKpiSocEFData(filtersState);
  const {isLoading: isLoadingNetGhgEf, data: net_ghg_emissions_factor} =
    useKpiNetGhgEFData(filtersState);
  const {isLoading: isLoadingTillage, data: tillage} = useKpiTillageData(filtersState);
  const {isLoading: isLoadingG2cLulc, data: grassland_to_cropland_lulc} =
    useKpiGrasslandToCroplandLulcData(filtersState);
  const {isLoading: isLoadingC2gLulc, data: cropland_to_grassland_lulc} =
    useKpiCroplandToGrasslandLulcData(filtersState);
  const {isLoading: isLoadingVolumeWeightedGhgKg, data: volume_weighted_ghg_kg} =
    useKpiVolumeCappedGhgData(filtersState);
  const {isLoading: isLoadingVolumeWeightedSocKg, data: volume_weighted_soc_kg} =
    useKpiVolumeCappedSocData(filtersState);
  const {isLoading: isLoadingVolumeWeightedNetKg, data: volume_weighted_net_kg} =
    useKpiVolumeCappedNetData(filtersState);
  const {isLoading: isLoadingYieldPerArea, data: yield_per_area} =
    useKpiYieldPerAreaData(filtersState);
  const {
    isLoading: isLoadingDroughtIndex,
    data: drought_index,
    contentOverrides: drought_index_content_overrides,
  } = useKpiDroughtIndexData(filtersState);
  const {isLoading: isLoadingAvgTemperature, data: avg_temperature} =
    useKpiAvgTemperatureData(filtersState);
  const {isLoading: isLoadingAvgMaxTemperature, data: avg_max_temperature} =
    useKpiAvgMaxTemperatureData(filtersState);
  const {isLoading: isLoadingYoyTemperatureData, data: yoy_temperature_delta} =
    useKpiYoyTemperatureData(filtersState);
  const {isLoading: isLoadingDaysOfHeatStress, data: days_of_heat_stress} =
    useKpiDaysOfHeatStressData(filtersState);
  const {isLoading: isLoadingHeatStressOccurrences, data: heat_stress_occurrences} =
    useKpiHeatStressOccurrencesData(filtersState);

  const {
    topLevelLookup: topLevelKpiLookup,
    annualizedLookup: annualizedKpiLookup,
    boundaryLevelLookup: boundaryKpiLookup,
    formatterLookup,
    unitLookup,
    content,
  } = useMemo(() => {
    if (isNil(selectedKpi)) {
      return {
        boundaryLevelLookup: null,
        annualizedLookup: null,
        topLevelLookup: null,
        formatterLookup: null,
        unitLookup: null,
        content: null,
      };
    }

    const kpis_ = {
      cover_crop,
      tillage,
      greenness_level_area_m2_high,
      greenness_level_area_m2_moderate,
      greenness_level_area_m2_low,
      greenness_level_area_m2_bare,
      ghg_kg_per_m2,
      soc_kg_per_m2,
      ghg_emissions_factor,
      soc_emissions_factor,
      net_ghg_emissions_factor,
      grassland_to_cropland_lulc,
      cropland_to_grassland_lulc,
      volume_weighted_ghg_kg,
      volume_weighted_soc_kg,
      volume_weighted_net_kg,
      yield_per_area,
      drought_index,
      avg_temperature,
      avg_max_temperature,
      yoy_temperature_delta,
      days_of_heat_stress,
      heat_stress_occurrences,
    };

    const content_: KpiContent = CONTENT[selectedKpi];

    return {
      ...kpis_[selectedKpi],
      content: content_,
    };
  }, [
    selectedKpi,
    cover_crop,
    tillage,
    greenness_level_area_m2_high,
    greenness_level_area_m2_moderate,
    greenness_level_area_m2_low,
    greenness_level_area_m2_bare,
    ghg_kg_per_m2,
    soc_kg_per_m2,
    ghg_emissions_factor,
    soc_emissions_factor,
    net_ghg_emissions_factor,
    grassland_to_cropland_lulc,
    cropland_to_grassland_lulc,
    volume_weighted_ghg_kg,
    volume_weighted_soc_kg,
    volume_weighted_net_kg,
    yield_per_area,
    drought_index,
    avg_temperature,
    avg_max_temperature,
    yoy_temperature_delta,
    days_of_heat_stress,
    heat_stress_occurrences,
  ]);

  // This has not be done in the useData hook since this approach seems janky
  // instead we should allow each kpi data hook to form it's own KPIReturnType
  // and manage overrides to content definition within the specific kpi hook
  // similar to the Report UI
  const contentOverrides = useMemo(() => {
    if (isNil(selectedKpi)) return {};

    const contentOverrides_: Partial<Record<AVAILABLE_KPIS, KPIReturnType['contentOverrides']>> = {
      drought_index: drought_index_content_overrides,
    };

    return contentOverrides_[selectedKpi] ?? {};
  }, [drought_index_content_overrides, selectedKpi]);

  const topLevelNumberContent = useTopLevelNumberContent({
    filtersState,
    topLevelKpiLookup,
    unitLookup,
  });

  const topLevelTimeTrendChartContent_ = useTopLevelTimeTrendChartContent({
    annualizedKpiLookup,
    filtersState,
    boundaryLookup: boundary.boundaryLevelLookup,
    formatterLookup,
    unitLookup,
  });

  const topLevelTimeTrendChartContent: TopLevelTimeTrendChartContent | null = useMemo(() => {
    if (
      isNil(contentOverrides.topLevelTimeTrendChartContent) ||
      isNil(topLevelTimeTrendChartContent_)
    )
      return topLevelTimeTrendChartContent_;

    return contentOverrides.topLevelTimeTrendChartContent(topLevelTimeTrendChartContent_);
  }, [contentOverrides, topLevelTimeTrendChartContent_]);

  const topLevelPanelContent = useMemo(() => {
    const topLevelNotifications: Array<string> = [];

    TOP_PANEL_NOTIFICATION_ALL_MATCH_CONDITIONS.forEach(({rule, message}) => {
      if (
        rule({
          boundaryType: filtersState.boundaryType,
          isTimeTrendEnabled: filtersState.isTimeTrendEnabled,
          subsectionIds: filtersState.subsectionIds,
        })
      )
        topLevelNotifications.push(message);
    });

    const topLevelPanelContent_: TopLevelPanelContent | null = isNil(content)
      ? null
      : {
          title: `${capitalizeFirstLetter(content.topLevelTitle)}${
            filtersState.isTimeTrendEnabled ? ' over time' : ''
          }`,
          description: content.topLevelDescription,
          notifications: topLevelNotifications,
          topLevelNumberContent,
          topLevelTimeTrendChartContent,
        };

    return topLevelPanelContent_;
  }, [
    content,
    filtersState.boundaryType,
    filtersState.isTimeTrendEnabled,
    filtersState.subsectionIds,
    topLevelNumberContent,
    topLevelTimeTrendChartContent,
  ]);

  const choroplethLookup = useChoroplethLookup({
    boundaryLookup: boundary.boundaryLevelLookup,
    boundaryKpiLookup,
    heatMapCategoryColorKey: content?.categoryColorKey,
    labelFormatter: isDefined(formatterLookup?.primary)
      ? (v: number) => `${formatterLookup.primary(v)} ${unitLookup?.primary.unitName.abbr}`
      : undefined,
    sortDirection: content?.sortDirection,
    choroplethGroupMode: content?.choroplethGroupMode,
    fixedChoroplethOptions: content?.fixedChoroplethOptions,
    biasedChoroplethOptions: content?.biasedChoroplethOptions,
  });

  /** @deprecated remove in SI-3290 */
  const tablePanelContent = useTableDataDeprecated({
    annualizedKpiLookup,
    timeTrendLineChartColorLookup: topLevelTimeTrendChartContent?.lineChartColorLookup ?? null,
    content,
    choroplethLookup,
    boundaryKpiLookup,
    boundaryLookup: boundary.boundaryLevelLookup,
    filtersState,
    unitLookup,
  });

  const mapTooltipContentLookup = useMapTooltipContentLookup({
    timeTrendLineChartColorLookup: topLevelTimeTrendChartContent?.lineChartColorLookup ?? null,
    content,
    choroplethLookup,
    boundaryKpiLookup,
    boundaryLookup: boundary.boundaryLevelLookup,
    unitLookup,
  });

  const mapLegendContent = useMemo(() => {
    const choroplethGroups_ = choroplethLookup?.choroplethGroups;

    if (isNil(content) || isNil(choroplethGroups_) || isNil(formatterLookup) || isNil(unitLookup))
      return null;

    const min = choroplethGroups_.at(0)?.exclusiveLowerBound;
    const max = choroplethGroups_.at(-1)?.inclusiveUpperBound;

    const labelMin = isNil(min) ? min : formatterLookup.primary(min);
    const labelMax = isNil(max) ? max : formatterLookup.primary(max);

    const mapLegendContent_: MapLegendContent = {
      choroplethGroups: choroplethGroups_,
      title: unitLookup.primary.unitName.abbr,
      labelMin,
      labelMax,
    };

    return mapLegendContent_;
  }, [choroplethLookup?.choroplethGroups, content, formatterLookup, unitLookup]);

  const mapLayersContent: MapLayersContent = useMemo(() => {
    const mapLayersContent_ = {
      choroplethLookup: choroplethLookup?.choroplethLookup ?? null,
      choroplethGroups: mapLegendContent?.choroplethGroups ?? null,
      heatMapCategoryColorKey: content?.categoryColorKey ?? null,
    };

    return isNil(contentOverrides.mapLayersContent)
      ? mapLayersContent_
      : contentOverrides.mapLayersContent(mapLayersContent_);
  }, [
    choroplethLookup?.choroplethLookup,
    content?.categoryColorKey,
    contentOverrides,
    mapLegendContent?.choroplethGroups,
  ]);

  const mapContent: MapContent = useMemo(
    () => ({
      filtersState,
      mapLayersContent,
      mapLegendContent,
      mapTooltipContentLookup,
    }),
    [filtersState, mapLayersContent, mapLegendContent, mapTooltipContentLookup]
  );

  return {
    isLoading:
      isLoadingSubregion ||
      isLoadingCoverCrop ||
      isLoadingGreenCoverHigh ||
      isLoadingGreenCoverModerate ||
      isLoadingGreenCoverLow ||
      isLoadingGreenCoverBare ||
      isLoadingGhgPerArea ||
      isLoadingSocPerArea ||
      isLoadingGhgEf ||
      isLoadingSocEf ||
      isLoadingNetGhgEf ||
      isLoadingTillage ||
      isLoadingG2cLulc ||
      isLoadingC2gLulc ||
      isLoadingVolumeWeightedGhgKg ||
      isLoadingVolumeWeightedSocKg ||
      isLoadingVolumeWeightedNetKg ||
      isLoadingYieldPerArea ||
      isLoadingDroughtIndex ||
      isLoadingAvgTemperature ||
      isLoadingAvgMaxTemperature ||
      isLoadingYoyTemperatureData ||
      isLoadingDaysOfHeatStress ||
      isLoadingHeatStressOccurrences,
    topLevelPanelContent,
    tablePanelContent,
    mapContent,
    content,
  };
};
