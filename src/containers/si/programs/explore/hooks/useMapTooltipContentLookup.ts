import fromPairs from 'lodash/fromPairs';
import {useMemo} from 'react';

import {getTypedEntries} from '_common/utils/object';
import {isDefined, isNil} from '_common/utils/typeGuards';

import {makeTooltipContent} from 'containers/si/programs/explore/helpers/metric.helpers';
import {type BoundaryData} from 'containers/si/programs/explore/hooks/useBoundaryData';
import type {
  ChoroplethLookup,
  KpiContent,
  KPIData,
  MapTooltipContent,
  MapTooltipContentLookup,
  TopLevelTimeTrendChartContent,
} from 'containers/si/programs/explore/types';

type UseMapTooltipContentLookupArgs = {
  content: KpiContent;
  timeTrendLineChartColorLookup: TopLevelTimeTrendChartContent['lineChartColorLookup'] | null;
  choroplethLookup: ChoroplethLookup | null;
  boundaryKpiLookup: KPIData['boundaryLevelLookup'];
  boundaryLookup: BoundaryData['data']['boundaryLevelLookup'];
  unitLookup: KPIData['unitLookup'];
};

export const useMapTooltipContentLookup = ({
  timeTrendLineChartColorLookup,
  content,
  choroplethLookup,
  boundaryKpiLookup,
  boundaryLookup,
  unitLookup,
}: UseMapTooltipContentLookupArgs): MapTooltipContentLookup | null => {
  const mapTooltipLookup = useMemo(() => {
    if (isNil(content) || isNil(boundaryLookup)) return null;

    return fromPairs(
      getTypedEntries(boundaryLookup).map(([boundaryId, boundaryData]) => {
        const {
          primaryMetricWithUncertaintyText,
          primaryAltMetricWithUncertaintyText,
          secondaryText,
          unknownText,
          notApplicableText,
          tierText,
        } =
          makeTooltipContent({
            kpiMetrics: boundaryKpiLookup?.[boundaryId]?.kpiMetrics ?? null,
            unitLookup,
          }) ?? {};

        const mapTooltipContent: MapTooltipContent = {
          boundaryName: boundaryData['name'],
          boundaryTimeTrendColor: timeTrendLineChartColorLookup?.get(boundaryId) ?? null,
          choropleth: choroplethLookup?.choroplethLookup[boundaryId] ?? null,
          ...(isDefined(primaryMetricWithUncertaintyText)
            ? {
                kpiTooltip: {
                  primaryText: primaryMetricWithUncertaintyText,
                  subText: [
                    primaryAltMetricWithUncertaintyText,
                    secondaryText,
                    unknownText,
                    notApplicableText,
                    tierText,
                  ].filter(isDefined),
                },
              }
            : {}),
        };

        return [boundaryId, mapTooltipContent];
      })
    );
  }, [
    boundaryKpiLookup,
    boundaryLookup,
    choroplethLookup?.choroplethLookup,
    content,
    timeTrendLineChartColorLookup,
    unitLookup,
  ]);

  return mapTooltipLookup;
};
