import {MeasurementEnum} from '_common/utils/measurement-unit-options';

import type {AnnualizedKpiMetricsLookup, KPIData} from 'containers/si/programs/explore/types';
import {
  COVERCROPMETRICS_FORMATTER_MAP,
  COVERCROPMETRICS_UNIT_MAP,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.cover_crop.transformation';

/**
 * Program: 110 (Regen Collective)
 * Year: 2022
 * Subsections: All Program available subsections
 * Crops: All SI available crops
 * KPI: Cover cropping
 */

export const COVERCROP_METRICUNITS_KPIDATA_MOCK: KPIData = {
  boundaryLevelLookup: {
    '872': {
      choroplethValue: 0.03901973067243741,
      kpiMetrics: {
        primary: {
          value: 0.03901973067243741,
          unit: 'unit-interval',
          formattedValue: '3.9%',
        },
        secondary: {
          value: 1071214.0266875732,
          unit: 'ha',
          formattedValue: '1.1M',
        },
        tracked: {
          value: 27453137.380167846,
          unit: 'ha',
          formattedValue: '27M',
        },
        unknown: {
          value: 1773.4058607421875,
          unit: 'ha',
          formattedValue: '1.8K',
        },
        notApplicable: {
          value: 0,
          unit: 'ha',
          formattedValue: '0',
        },
      },
    },
    '873': {
      choroplethValue: 0.020068287192039458,
      kpiMetrics: {
        primary: {
          value: 0.020068287192039458,
          unit: 'unit-interval',
          formattedValue: '2%',
        },
        secondary: {
          value: 18392.42181118164,
          unit: 'ha',
          formattedValue: '18K',
        },
        tracked: {
          value: 916491.8577843262,
          unit: 'ha',
          formattedValue: '916K',
        },
        unknown: {
          value: 36862.91620778808,
          unit: 'ha',
          formattedValue: '37K',
        },
        notApplicable: {
          value: 676199.6444428955,
          unit: 'ha',
          formattedValue: '676K',
        },
      },
    },
    '874': {
      choroplethValue: 0.04552999575294977,
      kpiMetrics: {
        primary: {
          value: 0.04552999575294977,
          unit: 'unit-interval',
          formattedValue: '4.6%',
        },
        secondary: {
          value: 632886.6231074951,
          unit: 'ha',
          formattedValue: '633K',
        },
        tracked: {
          value: 13900432.289552588,
          unit: 'ha',
          formattedValue: '14M',
        },
        unknown: {
          value: 11245.446632617188,
          unit: 'ha',
          formattedValue: '11K',
        },
        notApplicable: {
          value: 0,
          unit: 'ha',
          formattedValue: '0',
        },
      },
    },
    '875': {
      choroplethValue: 0.05331932281914219,
      kpiMetrics: {
        primary: {
          value: 0.05331932281914219,
          unit: 'unit-interval',
          formattedValue: '5.3%',
        },
        secondary: {
          value: 669669.5204800019,
          unit: 'ha',
          formattedValue: '670K',
        },
        tracked: {
          value: 12559602.880769964,
          unit: 'ha',
          formattedValue: '13M',
        },
        unknown: {
          value: 0,
          unit: 'ha',
          formattedValue: '0',
        },
        notApplicable: {
          value: 0,
          unit: 'ha',
          formattedValue: '0',
        },
      },
    },
    '16648': {
      choroplethValue: 0.04983983681698762,
      kpiMetrics: {
        primary: {
          value: 0.04983983681698762,
          unit: 'unit-interval',
          formattedValue: '5%',
        },
        secondary: {
          value: 170745.61821933594,
          unit: 'ha',
          formattedValue: '171K',
        },
        tracked: {
          value: 3425886.3817374757,
          unit: 'ha',
          formattedValue: '3.4M',
        },
        unknown: {
          value: 47.2033125,
          unit: 'ha',
          formattedValue: '47',
        },
        notApplicable: {
          value: 0,
          unit: 'ha',
          formattedValue: '0',
        },
      },
    },
  },
  topLevelLookup: {
    primary: {
      value: 0.043994231889487055,
      unit: 'unit-interval',
      formattedValue: '4.4%',
    },
    secondary: {
      value: 2562908.210305588,
      unit: 'ha',
      formattedValue: '2.6M',
    },
    tracked: {
      value: 58255550.79001221,
      unit: 'ha',
      formattedValue: '58M',
    },
    unknown: {
      value: 49928.972013647464,
      unit: 'ha',
      formattedValue: '50K',
    },
    notApplicable: {
      value: 676199.6444428955,
      unit: 'ha',
      formattedValue: '676K',
    },
  },
  annualizedLookup: null,
  unitLookup: {
    primary: {
      unit: 'unit-interval',
      unitName: {
        singular: '% of cropland area practicing',
        plural: '% of cropland area practicing',
        abbr: 'adoption',
      },
    },
    secondary: {
      unit: 'ha',
      unitName: {
        singular: 'hectare',
        plural: 'hectares',
        abbr: 'ha',
      },
    },
  },
  formatterLookup: {
    primary: COVERCROPMETRICS_FORMATTER_MAP.adoption,
    secondary: COVERCROPMETRICS_FORMATTER_MAP.covercroppedArea,
  },
};

const COVERCROP_METRICUNITS_SUBREGION_ANNUALIZED_METRIC_LOOKUP_DETAIL_BOUNDARY_LEVEL_MOCK: AnnualizedKpiMetricsLookup['boundaryLevel'] =
  {
    '872': {
      '2015': {
        primary: {
          value: 0.03509489717548463,
          unit: 'unit-interval',
          formattedValue: '3.5%',
        },
        secondary: {
          value: 2371246.186037572,
          unit: 'ac',
          formattedValue: '2.4M',
        },
      },

      '2016': {
        primary: {
          value: 0.036855379715275645,
          unit: 'unit-interval',
          formattedValue: '3.7%',
        },
        secondary: {
          value: 2520251.9431804335,
          unit: 'ac',
          formattedValue: '2.5M',
        },
      },

      '2017': {
        primary: {
          value: 0.044807146011430876,
          unit: 'unit-interval',
          formattedValue: '4.5%',
        },
        secondary: {
          value: 3115664.620952426,
          unit: 'ac',
          formattedValue: '3.1M',
        },
      },

      '2018': {
        primary: {
          value: 0.04640948782772285,
          unit: 'unit-interval',
          formattedValue: '4.6%',
        },
        secondary: {
          value: 3232005.76641801,
          unit: 'ac',
          formattedValue: '3.2M',
        },
      },

      '2019': {
        primary: {
          value: 0.07181662048617232,
          unit: 'unit-interval',
          formattedValue: '7.2%',
        },
        secondary: {
          value: 4658377.132815262,
          unit: 'ac',
          formattedValue: '4.7M',
        },
      },

      '2020': {
        primary: {
          value: 0.05224456093117676,
          unit: 'unit-interval',
          formattedValue: '5.2%',
        },
        secondary: {
          value: 3620735.3759623743,
          unit: 'ac',
          formattedValue: '3.6M',
        },
      },

      '2021': {
        primary: {
          value: 0.05319761816474185,
          unit: 'unit-interval',
          formattedValue: '5.3%',
        },
        secondary: {
          value: 3652480.3867655084,
          unit: 'ac',
          formattedValue: '3.7M',
        },
      },

      '2022': {
        primary: {
          value: 0.05487274333312234,
          unit: 'unit-interval',
          formattedValue: '5.5%',
        },
        secondary: {
          value: 3764930.722640397,
          unit: 'ac',
          formattedValue: '3.8M',
        },
      },
    },

    '873': {
      '2015': {
        primary: {
          value: 0.011505639906717846,
          unit: 'unit-interval',
          formattedValue: '1.2%',
        },
        secondary: {
          value: 61929.91910340356,
          unit: 'ac',
          formattedValue: '62K',
        },
      },

      '2016': {
        primary: {
          value: 0.00945868475577783,
          unit: 'unit-interval',
          formattedValue: '0.9%',
        },
        secondary: {
          value: 46149.24377618847,
          unit: 'ac',
          formattedValue: '46K',
        },
      },

      '2017': {
        primary: {
          value: 0.021931204054403904,
          unit: 'unit-interval',
          formattedValue: '2.2%',
        },
        secondary: {
          value: 123975.5266815292,
          unit: 'ac',
          formattedValue: '124K',
        },
      },

      '2018': {
        primary: {
          value: 0.011495383102313555,
          unit: 'unit-interval',
          formattedValue: '1.1%',
        },
        secondary: {
          value: 66174.58399475804,
          unit: 'ac',
          formattedValue: '66K',
        },
      },

      '2019': {
        primary: {
          value: 0.017014847734470515,
          unit: 'unit-interval',
          formattedValue: '1.7%',
        },
        secondary: {
          value: 103884.21472742787,
          unit: 'ac',
          formattedValue: '104K',
        },
      },

      '2020': {
        primary: {
          value: 0.019274736883876646,
          unit: 'unit-interval',
          formattedValue: '1.9%',
        },
        secondary: {
          value: 123675.18224207088,
          unit: 'ac',
          formattedValue: '124K',
        },
      },

      '2021': {
        primary: {
          value: 0.011476279560778398,
          unit: 'unit-interval',
          formattedValue: '1.1%',
        },
        secondary: {
          value: 77002.49497569584,
          unit: 'ac',
          formattedValue: '77K',
        },
      },

      '2022': {
        primary: {
          value: 0.005511945438944393,
          unit: 'unit-interval',
          formattedValue: '0.6%',
        },
        secondary: {
          value: 32917.13739792055,
          unit: 'ac',
          formattedValue: '33K',
        },
      },
    },

    '874': {
      '2015': {
        primary: {
          value: 0.029368591004814704,
          unit: 'unit-interval',
          formattedValue: '2.9%',
        },
        secondary: {
          value: 1260356.5425505135,
          unit: 'ac',
          formattedValue: '1.3M',
        },
      },

      '2016': {
        primary: {
          value: 0.035468032832141075,
          unit: 'unit-interval',
          formattedValue: '3.5%',
        },
        secondary: {
          value: 1546949.3174096758,
          unit: 'ac',
          formattedValue: '1.5M',
        },
      },

      '2017': {
        primary: {
          value: 0.02928738016775121,
          unit: 'unit-interval',
          formattedValue: '2.9%',
        },
        secondary: {
          value: 1316724.075997906,
          unit: 'ac',
          formattedValue: '1.3M',
        },
      },

      '2018': {
        primary: {
          value: 0.02763780713657303,
          unit: 'unit-interval',
          formattedValue: '2.8%',
        },
        secondary: {
          value: 1259472.6401420792,
          unit: 'ac',
          formattedValue: '1.3M',
        },
      },

      '2019': {
        primary: {
          value: 0.053155191353714465,
          unit: 'unit-interval',
          formattedValue: '5.3%',
        },
        secondary: {
          value: 2181104.6803461765,
          unit: 'ac',
          formattedValue: '2.2M',
        },
      },

      '2020': {
        primary: {
          value: 0.0431443718122499,
          unit: 'unit-interval',
          formattedValue: '4.3%',
        },
        secondary: {
          value: 1793299.2863516489,
          unit: 'ac',
          formattedValue: '1.8M',
        },
      },

      '2021': {
        primary: {
          value: 0.044265883647254686,
          unit: 'unit-interval',
          formattedValue: '4.4%',
        },
        secondary: {
          value: 2022200.0962080886,
          unit: 'ac',
          formattedValue: '2M',
        },
      },

      '2022': {
        primary: {
          value: 0.036194594310697326,
          unit: 'unit-interval',
          formattedValue: '3.6%',
        },
        secondary: {
          value: 1538204.9783713329,
          unit: 'ac',
          formattedValue: '1.5M',
        },
      },
    },

    '875': {
      '2015': {
        primary: {
          value: 0.023140209591311866,
          unit: 'unit-interval',
          formattedValue: '2.3%',
        },
        secondary: {
          value: 698992.1853076225,
          unit: 'ac',
          formattedValue: '699K',
        },
      },

      '2016': {
        primary: {
          value: 0.020916738268249645,
          unit: 'unit-interval',
          formattedValue: '2.1%',
        },
        secondary: {
          value: 666666.7050215348,
          unit: 'ac',
          formattedValue: '667K',
        },
      },

      '2017': {
        primary: {
          value: 0.03685290838102546,
          unit: 'unit-interval',
          formattedValue: '3.7%',
        },
        secondary: {
          value: 1190151.2949312406,
          unit: 'ac',
          formattedValue: '1.2M',
        },
      },

      '2018': {
        primary: {
          value: 0.046684693288150565,
          unit: 'unit-interval',
          formattedValue: '4.7%',
        },
        secondary: {
          value: 1511953.198844097,
          unit: 'ac',
          formattedValue: '1.5M',
        },
      },

      '2019': {
        primary: {
          value: 0.051225050475573075,
          unit: 'unit-interval',
          formattedValue: '5.1%',
        },
        secondary: {
          value: 1642185.2832797423,
          unit: 'ac',
          formattedValue: '1.6M',
        },
      },

      '2020': {
        primary: {
          value: 0.04061677097978603,
          unit: 'unit-interval',
          formattedValue: '4.1%',
        },
        secondary: {
          value: 1295716.7881728932,
          unit: 'ac',
          formattedValue: '1.3M',
        },
      },

      '2021': {
        primary: {
          value: 0.05094761965609893,
          unit: 'unit-interval',
          formattedValue: '5.1%',
        },
        secondary: {
          value: 1616979.6447788004,
          unit: 'ac',
          formattedValue: '1.6M',
        },
      },

      '2022': {
        primary: {
          value: 0.053319322819141764,
          unit: 'unit-interval',
          formattedValue: '5.3%',
        },
        secondary: {
          value: 1654787.8217388995,
          unit: 'ac',
          formattedValue: '1.7M',
        },
      },
    },

    '16648': {
      '2015': {
        primary: {
          value: 0.036289428813872475,
          unit: 'unit-interval',
          formattedValue: '3.6%',
        },
        secondary: {
          value: 334069.73343340127,
          unit: 'ac',
          formattedValue: '334K',
        },
      },

      '2016': {
        primary: {
          value: 0.04960497618870645,
          unit: 'unit-interval',
          formattedValue: '5%',
        },
        secondary: {
          value: 481528.3511894156,
          unit: 'ac',
          formattedValue: '482K',
        },
      },

      '2017': {
        primary: {
          value: 0.056456367504115104,
          unit: 'unit-interval',
          formattedValue: '5.6%',
        },
        secondary: {
          value: 530313.8697087447,
          unit: 'ac',
          formattedValue: '530K',
        },
      },

      '2018': {
        primary: {
          value: 0.041802948292026125,
          unit: 'unit-interval',
          formattedValue: '4.2%',
        },
        secondary: {
          value: 393931.8375643114,
          unit: 'ac',
          formattedValue: '394K',
        },
      },

      '2019': {
        primary: {
          value: 0.052645013504833235,
          unit: 'unit-interval',
          formattedValue: '5.3%',
        },
        secondary: {
          value: 535396.9767048422,
          unit: 'ac',
          formattedValue: '535K',
        },
      },

      '2020': {
        primary: {
          value: 0.03962270119343717,
          unit: 'unit-interval',
          formattedValue: '4%',
        },
        secondary: {
          value: 399317.55341004976,
          unit: 'ac',
          formattedValue: '399K',
        },
      },

      '2021': {
        primary: {
          value: 0.07331124314889838,
          unit: 'unit-interval',
          formattedValue: '7.3%',
        },
        secondary: {
          value: 709706.0044590052,
          unit: 'ac',
          formattedValue: '710K',
        },
      },

      '2022': {
        primary: {
          value: 0.06338202506002578,
          unit: 'unit-interval',
          formattedValue: '6.3%',
        },
        secondary: {
          value: 592822.8854213345,
          unit: 'ac',
          formattedValue: '593K',
        },
      },
    },
  };

const COVERCROP_METRICUNITS_ANNUALIZED_METRIC_LOOKUP_DETAIL_TOP_LEVEL_MOCK: AnnualizedKpiMetricsLookup['topLevel'] =
  {
    '2015': {
      primary: {
        value: 0.030439777704702978,
        unit: 'unit-interval',
        formattedValue: '3%',
      },
      secondary: {
        value: 4726594.566432513,
        unit: 'ac',
        formattedValue: '4.7M',
      },
    },

    '2016': {
      primary: {
        value: 0.03320504538101856,
        unit: 'unit-interval',
        formattedValue: '3.3%',
      },
      secondary: {
        value: 5261545.560577248,
        unit: 'ac',
        formattedValue: '5.3M',
      },
    },

    '2017': {
      primary: {
        value: 0.038785447461827945,
        unit: 'unit-interval',
        formattedValue: '3.9%',
      },
      secondary: {
        value: 6276829.388271848,
        unit: 'ac',
        formattedValue: '6.3M',
      },
    },

    '2018': {
      primary: {
        value: 0.03970760233605569,
        unit: 'unit-interval',
        formattedValue: '4%',
      },
      secondary: {
        value: 6463538.026963255,
        unit: 'ac',
        formattedValue: '6.5M',
      },
    },

    '2019': {
      primary: {
        value: 0.05913809362047206,
        unit: 'unit-interval',
        formattedValue: '5.9%',
      },
      secondary: {
        value: 9120948.287873453,
        unit: 'ac',
        formattedValue: '9.1M',
      },
    },

    '2020': {
      primary: {
        value: 0.04541351549909976,
        unit: 'unit-interval',
        formattedValue: '4.5%',
      },
      secondary: {
        value: 7232744.186139035,
        unit: 'ac',
        formattedValue: '7.2M',
      },
    },

    '2021': {
      primary: {
        value: 0.04972213335074522,
        unit: 'unit-interval',
        formattedValue: '5%',
      },
      secondary: {
        value: 8078368.627187098,
        unit: 'ac',
        formattedValue: '8.1M',
      },
    },

    '2022': {
      primary: {
        value: 0.04815917557473681,
        unit: 'unit-interval',
        formattedValue: '4.8%',
      },
      secondary: {
        value: 7583663.545569885,
        unit: 'ac',
        formattedValue: '7.6M',
      },
    },
  };

export const COVERCROP_METRICUNITS_ANNUALIZED_LOOKUP_DETAIL_MOCK: AnnualizedKpiMetricsLookup = {
  topLevel: COVERCROP_METRICUNITS_ANNUALIZED_METRIC_LOOKUP_DETAIL_TOP_LEVEL_MOCK,
  boundaryLevel:
    COVERCROP_METRICUNITS_SUBREGION_ANNUALIZED_METRIC_LOOKUP_DETAIL_BOUNDARY_LEVEL_MOCK,
};

export const COVERCROP_FORMATTER_LOOKUP = {
  primary: COVERCROPMETRICS_FORMATTER_MAP['adoption'],
  secondary: COVERCROPMETRICS_FORMATTER_MAP['covercroppedArea'],
};

export const COVERCROP_METRICUNITS_UNITDETAIL_LOOKUP = {
  primary: COVERCROPMETRICS_UNIT_MAP['adoption'](MeasurementEnum.MetricUnits),
  secondary: COVERCROPMETRICS_UNIT_MAP['covercroppedArea'](MeasurementEnum.MetricUnits),
};
