/**
 * Program: 110 (Regen Collective)
 * Year: 2022
 * Subsections: All Program available subsections
 * Crops: All SI available crops
 * KPI: Cover cropping
 */

import {CO2E} from 'containers/si/constants';
import type {KPIData} from 'containers/si/programs/explore/types';
import {GHGMETRICS_FORMATTER_MAP} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.ghg_kg_per_m2.transformation';

export const GHG_METRICUNITS_KPIDATA_MOCK: KPIData = {
  boundaryLevelLookup: {
    '872': {
      choroplethValue: 1.349039001656134,
      kpiMetrics: {
        primary: {
          value: 1.349039001656134,
          unit: 'mt/ha',
          formattedValue: '1.349',
        },
      },
    },
    '873': {
      choroplethValue: 2.0449301324206273,
      kpiMetrics: {
        primary: {
          value: 2.0449301324206273,
          unit: 'mt/ha',
          formattedValue: '2.045',
        },
        uncertainty: {
          value: 0.01349039001656134,
          unit: 'mt/ha',
          formattedValue: '0.0135',
        },
      },
    },
    '874': {
      choroplethValue: 1.3199048636519881,
      kpiMetrics: {
        primary: {
          value: 1.3199048636519881,
          unit: 'mt/ha',
          formattedValue: '1.32',
        },
      },
    },
    '875': {
      choroplethValue: 0.5451405871059276,
      kpiMetrics: {
        primary: {
          value: 0.5451405871059276,
          unit: 'mt/ha',
          formattedValue: '0.545',
        },
      },
    },
    '16648': {
      choroplethValue: 1.3876605295703806,
      kpiMetrics: {
        primary: {
          value: 1.3876605295703806,
          unit: 'mt/ha',
          formattedValue: '1.388',
        },
      },
    },
  },
  topLevelLookup: {
    primary: {
      value: 1.1875595671573755,
      unit: 'mt/ha',
      formattedValue: '1.188',
    },
  },
  annualizedLookup: null,
  unitLookup: {
    primary: {
      unit: 'mt/ha',
      unitName: {
        singular: `metric tonne ${CO2E} / hectare`,
        plural: `metric tonnes ${CO2E} / hectare`,
        abbr: `mt ${CO2E} / ha`,
      },
    },
  },
  formatterLookup: {
    primary: GHGMETRICS_FORMATTER_MAP.ghgMassPerArea,
  },
} as const;
