import React from 'react';

import {type Lab} from '@regrow-internal/design-system';

import {getTypedEntries} from '_common/utils/object';
import {isDefined, isNil, isNonEmptyArray} from '_common/utils/typeGuards';

import {useProgramYears} from 'containers/si/hooks/useProgramYears';
import {KpiMetricsTableCell} from 'containers/si/programs/explore/components/Table/KpiMetricsTableCell';
import {RegionTableCell} from 'containers/si/programs/explore/components/Table/RegionTableCell';
import {getNumberOrNullSortComparator} from 'containers/si/programs/explore/helpers/datagrid.helpers';
import {makeTooltipContent} from 'containers/si/programs/explore/helpers/metric.helpers';
import {type BoundaryData} from 'containers/si/programs/explore/hooks/useBoundaryData';
import type {
  ChoroplethLookup,
  FiltersState,
  KpiContent,
  KPIData,
  KpiUnitLookup,
  RowModel,
  TableContent,
  TopLevelTimeTrendChartContent,
} from 'containers/si/programs/explore/types';

type UseTableDataArgs = {
  annualizedKpiLookup: KPIData['annualizedLookup'];
  boundaryLookup: BoundaryData['data']['boundaryLevelLookup'];
  timeTrendLineChartColorLookup: TopLevelTimeTrendChartContent['lineChartColorLookup'] | null;
  choroplethLookup: ChoroplethLookup | null;
  content: KpiContent;
  filtersState: FiltersState;
  unitLookup: KPIData['unitLookup'];
};

export const useTableData = ({
  annualizedKpiLookup,
  boundaryLookup,
  choroplethLookup,
  content,
  filtersState: {year: selectedYear},
  unitLookup,
}: UseTableDataArgs): TableContent | null => {
  const {programYears} = useProgramYears();

  const rows: Array<RowModel> | null = React.useMemo(() => {
    if (isNil(boundaryLookup)) return null;

    return getTypedEntries(boundaryLookup).map(([boundaryId, boundaryData]) => ({
      id: boundaryId,
      boundaryName: boundaryData['name'],
      choroplethColor: choroplethLookup?.choroplethLookup[boundaryId] ?? null,
      kpiMetrics: annualizedKpiLookup?.boundaryLevel[boundaryId] ?? null,
    }));
  }, [annualizedKpiLookup?.boundaryLevel, boundaryLookup, choroplethLookup?.choroplethLookup]);

  const columns = React.useMemo(() => {
    const yearColumns = programYears.map(year =>
      makeKpiColumn({
        headerName: `${year}`,
        field: 'kpiMetrics',
        sortDirection: content?.sortDirection,
        unitLookup,
        shouldBold: year === selectedYear,
      })
    );

    return [
      {
        headerName: 'Region name',

        field: 'boundaryName',
        flex: 1,
        minWidth: 200,
        renderCell: ({row: {boundaryName}}) => <RegionTableCell boundaryName={boundaryName} />,
      },
      ...yearColumns,
    ];
  }, [content?.sortDirection, programYears, selectedYear, unitLookup]);

  const sorting: Lab.GridSortingInitialState = {sortModel: [{field: 'boundaryName', sort: 'asc'}]};

  if (isNil(rows)) return null;

  return {
    rows,
    columns,
    sorting,
    choroplethGroups: choroplethLookup?.choroplethGroups ?? null,
  };
};

const makeKpiColumn = ({
  headerName,
  field,
  sortDirection,
  unitLookup,
  shouldBold = false,
}: {
  headerName: NonNullable<Lab.GridColDef['headerName']>;
  field: 'kpiMetrics';
  sortDirection: Lab.GridSortDirection;
  unitLookup: KpiUnitLookup | null;
  shouldBold?: boolean;
}): Lab.GridColDef<RowModel, number | null> => ({
  headerName,
  field: headerName,
  flex: 1,
  minWidth: 100,
  type: 'number',
  valueGetter: ({row}) => row[field]?.[headerName]?.primary.value ?? null,
  renderCell: ({row}) => {
    const boundaryName = row['boundaryName'];
    const primaryMetric = row[field]?.[headerName]?.primary;
    const uncertaintyMetric = row[field]?.[headerName]?.uncertainty;
    const {
      primaryMetricWithUncertaintyText,
      primaryAltMetricWithUncertaintyText,
      secondaryText,
      unknownText,
      notApplicableText,
      tierText,
    } =
      makeTooltipContent({
        kpiMetrics: row[field]?.[headerName],
        unitLookup,
      }) ?? {};

    const tooltipSubtext = [
      primaryAltMetricWithUncertaintyText,
      secondaryText,
      unknownText,
      notApplicableText,
      tierText,
    ].filter(isDefined);

    const tooltip =
      isDefined(primaryMetricWithUncertaintyText) && isNonEmptyArray(tooltipSubtext)
        ? {
            title: `${headerName} - ${boundaryName}`,
            primaryText: primaryMetricWithUncertaintyText,
            subText: tooltipSubtext,
          }
        : undefined;

    return (
      <KpiMetricsTableCell
        metric={primaryMetric}
        uncertainty={uncertaintyMetric}
        tooltip={tooltip}
        isActiveCell={shouldBold}
      />
    );
  },
  // sorts null to bottom on default sort.
  // TODO: When DS is upgraded to MUI 7, use getSortComparator which exposes sortOrder so null can always be sorted to the bottom
  sortComparator: getNumberOrNullSortComparator(sortDirection),
});
