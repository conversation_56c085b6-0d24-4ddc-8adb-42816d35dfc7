import type {ChartDataset} from 'chart.js';

import type {Lab, Theme} from '@regrow-internal/design-system';

import type {MeasurementEnum} from '_common/utils/measurement-unit-options';
import type {RequiredKeys} from '_common/utils/type-utils';

import type {BoundaryType, KPI} from 'containers/si/api/apiTypes';
import type {MAP_LAYER_OPTIONS} from 'containers/si/programs/explore/constants';
import type {CoverCropMetrics} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.cover_crop.transformation';
import type {C2gLulcMetrics} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.cropland_to_grassland_lulc.transformation';
import type {DroughtMetrics} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.drought_index.transformation';
import type {GhgEFMetrics} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.ghg_emissions_factor.transformation';
import type {GhgMetrics} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.ghg_kg_per_m2.transformation';
import type {G2cLulcMetrics} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.grassland_to_cropland_lulc.transformation';
import type {GreennessLevelMetrics} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.greenness_level_area_m2.transformation';
import type {HeatStressMetrics} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.heat_stress.transformation';
import type {NetGhgEFMetrics} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.net_ghg_emissions_factor.transformation';
import type {SocEFMetrics} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.soc_emissions_factor.transformation';
import type {SocMetrics} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.soc_kg_per_m2.transformation';
import type {TemperatureMetrics} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.temperature.transformation';
import type {TillageMetrics} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.tillage.transformation';
import type {VolumeWeightedGhgMetrics} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.volume_weighted_ghg_kg.transformation';
import type {VolumeWeightedNetMetrics} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.volume_weighted_net_kg.transformation';
import type {VolumeWeightedSocMetrics} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.volume_weighted_soc_kg.transformation';
import type {YieldPerAreaMetrics} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.yield_per_area.transformation';
import type {BoundaryLookupValue} from 'containers/si/programs/helpers/types';
import type {KPIDataFiltersState} from 'containers/si/types';
import type {Metric, UnitDetail, UnitType} from 'containers/si/utils/value.types';

export type AVAILABLE_KPIS =
  | Extract<
      KPI,
      | 'cover_crop'
      | 'tillage'
      | 'ghg_kg_per_m2'
      | 'soc_kg_per_m2'
      | 'ghg_emissions_factor'
      | 'soc_emissions_factor'
      | 'net_ghg_emissions_factor'
      | 'volume_weighted_ghg_kg'
      | 'volume_weighted_soc_kg'
      | 'volume_weighted_net_kg'
      | 'yield_per_area'
      | 'grassland_to_cropland_lulc'
      | 'cropland_to_grassland_lulc'
      | 'drought_index'
    >
  | 'greenness_level_area_m2_high'
  | 'greenness_level_area_m2_moderate'
  | 'greenness_level_area_m2_low'
  | 'greenness_level_area_m2_bare'
  | 'avg_temperature'
  | 'avg_max_temperature'
  | 'yoy_temperature_delta'
  | 'days_of_heat_stress'
  | 'heat_stress_occurrences';
// ^^ This must be done for green cover, heat stress & temperature as we're treating
// KPI subtypes as independent kpis to render them as separate items in our kpi dropdown

export type KPIMetrics = Record<string, Metric | null>;

export type MetricLookupMap<T> = {
  primaryMetricKey: keyof T;
  uncertaintyMetricKey?: keyof T;
  primaryAltMetricKey?: keyof T;
  uncertaintyAltMetricKey?: keyof T;
  secondaryMetricKey?: keyof T;
  secondaryUncertaintyMetricKey?: keyof T;
  trackedMetricKey?: keyof T;
  unknownMetricKey?: keyof T;
  notApplicableMetricKey?: keyof T;
  tierMetricKey?: keyof T;
};

export type FormatterLookupMap<T> = Record<keyof T, (value: number) => string>;

export type UnitLookupMap<T> = Record<
  keyof T,
  (unitsSystem: MeasurementEnum) => UnitDetail<UnitType>
>;

export type BiasedChoroplethOptions = {
  minValue: number;
  maxValue: number;
  bias?: number;
  zMinMultiplier?: number;
  zMaxMultiplier: number;
};

export type Content<T> = {
  topLevelTitle: string;
  topLevelDescription: string;
  primaryMetricKey: RequiredKeys<T>;
  uncertaintyMetricKey?: keyof T;
  primaryAltMetricKey?: keyof T;
  uncertaintyAltMetricKey?: keyof T;
  secondaryMetricKey?: keyof T;
  secondaryUncertaintyMetricKey?: keyof T;
  trackedMetricKey?: keyof T;
  unknownMetricKey?: keyof T;
  notApplicableMetricKey?: keyof T;
  tierMetricKey?: keyof T;
  label: string;
  categoryColorKey: keyof Theme['palette']['heatMapCategoryPalette'];
  sortDirection: NonNullable<Lab.GridSortDirection>;
  /** default === 'quantile' */
  choroplethGroupMode?: ChoroplethGroupMode;
  /** only used with ChoroplethGroupMode `fixedValue` */
  fixedChoroplethOptions?: {
    fixedChoroplethGroupValues?: Array<number>;
    isClassification?: boolean;
  };
  /** only used with ChoroplethGroupMode `biasedDistribution` */
  biasedChoroplethOptions?: BiasedChoroplethOptions;
};

export type KpiContent =
  | Content<CoverCropMetrics>
  | Content<GreennessLevelMetrics>
  | Content<TillageMetrics>
  | Content<C2gLulcMetrics>
  | Content<G2cLulcMetrics>
  | Content<GhgMetrics>
  | Content<SocMetrics>
  | Content<GhgEFMetrics>
  | Content<SocEFMetrics>
  | Content<NetGhgEFMetrics>
  | Content<VolumeWeightedGhgMetrics>
  | Content<VolumeWeightedSocMetrics>
  | Content<VolumeWeightedNetMetrics>
  | Content<YieldPerAreaMetrics>
  | Content<DroughtMetrics>
  | Content<TemperatureMetrics>
  | Content<HeatStressMetrics>
  | Content<{empty: null}>
  | null;

export type ExtendedFiltersState = {
  kpi?: AVAILABLE_KPIS;
  /** @deprecated TODO: remove in SI-3290 */
  isTimeTrendEnabled: boolean;
  /** @deprecated TODO: remove in SI-3290 */
  startYear?: number;
  boundaryType?: BoundaryType;
};

export type FiltersState = KPIDataFiltersState & ExtendedFiltersState;

export type DirtyFiltersState = KPIDataFiltersState & {
  boundaryType?: BoundaryType;
};

export type KpiUnitLookup = {
  primary: UnitDetail<UnitType>;
  secondary?: UnitDetail<UnitType>;
};

export type KpiFormatterLookup = {
  primary: (value: number) => string;
  secondary?: (value: number) => string;
};

export type MappedKpiMetrics = {
  primary: Metric;
  uncertainty?: Metric | null;
  primaryAlt?: Metric | null;
  uncertaintyAlt?: Metric | null;
  secondary?: Metric | null;
  secondaryUncertainty?: Metric | null;
  tracked?: Metric | null;
  unknown?: Metric | null;
  notApplicable?: Metric | null;
  tier?: Metric | null;
};

export const KPI_PRIMARY_METRIC_KEYS = ['primary', 'uncertainty'] as const;

export const KPI_PRIMARY_ALT_METRIC_KEYS = ['primaryAlt', 'uncertaintyAlt'] as const;

export const KPI_SECONDARYTYPE_METRIC_KEYS = [
  'secondary',
  'secondaryUncertainty',
  'tracked',
  'unknown',
  'notApplicable',
] as const;

export const KPI_TIERTYPE_METRIC_KEYS = ['tier'] as const;

export type KpiPrimaryMetricKeys = Extract<
  keyof MappedKpiMetrics,
  (typeof KPI_PRIMARY_METRIC_KEYS)[number]
>;

export type KpiPrimaryAltMetricKeys = Extract<
  keyof MappedKpiMetrics,
  (typeof KPI_PRIMARY_ALT_METRIC_KEYS)[number]
>;

export type KpiSecondaryTypeKeys = Extract<
  keyof MappedKpiMetrics,
  (typeof KPI_SECONDARYTYPE_METRIC_KEYS)[number]
>;

export type KpiTierTypeKeys = Extract<
  keyof MappedKpiMetrics,
  (typeof KPI_TIERTYPE_METRIC_KEYS)[number]
>;

export type AnnualizedKpiMetricsLookup = {
  topLevel: Record<string, MappedKpiMetrics | null>;
  boundaryLevel: Record<string, Record<string, MappedKpiMetrics | null> | null>;
};

export type MetricTooltipContent = {
  primaryMetricWithUncertaintyText: string;
  primaryAltMetricWithUncertaintyText?: string;
  secondaryText?: string;
  secondaryWithoutTrackedText?: string;
  unknownText?: string;
  notApplicableText?: string;
  tierText?: string;
};

export type TopLevelNumberContent = {
  metric: Metric | null;
  uncertainty?: Metric | null;
  unitDetail: UnitDetail<UnitType>;
  subText?: string;
  tooltip?: {
    title?: string;
    primaryText: string;
    subText?: Array<string>;
  };
};

export type TopLevelTimeTrendChartContent = {
  labels: Array<string>;
  datasets: Array<ChartDataset<'line', Array<number | null>>>;
  tooltips: Record<TopLevelTimeTrendChartContent['labels'][number], Array<string>>;
  unitDetail: UnitDetail<UnitType>;
  formatter: (value: number) => string;
  range?: {min: number; max: number; stepSize?: number};
  lineChartColorLookup: Map<string, string>;
};

export type TopLevelPanelContent = {
  title: string;
  description?: string;
  notifications?: Array<string>;
  topLevelNumberContent: TopLevelNumberContent | null;
  topLevelTimeTrendChartContent: TopLevelTimeTrendChartContent | null;
};

/** @deprecated TODO remove in SI-3290 */
export type TablePanelContent = {
  rows: Array<RowModelDeprecated>;
  columns: Array<Lab.GridColDef<RowModelDeprecated>>;
  sorting: Lab.GridInitialState['sorting'];
  choroplethGroups: ChoroplethGroups | null;
};

export type TableContent = {
  rows: Array<RowModel>;
  columns: Array<Lab.GridColDef<RowModel>>;
  sorting: Lab.GridInitialState['sorting'];
  choroplethGroups: ChoroplethGroups | null;
};

export type MapLegendContent = {
  choroplethGroups: ChoroplethGroups;
  title: string;
  labelMin?: string;
  labelMax?: string;
};

export type MapTooltipContent = {
  boundaryName: string;
  boundaryTimeTrendColor: string | null;
  choropleth: ChoroplethColor | null;
  cropAreaText?: string;
  farmCountText?: string;
  kpiTooltip?: {
    primaryText: string;
    subText?: Array<string>;
  };
};

export type MapTooltipContentLookup = Record<string, MapTooltipContent>;

export type MapLayersContent = {
  choroplethLookup: ChoroplethLookup['choroplethLookup'] | null;
  choroplethGroups: ChoroplethGroups | null;
  heatMapCategoryColorKey: keyof Theme['palette']['heatMapCategoryPalette'] | null;
};

export type ActiveMapLayers = Array<keyof typeof MAP_LAYER_OPTIONS>;

export type MapContent = {
  filtersState: FiltersState;
  mapLayersContent: MapLayersContent;
  mapLegendContent: MapLegendContent | null;
  mapTooltipContentLookup: MapTooltipContentLookup | null;
};

/** @deprecated TODO remove in SI-3290 */
export type RowModelDeprecated = {
  id: BoundaryLookupValue['id'];
  boundaryName: BoundaryLookupValue['name'];
  boundaryTimeTrendColor: string | null;
  choroplethColor: ChoroplethColor | null;
  kpiMetrics: MappedKpiMetrics | null;
  startYearKpiMetrics?: MappedKpiMetrics | null;
  endYearKpiMetrics?: MappedKpiMetrics | null;
};

export type RowModel = {
  id: BoundaryLookupValue['id'];
  boundaryName: BoundaryLookupValue['name'];
  choroplethColor: ChoroplethColor | null;
  kpiMetrics: Record<string, MappedKpiMetrics | null> | null;
};

type KpiByBoundaryLookup = {
  choroplethValue: number;
  kpiMetrics: MappedKpiMetrics;
};

export type KPIData = {
  annualizedLookup: AnnualizedKpiMetricsLookup | null;
  topLevelLookup: MappedKpiMetrics | null;
  boundaryLevelLookup: Record<string, KpiByBoundaryLookup | null> | null;
  unitLookup: KpiUnitLookup | null;
  formatterLookup: KpiFormatterLookup | null;
};

export type KPIReturnType = {
  isLoading: boolean;
  data: KPIData;
  contentOverrides?: {
    topLevelTimeTrendChartContent?: (
      content: TopLevelTimeTrendChartContent
    ) => TopLevelTimeTrendChartContent;
    mapLayersContent?: (content: MapContent['mapLayersContent']) => MapContent['mapLayersContent'];
  };
};

export type ChoroplethColor = {
  color: string;
  cssSelector: string;
};

export type DivisionGroup = {exclusiveLowerBound: number; inclusiveUpperBound: number};
export type ChoroplethGroup = DivisionGroup & {
  color: string;
  label: string;
  cssSelector: string;
  cssWidth: string;
};

export type ChoroplethGroups = Array<ChoroplethGroup>;

export type ChoroplethGroupMode = 'quantile' | 'evenDistribution' | 'fixedValue' | 'biased';

export type ChoroplethLookup = {
  choroplethLookup: Record<string, ChoroplethColor>;
  choroplethGroups: ChoroplethGroups;
};
