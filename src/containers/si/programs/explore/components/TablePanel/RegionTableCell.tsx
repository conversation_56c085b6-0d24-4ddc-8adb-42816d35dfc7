import React from 'react';

import {Stack, TypographyOverflow} from '@regrow-internal/design-system';

import {DSColorIndicator} from 'containers/si/programs/explore/components/DSColorIndicator';

/** @deprecated remove in SI-3290 */
export const RegionTableCell = ({
  boundaryName,
  color,
}: {
  boundaryName: string;
  color: string | null;
}) => {
  return (
    <Stack width={1}>
      <Stack direction="row" alignItems="center" gap={1}>
        <TypographyOverflow variant="h5">{boundaryName}</TypographyOverflow>
        <DSColorIndicator color={color} />
      </Stack>
    </Stack>
  );
};
