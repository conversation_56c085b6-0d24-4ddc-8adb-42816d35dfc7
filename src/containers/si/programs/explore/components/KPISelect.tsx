import {useFeatureFlagEnabled} from 'posthog-js/react';
import React, {useMemo} from 'react';

import {SelectField} from '@regrow-internal/design-system';

import {getTypedKeys} from '_common/utils/object';
import {capitalizeFirstLetter} from '_common/utils/string';
import {isDefined, isNonEmptyArray} from '_common/utils/typeGuards';

import {UnavailableOptionLabel} from 'containers/si/components/filters/UnavailableOptionLabel';
import {useKPIAccess} from 'containers/si/hooks/useKPIAccess';
import {useTabAccess} from 'containers/si/hooks/useTabAccess';
import {
  AVAILABLE_KPI_TO_ACCESS_KEY,
  GREEN_COVER_KPIS,
  HEAT_STRESS_KPIS,
  VOLUMES_KPIS,
} from 'containers/si/programs/explore/constants';
import {
  AVAILABLE_KPIS_TO_LABELS,
  KPI_SELECT_DISABLED_RULES,
} from 'containers/si/programs/explore/content';
import type {AVAILABLE_KPIS, FiltersState} from 'containers/si/programs/explore/types';

interface KPISelectProps {
  filtersState: FiltersState;
  onKPIChange: (kpi: FiltersState['kpi']) => void;
}

export const KPISelect: React.FC<KPISelectProps> = ({filtersState, onKPIChange}) => {
  const {kpi: selectedKpi} = filtersState;
  const {isKPIAvailable} = useKPIAccess();
  const {getHasTabAccess} = useTabAccess();
  // TODO: SI-3110 - remove when green cover is rolled out to all geos
  const isGreennessLevelKpiEnabled = useFeatureFlagEnabled('si-kpi-greennness-levels');
  const isHeatStressEnabled = useFeatureFlagEnabled('si-heat-risk-kpi');
  const hasVolumesAccess = getHasTabAccess('commodity_volumes');

  const availableOptions = useMemo(() => {
    const predicates: Array<(kpi: AVAILABLE_KPIS) => boolean> = [
      kpi => AVAILABLE_KPI_TO_ACCESS_KEY[kpi].every(isKPIAvailable),
      // TODO: SI-3110 - remove when green cover is rolled out to all geos
      kpi => (GREEN_COVER_KPIS.includes(kpi) ? isGreennessLevelKpiEnabled ?? false : true),
      // TODO: SI-3475 - remove when heat is released
      kpi => (HEAT_STRESS_KPIS.includes(kpi) ? isHeatStressEnabled ?? false : true),
      kpi => (VOLUMES_KPIS.includes(kpi) ? hasVolumesAccess : true),
    ];

    const availableKpis = getTypedKeys(AVAILABLE_KPI_TO_ACCESS_KEY).filter(kpi =>
      predicates.every(predicate => predicate(kpi))
    );

    return makeSelectOptions(filtersState, availableKpis);
  }, [
    filtersState,
    isKPIAvailable,
    isGreennessLevelKpiEnabled,
    hasVolumesAccess,
    isHeatStressEnabled,
  ]);

  return (
    <SelectField<KPISelectProps['filtersState']['kpi']>
      aria-label="Select KPI"
      onChange={e => onKPIChange(e.target.value)}
      value={selectedKpi}
      options={availableOptions}
    />
  );
};

const makeSelectOptions = (filtersState: FiltersState, availableKpis: Array<AVAILABLE_KPIS>) =>
  availableKpis.map(kpi => {
    const disabledMessages = KPI_SELECT_DISABLED_RULES.map(({rule, message}) =>
      rule(filtersState, kpi) ? message(filtersState, kpi) : null
    ).filter(isDefined);

    return isNonEmptyArray(disabledMessages)
      ? {
          label: (
            <UnavailableOptionLabel
              label={`${capitalizeFirstLetter(AVAILABLE_KPIS_TO_LABELS[kpi])}`}
              message={`${capitalizeFirstLetter(
                AVAILABLE_KPIS_TO_LABELS[kpi]
              )} reporting is unavailable with ${disabledMessages.join(', ')}`}
            />
          ),
          value: kpi,
          disabled: true,
        }
      : {
          label: capitalizeFirstLetter(AVAILABLE_KPIS_TO_LABELS[kpi]),
          value: kpi,
        };
  });
