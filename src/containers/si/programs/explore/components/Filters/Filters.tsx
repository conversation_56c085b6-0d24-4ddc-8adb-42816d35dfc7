import React from 'react';

import {Box, Button, SvgIcon} from '@regrow-internal/design-system';

import {RegionsSelect} from 'containers/si/components/filters/RegionsSelect';
import {YearsSelect} from 'containers/si/components/filters/YearsSelect';
import {useTabAccess} from 'containers/si/hooks/useTabAccess';
import {AggregationLevelSelect} from 'containers/si/programs/explore/components/AggregationLevelSelect';
import {ExploreCropsSelect} from 'containers/si/programs/explore/components/Filters/ExploreCropsSelect';
import {ExploreDataScenarioSelect} from 'containers/si/programs/explore/components/Filters/ExploreDataScenarioSelect';
import type {DirtyFiltersState, FiltersState} from 'containers/si/programs/explore/types';
import {isValidCommonKpiFiltersState} from 'containers/si/typeGuards';

export const Filters = ({
  isDirty,
  dirtyFiltersState,
  filtersState,
  onApplyFiltersClick,
  onCancelClick,
  onFilterChange,
}: {
  isDirty: boolean;
  dirtyFiltersState: DirtyFiltersState;
  filtersState: FiltersState;
  onApplyFiltersClick: () => void;
  onCancelClick: () => void;
  onFilterChange: (newDirtyFilterState: Partial<DirtyFiltersState>) => void;
}) => {
  const {getHasTabAccess} = useTabAccess();

  const isSubmitDisabled = !isDirty || !isValidCommonKpiFiltersState(dirtyFiltersState);

  return (
    <Box
      bgcolor="semanticPalette.surface.secondary"
      p={theme => theme.spacing(2, 5)}
      borderBottom={theme => `1px solid ${theme.palette.semanticPalette.stroke.main}`}
    >
      <Box
        justifyContent="space-between"
        bgcolor="semanticPalette.surface.secondary"
        display="flex"
        gap={3}
        flexWrap={'wrap'}
      >
        <Box alignItems="center" display="flex" flexGrow={1} gap={3} flexWrap={'wrap'}>
          <SvgIcon color="inherit" fontSize="medium" type="filter" />

          {getHasTabAccess('data_scenarios') ? (
            <ExploreDataScenarioSelect
              dataScenarioId={dirtyFiltersState.dataScenarioId}
              onDataScenarioChange={dataScenarioId => onFilterChange({dataScenarioId})}
              kpi={filtersState.kpi} // the kpi filter is not managed within the dirty filters
              boundaryType={dirtyFiltersState.boundaryType}
            />
          ) : null}
          <Box width={theme => theme.fixedWidths.xs}>
            <RegionsSelect
              regionIds={dirtyFiltersState.subsectionIds}
              dataScenarioId={dirtyFiltersState.dataScenarioId}
              onRegionsChange={subsectionIds => onFilterChange({subsectionIds})}
            />
          </Box>
          <AggregationLevelSelect
            boundaryType={dirtyFiltersState.boundaryType}
            dataScenarioId={dirtyFiltersState.dataScenarioId}
            kpi={filtersState.kpi} // the kpi filter is not managed within the dirty filters
            onAggregationLevelChange={boundaryType => onFilterChange({boundaryType})}
          />
          <ExploreCropsSelect
            cropIds={dirtyFiltersState.cropIds}
            regionIds={dirtyFiltersState.subsectionIds}
            dataScenarioId={dirtyFiltersState.dataScenarioId}
            onCropsChange={cropIds => onFilterChange({cropIds})}
            kpi={filtersState.kpi} // the kpi filter is not managed within the dirty filters
          />
          <YearsSelect
            year={dirtyFiltersState.year}
            onYearChange={year => onFilterChange({year})}
          />
        </Box>
        <Box alignItems="flex-end" display="flex" gap={3}>
          {isDirty && (
            <Button color="secondary" variant="outlined" onClick={onCancelClick}>
              Cancel
            </Button>
          )}
          <Button disabled={isSubmitDisabled} onClick={onApplyFiltersClick}>
            Apply Filters
          </Button>
        </Box>
      </Box>
    </Box>
  );
};
