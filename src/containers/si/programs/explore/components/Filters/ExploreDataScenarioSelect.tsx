import {isNonEmptyArray} from '@apollo/client/utilities';
import React, {useMemo} from 'react';

import {isDefined} from '_common/utils/typeGuards';

import {
  DataScenarioSelect,
  type DataScenarioSelectProps,
} from 'containers/si/components/filters/DataScenarioSelect';
import {DATA_SCENARIO_SELECT_DISABLED_RULES} from 'containers/si/programs/explore/content';
import type {FiltersState} from 'containers/si/programs/explore/types';

type ExploreDataScenarioSelectProps = Omit<
  DataScenarioSelectProps,
  'disabled' | 'disabledMessage'
> & {
  kpi: FiltersState['kpi'];
  boundaryType: FiltersState['boundaryType'];
};

export const ExploreDataScenarioSelect: React.FC<ExploreDataScenarioSelectProps> = ({
  dataScenarioId,
  onDataScenarioChange,
  kpi,
  boundaryType,
}) => {
  const disabled = useMemo(() => {
    const disabledMessages = DATA_SCENARIO_SELECT_DISABLED_RULES.map(({rule, message}) =>
      rule({kpi, boundaryType}) ? message({kpi, boundaryType}) : null
    ).filter(isDefined);

    return {
      status: isNonEmptyArray(disabledMessages),
      message: isNonEmptyArray(disabledMessages)
        ? `Data scenario filtering is unavailable with ${disabledMessages.join(', ')}`
        : undefined,
    };
  }, [boundaryType, kpi]);

  return (
    <DataScenarioSelect
      dataScenarioId={dataScenarioId}
      onDataScenarioChange={onDataScenarioChange}
      disabled={disabled.status}
      disabledMessage={disabled.message}
    />
  );
};
