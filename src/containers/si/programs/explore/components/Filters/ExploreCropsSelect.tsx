import React, {useMemo} from 'react';

import {isDefined, isNonEmptyArray} from '_common/utils/typeGuards';

import {
  ProgramCropsSelect,
  type ProgramCropsSelectProps,
} from 'containers/si/components/filters/CropsSelect/ProgramCropsSelect';
import {MULTICROP_SELECT_DISABLED_RULES} from 'containers/si/programs/explore/content';
import type {FiltersState} from 'containers/si/programs/explore/types';

type ExploreCropsSelectProps = ProgramCropsSelectProps & {
  kpi: FiltersState['kpi'];
};

export const ExploreCropsSelect: React.FC<ExploreCropsSelectProps> = ({
  kpi,
  ...cropSelectProps
}) => {
  const disableMultiple = useMemo(() => {
    const disabledMessages = MULTICROP_SELECT_DISABLED_RULES.map(({rule, message}) =>
      rule({kpi}) ? message({kpi}) : null
    ).filter(isDefined);

    return {
      status: isNonEmptyArray(disabledMessages),
      message: isNonEmptyArray(disabledMessages)
        ? `Multi-crop aggregation is unavailable with ${disabledMessages.join(', ')}`
        : undefined,
    };
  }, [kpi]);

  return (
    <ProgramCropsSelect
      {...cropSelectProps}
      disableMultiple={disableMultiple.status}
      disableMultipleMessage={disableMultiple.message}
    />
  );
};
