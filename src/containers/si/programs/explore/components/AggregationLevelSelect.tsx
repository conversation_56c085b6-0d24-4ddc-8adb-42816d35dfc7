import React, {useCallback} from 'react';

import type {SelectChangeEvent} from '@regrow-internal/design-system';
import {MenuItem, Select} from '@regrow-internal/design-system';

import {getTypedKeys} from '_common/utils/object';
import {capitalizeFirstLetter} from '_common/utils/string';
import {isDefined, isNonEmptyArray} from '_common/utils/typeGuards';

import type {BoundaryType} from 'containers/si/api/apiTypes';
import {UnavailableOptionLabel} from 'containers/si/components/filters/UnavailableOptionLabel';
import {BOUNDARY_TYPE_TO_TEXT_LABEL} from 'containers/si/programs/explore/constants';
import {AGGREGATION_LEVEL_SELECT_DISABLED_RULES} from 'containers/si/programs/explore/content';
import type {FiltersState} from 'containers/si/programs/explore/types';

type AggregationLevelSelectProps = {
  boundaryType: FiltersState['boundaryType'];
  dataScenarioId: FiltersState['dataScenarioId'];
  kpi: FiltersState['kpi'];
  onAggregationLevelChange: (boundaryType: FiltersState['boundaryType']) => void;
};

export const AggregationLevelSelect = ({
  boundaryType,
  dataScenarioId,
  kpi,
  onAggregationLevelChange,
}: AggregationLevelSelectProps) => {
  const onChange = useCallback(
    (e: SelectChangeEvent<FiltersState['boundaryType']>) =>
      onAggregationLevelChange(e.target.value),
    [onAggregationLevelChange]
  );
  const ariaLabel = `Select aggregation level`;
  const selectedText = !isDefined(boundaryType)
    ? ariaLabel
    : `Aggregated by ${BOUNDARY_TYPE_TO_TEXT_LABEL[boundaryType]}`;

  return (
    <Select<FiltersState['boundaryType']>
      aria-label={ariaLabel}
      required
      onChange={onChange}
      value={boundaryType}
      renderValue={() => `${selectedText}`}
    >
      {makeSelectMenuItems({dataScenarioId, kpi}, getTypedKeys(BOUNDARY_TYPE_TO_TEXT_LABEL))}
    </Select>
  );
};

const makeSelectMenuItems = (
  filtersState: Pick<FiltersState, 'dataScenarioId' | 'kpi'>,
  availableBoundaryTypes: Array<BoundaryType>
) =>
  availableBoundaryTypes.map(boundaryType => {
    const disabledMessages = AGGREGATION_LEVEL_SELECT_DISABLED_RULES.map(({rule, message}) =>
      rule(filtersState, boundaryType) ? message(filtersState, boundaryType) : null
    ).filter(isDefined);

    return isNonEmptyArray(disabledMessages) ? (
      <MenuItem
        key={boundaryType}
        disabled
        aria-label={BOUNDARY_TYPE_TO_TEXT_LABEL[boundaryType]}
        value={boundaryType}
      >
        <UnavailableOptionLabel
          label={`${BOUNDARY_TYPE_TO_TEXT_LABEL[boundaryType]}`}
          message={`${capitalizeFirstLetter(
            BOUNDARY_TYPE_TO_TEXT_LABEL[boundaryType]
          )} reporting is unavailable with ${disabledMessages.join(', ')}`}
        />
      </MenuItem>
    ) : (
      <MenuItem key={boundaryType} value={boundaryType}>
        {BOUNDARY_TYPE_TO_TEXT_LABEL[boundaryType]}
      </MenuItem>
    );
  });
