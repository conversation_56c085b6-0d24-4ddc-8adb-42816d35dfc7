import {isDefined} from '_common/utils/typeGuards';

import type {BoundaryType} from 'containers/si/api/apiTypes';
import {
  CH4,
  CO2,
  CROPLAND_SATELLITE_VERIFIED_TEXT,
  GLOBAL_TEMPERATURE_CHOROPLETH_DEFAULTS,
  N2O,
} from 'containers/si/constants';
import {
  BOUNDARY_TYPE_TO_TEXT_LABEL,
  KPIS_INCOMPATIBLE_WITH_ADMIN2,
  KPIS_INCOMPATIBLE_WITH_DATA_SCENARIO,
  KPIS_INCOMPATIBLE_WITH_MULTIPLE_CROPS,
  TIMETREND_MULTILINE_MAX_BOUNDARY_COUNT,
} from 'containers/si/programs/explore/constants';
import type {AVAILABLE_KPIS, Content, FiltersState} from 'containers/si/programs/explore/types';
import {
  COVER_CROP_CONTENT,
  type CoverCropMetrics,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.cover_crop.transformation';
import {type C2gLulcMetrics} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.cropland_to_grassland_lulc.transformation';
import {
  BE_TO_FE_DROUGHT_INDEX_MAP,
  DROUGHT_CONTENT,
  type DroughtMetrics,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.drought_index.transformation';
import {type GhgEFMetrics} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.ghg_emissions_factor.transformation';
import {type GhgMetrics} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.ghg_kg_per_m2.transformation';
import {type G2cLulcMetrics} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.grassland_to_cropland_lulc.transformation';
import {
  GREENNESSLEVEL_LABEL_MAP,
  type GreennessLevelMetrics,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.greenness_level_area_m2.transformation';
import {
  HEAT_STRESS_CONTENT,
  HEATWAVE_OCCURRENCES_CONTENT,
  type HeatStressMetrics,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.heat_stress.transformation';
import {type NetGhgEFMetrics} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.net_ghg_emissions_factor.transformation';
import {type SocEFMetrics} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.soc_emissions_factor.transformation';
import {type SocMetrics} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.soc_kg_per_m2.transformation';
import {
  AVG_MAX_TEMP_CONTENT,
  AVG_TEMP_CONTENT,
  YOY_TEMP_CHANGE_CONTENT,
  type TemperatureMetrics,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.temperature.transformation';
import {type TillageMetrics} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.tillage.transformation';
import {type VolumeWeightedGhgMetrics} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.volume_weighted_ghg_kg.transformation';
import {type VolumeWeightedNetMetrics} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.volume_weighted_net_kg.transformation';
import {type VolumeWeightedSocMetrics} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.volume_weighted_soc_kg.transformation';
import {type YieldPerAreaMetrics} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.yield_per_area.transformation';

const GHG_KG_PER_M2: Content<GhgMetrics> = {
  topLevelTitle: 'Average GHG emissions',
  topLevelDescription: `Average field emissions (${N2O} and ${CH4}) in ${CO2} equivalents per unit of cropland`,
  primaryMetricKey: 'ghgMassPerArea',
  uncertaintyMetricKey: 'ghgStdErr',
  label: 'GHG emissions',
  categoryColorKey: '2',
  sortDirection: 'asc',
} as const;

const SOC_KG_PER_M2: Content<SocMetrics> = {
  topLevelTitle: 'Average sequestration (dSOC)',
  topLevelDescription: `Average change in soil organic carbon in ${CO2} equivalents per unit of cropland`,
  primaryMetricKey: 'dSocMassPerArea',
  uncertaintyMetricKey: 'dSocStdErr',
  label: `sequestration (dSOC)`,
  categoryColorKey: '2',
  sortDirection: 'desc',
} as const;

const GHG_EMISSIONS_FACTOR: Content<GhgEFMetrics> = {
  topLevelTitle: 'Field GHG emission factor',
  topLevelDescription: `The average amount of field-based ${N2O} and ${CH4} emissions produced per kg yield`,
  primaryMetricKey: 'ghgEmissionsPerYield',
  uncertaintyMetricKey: 'ghgEFStdErr',
  label: 'Field GHG emission factor',
  categoryColorKey: '2',
  sortDirection: 'asc',
};

const SOC_EMISSIONS_FACTOR: Content<SocEFMetrics> = {
  topLevelTitle: 'Field SOC sequestration factor',
  topLevelDescription: `The average amount of SOC sequestered per kg yield`,
  primaryMetricKey: 'socEmissionsPerYield',
  uncertaintyMetricKey: 'socEFStdErr',
  label: 'Field SOC sequestration factor',
  categoryColorKey: '2',
  sortDirection: 'desc',
};

const NET_GHG_EMISSIONS_FACTOR: Content<NetGhgEFMetrics> = {
  topLevelTitle: 'Field net emission factor',
  topLevelDescription: `The average amount of field-based ${N2O} and ${CH4} emissions produced and SOC sequestered per kg yield`,
  primaryMetricKey: 'netGhgEmissionsPerYield',
  uncertaintyMetricKey: 'netGhgEFStdErr',
  label: 'Field net emission factor',
  categoryColorKey: '2',
  sortDirection: 'asc',
};

const COVER_CROP: Content<CoverCropMetrics> = {
  topLevelTitle: `Average ${COVER_CROP_CONTENT.name}`,
  topLevelDescription: `Percentage of ${CROPLAND_SATELLITE_VERIFIED_TEXT} cropland area practicing cover cropping`,
  primaryMetricKey: 'adoption',
  uncertaintyMetricKey: 'adoptionStdDev',
  primaryAltMetricKey: 'adoptionNotApplicableExclusive',
  uncertaintyAltMetricKey: 'adoptionStdDevNotApplicableExclusive',
  secondaryMetricKey: 'covercroppedArea',
  secondaryUncertaintyMetricKey: 'covercroppedAreaStdDev',
  trackedMetricKey: 'totalTrackedArea',
  unknownMetricKey: 'unknownArea',
  notApplicableMetricKey: 'notApplicableArea',
  label: COVER_CROP_CONTENT.name,
  categoryColorKey: COVER_CROP_CONTENT.categoryColor,
  sortDirection: 'desc',
} as const;

const GREEN_COVER_HIGH: Content<GreennessLevelMetrics> = {
  topLevelTitle: `Average ${GREENNESSLEVEL_LABEL_MAP.highArea.label}`,
  topLevelDescription: `Cropland area with high off-season green cover`,
  primaryMetricKey: 'highArea',
  unknownMetricKey: 'unknownArea',
  notApplicableMetricKey: 'notApplicableArea',
  label: GREENNESSLEVEL_LABEL_MAP.highArea.label,
  categoryColorKey: '1',
  sortDirection: 'desc',
} as const;

const GREEN_COVER_MODERATE: Content<GreennessLevelMetrics> = {
  topLevelTitle: `Average ${GREENNESSLEVEL_LABEL_MAP.moderateArea.label}`,
  topLevelDescription: `Cropland area with moderate off-season green cover`,
  primaryMetricKey: 'moderateArea',
  unknownMetricKey: 'unknownArea',
  notApplicableMetricKey: 'notApplicableArea',
  label: GREENNESSLEVEL_LABEL_MAP.moderateArea.label,
  categoryColorKey: '1',
  sortDirection: 'desc',
} as const;

const GREEN_COVER_LOW: Content<GreennessLevelMetrics> = {
  topLevelTitle: `Average ${GREENNESSLEVEL_LABEL_MAP.lowArea.label}`,
  topLevelDescription: `Cropland area with low off-season green cover`,
  primaryMetricKey: 'lowArea',
  unknownMetricKey: 'unknownArea',
  notApplicableMetricKey: 'notApplicableArea',
  label: GREENNESSLEVEL_LABEL_MAP.lowArea.label,
  categoryColorKey: '1',
  sortDirection: 'desc',
} as const;

const GREEN_COVER_BARE: Content<GreennessLevelMetrics> = {
  topLevelTitle: `Average ${GREENNESSLEVEL_LABEL_MAP.bareArea.label}`,
  topLevelDescription: `Cropland area with no off-season green cover`,
  primaryMetricKey: 'bareArea',
  unknownMetricKey: 'unknownArea',
  notApplicableMetricKey: 'notApplicableArea',
  label: GREENNESSLEVEL_LABEL_MAP.bareArea.label,
  categoryColorKey: '1',
  sortDirection: 'desc',
} as const;

const TILLAGE: Content<TillageMetrics> = {
  topLevelTitle: 'Average conservation tillage',
  topLevelDescription: `Percentage of ${CROPLAND_SATELLITE_VERIFIED_TEXT} cropland area practicing reduced till and no till`,
  primaryMetricKey: 'adoption',
  uncertaintyMetricKey: 'adoptionStdDev',
  secondaryMetricKey: 'conservationTillageArea',
  secondaryUncertaintyMetricKey: 'conservationTillageAreaStdDev',
  trackedMetricKey: 'totalTrackedArea',
  unknownMetricKey: 'unknownArea',
  label: 'conservation tillage',
  categoryColorKey: '1',
  sortDirection: 'desc',
} as const;

const GRASSLAND_TO_CROPLAND_LULC: Content<G2cLulcMetrics> = {
  topLevelTitle: 'Average land use, land use change (LULUC) - conversion',
  topLevelDescription: 'Percentage of observed land area converted from grassland to cropland',
  primaryMetricKey: 'conversion',
  secondaryMetricKey: 'convertedArea',
  trackedMetricKey: 'totalTrackedArea',
  label: 'conversion (LULUC)',
  categoryColorKey: '1',
  sortDirection: 'asc',
} as const;

const CROPLAND_TO_GRASSLAND_LULC: Content<C2gLulcMetrics> = {
  topLevelTitle: 'Average land use, land use change (LULUC) - restoration',
  topLevelDescription: 'Percentage of observed land area restored from cropland to grassland',
  primaryMetricKey: 'restoration',
  secondaryMetricKey: 'restoredArea',
  trackedMetricKey: 'totalTrackedArea',
  label: 'restoration (LULUC)',
  categoryColorKey: '1',
  sortDirection: 'desc',
} as const;

const VOLUME_WEIGHTED_GHG_KG: Content<VolumeWeightedGhgMetrics> = {
  topLevelTitle: 'Total GHG emissions (volume-capped)',
  topLevelDescription:
    'The sum of all GHG emissions produced over time, specific to the volume of the commodities purchased',
  primaryMetricKey: 'volumeWeightedGhgMass',
  label: 'total GHG Emissions (volume-capped)',
  categoryColorKey: '2',
  sortDirection: 'asc',
} as const;

const VOLUME_WEIGHTED_SOC_KG: Content<VolumeWeightedSocMetrics> = {
  topLevelTitle: 'Total SOC sequestration (volume-capped)',
  topLevelDescription:
    'The sum of all SOC sequestered over time, specific to the volume of the commodities purchased',
  primaryMetricKey: 'volumeWeightedSocMass',
  label: 'total SOC sequestration (volume-capped)',
  categoryColorKey: '2',
  sortDirection: 'desc',
} as const;

const VOLUME_WEIGHTED_NET_KG: Content<VolumeWeightedNetMetrics> = {
  topLevelTitle: 'Total net emissions (volume-capped)',
  topLevelDescription:
    'The sum of all GHG emissions produced and SOC sequestered, specific to the volume of the commodities purchased',
  primaryMetricKey: 'volumeWeightedNetMass',
  label: 'total net emissions (volume-capped)',
  categoryColorKey: '2',
  sortDirection: 'asc',
} as const;

const YIELD_PER_AREA: Content<YieldPerAreaMetrics> = {
  topLevelTitle: 'Total crop yield per area',
  topLevelDescription:
    'The average mass of the given commodity harvested per cropland area growing the given commodity',
  primaryMetricKey: 'yieldPerArea',
  label: 'crop yield',
  categoryColorKey: '8',
  sortDirection: 'desc',
} as const;

const DROUGHT_INDEX: Content<DroughtMetrics> = {
  topLevelTitle: DROUGHT_CONTENT.name,
  topLevelDescription: '',
  primaryMetricKey: 'droughtIndex',
  tierMetricKey: 'tier',
  label: DROUGHT_CONTENT.name,
  categoryColorKey: '4',
  sortDirection: 'desc',
  choroplethGroupMode: 'fixedValue',
  fixedChoroplethOptions: {
    fixedChoroplethGroupValues: Object.values(BE_TO_FE_DROUGHT_INDEX_MAP),
    isClassification: true,
  },
} as const;

const AVG_TEMPERATURE: Content<TemperatureMetrics> = {
  topLevelTitle: AVG_TEMP_CONTENT.name,
  topLevelDescription: '',
  primaryMetricKey: 'avgTemperature',
  tierMetricKey: 'tier',
  label: AVG_TEMP_CONTENT.name,
  categoryColorKey: '1',
  sortDirection: 'desc',
  choroplethGroupMode: 'biased',
  biasedChoroplethOptions: GLOBAL_TEMPERATURE_CHOROPLETH_DEFAULTS,
} as const;

const AVG_MAX_TEMPERATURE: Content<TemperatureMetrics> = {
  topLevelTitle: AVG_MAX_TEMP_CONTENT.name,
  topLevelDescription: '',
  primaryMetricKey: 'avgMaxTemperature',
  tierMetricKey: 'tier',
  label: AVG_MAX_TEMP_CONTENT.name,
  categoryColorKey: '1',
  sortDirection: 'desc',
  choroplethGroupMode: 'biased',
  biasedChoroplethOptions: GLOBAL_TEMPERATURE_CHOROPLETH_DEFAULTS,
} as const;

const YOY_TEMPERATURE: Content<TemperatureMetrics> = {
  topLevelTitle: YOY_TEMP_CHANGE_CONTENT.name,
  topLevelDescription: '',
  primaryMetricKey: 'yoyTemperatureDelta',
  tierMetricKey: 'tier',
  label: YOY_TEMP_CHANGE_CONTENT.name,
  categoryColorKey: '4',
  sortDirection: 'desc',
  choroplethGroupMode: 'quantile',
} as const;

const DAYS_OF_HEAT_STRESS: Content<HeatStressMetrics> = {
  topLevelTitle: HEAT_STRESS_CONTENT.name,
  topLevelDescription: '',
  primaryMetricKey: 'daysOfHeatStress',
  tierMetricKey: 'tier',
  label: HEAT_STRESS_CONTENT.name,
  categoryColorKey: '4',
  sortDirection: 'desc',
  choroplethGroupMode: 'fixedValue',
  fixedChoroplethOptions: {
    fixedChoroplethGroupValues: [0, 2, 5, 10, 20, 40, 75, 365],
  },
} as const;

const HEAT_STRESS_OCCURRENCES: Content<HeatStressMetrics> = {
  topLevelTitle: HEATWAVE_OCCURRENCES_CONTENT.name,
  topLevelDescription: '',
  primaryMetricKey: 'heatStressOccurrences',
  tierMetricKey: 'tier',
  label: HEATWAVE_OCCURRENCES_CONTENT.name,
  categoryColorKey: '4',
  sortDirection: 'desc',
  choroplethGroupMode: 'fixedValue',
  fixedChoroplethOptions: {
    fixedChoroplethGroupValues: [0, 2, 5, 10, 20, 40, 75, 365],
  },
} as const;

export const CONTENT = {
  cover_crop: COVER_CROP,
  tillage: TILLAGE,
  greenness_level_area_m2_high: GREEN_COVER_HIGH,
  greenness_level_area_m2_moderate: GREEN_COVER_MODERATE,
  greenness_level_area_m2_low: GREEN_COVER_LOW,
  greenness_level_area_m2_bare: GREEN_COVER_BARE,
  ghg_kg_per_m2: GHG_KG_PER_M2,
  soc_kg_per_m2: SOC_KG_PER_M2,
  ghg_emissions_factor: GHG_EMISSIONS_FACTOR,
  soc_emissions_factor: SOC_EMISSIONS_FACTOR,
  net_ghg_emissions_factor: NET_GHG_EMISSIONS_FACTOR,
  grassland_to_cropland_lulc: GRASSLAND_TO_CROPLAND_LULC,
  cropland_to_grassland_lulc: CROPLAND_TO_GRASSLAND_LULC,
  volume_weighted_ghg_kg: VOLUME_WEIGHTED_GHG_KG,
  volume_weighted_soc_kg: VOLUME_WEIGHTED_SOC_KG,
  volume_weighted_net_kg: VOLUME_WEIGHTED_NET_KG,
  yield_per_area: YIELD_PER_AREA,
  drought_index: DROUGHT_INDEX,
  avg_temperature: AVG_TEMPERATURE,
  avg_max_temperature: AVG_MAX_TEMPERATURE,
  yoy_temperature_delta: YOY_TEMPERATURE,
  days_of_heat_stress: DAYS_OF_HEAT_STRESS,
  heat_stress_occurrences: HEAT_STRESS_OCCURRENCES,
} as const;

export const AVAILABLE_KPIS_TO_LABELS: Record<AVAILABLE_KPIS, string> = {
  cover_crop: COVER_CROP.label,
  greenness_level_area_m2_high: GREEN_COVER_HIGH.label,
  greenness_level_area_m2_moderate: GREEN_COVER_MODERATE.label,
  greenness_level_area_m2_low: GREEN_COVER_LOW.label,
  greenness_level_area_m2_bare: GREEN_COVER_BARE.label,
  tillage: TILLAGE.label,
  ghg_kg_per_m2: GHG_KG_PER_M2.label,
  soc_kg_per_m2: SOC_KG_PER_M2.label,
  ghg_emissions_factor: GHG_EMISSIONS_FACTOR.label,
  soc_emissions_factor: SOC_EMISSIONS_FACTOR.label,
  net_ghg_emissions_factor: NET_GHG_EMISSIONS_FACTOR.label,
  grassland_to_cropland_lulc: GRASSLAND_TO_CROPLAND_LULC.label,
  cropland_to_grassland_lulc: CROPLAND_TO_GRASSLAND_LULC.label,
  volume_weighted_ghg_kg: VOLUME_WEIGHTED_GHG_KG.label,
  volume_weighted_soc_kg: VOLUME_WEIGHTED_SOC_KG.label,
  volume_weighted_net_kg: VOLUME_WEIGHTED_NET_KG.label,
  yield_per_area: YIELD_PER_AREA.label,
  drought_index: DROUGHT_INDEX.label,
  avg_temperature: AVG_TEMPERATURE.label,
  avg_max_temperature: AVG_MAX_TEMPERATURE.label,
  yoy_temperature_delta: YOY_TEMPERATURE.label,
  days_of_heat_stress: DAYS_OF_HEAT_STRESS.label,
  heat_stress_occurrences: HEAT_STRESS_OCCURRENCES.label,
};

export const TOP_PANEL_NOTIFICATION_ALL_MATCH_CONDITIONS: Array<{
  rule: (
    filtersState: Pick<FiltersState, 'boundaryType' | 'isTimeTrendEnabled' | 'subsectionIds'>
  ) => boolean;
  message: string;
}> = [
  {
    rule: filtersState => filtersState.boundaryType === 'admin2',
    message:
      'Average metrics can differ when aggregated at the county and district level because all fields of a county or district are included when even a partial county or district intersects with the selected subregions.',
  },
  {
    rule: filtersState =>
      filtersState.isTimeTrendEnabled &&
      filtersState.boundaryType === 'subsection' &&
      filtersState.subsectionIds.length > TIMETREND_MULTILINE_MAX_BOUNDARY_COUNT,
    message:
      'The multiline annualized chart is not available when selecting more than five subregions.',
  },
  {
    rule: filtersState => filtersState.isTimeTrendEnabled && filtersState.boundaryType === 'admin2',
    message:
      'The multiline annualized chart in not available when aggregated by counties and districts.',
  },
];

export const DATA_SCENARIO_SELECT_DISABLED_RULES: Array<{
  rule: (filtersState: Pick<FiltersState, 'kpi' | 'boundaryType'>) => boolean;
  message: (filtersState: Pick<FiltersState, 'kpi' | 'boundaryType'>) => string;
}> = [
  {
    rule: filtersState =>
      isDefined(filtersState.kpi) &&
      KPIS_INCOMPATIBLE_WITH_DATA_SCENARIO.includes(filtersState.kpi),
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    message: filtersState => `${AVAILABLE_KPIS_TO_LABELS[filtersState.kpi!]} KPI reporting`,
  },
  {
    rule: filtersState =>
      isDefined(filtersState.boundaryType) && ['admin2'].includes(filtersState.boundaryType),
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    message: filtersState => `${BOUNDARY_TYPE_TO_TEXT_LABEL[filtersState.boundaryType!]} reporting`,
  },
];

export const MULTICROP_SELECT_DISABLED_RULES: Array<{
  rule: (filtersState: Pick<FiltersState, 'kpi'>) => boolean;
  message: (filtersState: Pick<FiltersState, 'kpi'>) => string;
}> = [
  {
    rule: filtersState =>
      isDefined(filtersState['kpi']) &&
      KPIS_INCOMPATIBLE_WITH_MULTIPLE_CROPS.includes(filtersState.kpi),
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    message: filtersState => `${AVAILABLE_KPIS_TO_LABELS[filtersState['kpi']!]} KPI reporting`,
  },
];

export const AGGREGATION_LEVEL_SELECT_DISABLED_RULES: Array<{
  rule: (
    filtersState: Pick<FiltersState, 'dataScenarioId' | 'kpi'>,
    boundaryType: BoundaryType
  ) => boolean;
  message: (
    filtersState: Pick<FiltersState, 'dataScenarioId' | 'kpi'>,
    boundaryType: BoundaryType
  ) => string;
}> = [
  {
    rule: (filtersState, boundaryType) =>
      boundaryType === 'admin2' && isDefined(filtersState['dataScenarioId']),
    message: () => 'data scenario filtering',
  },
  {
    rule: (filtersState, boundaryType) =>
      boundaryType === 'admin2' &&
      isDefined(filtersState['kpi']) &&
      KPIS_INCOMPATIBLE_WITH_ADMIN2.includes(filtersState['kpi']),
    message: filtersState =>
      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
      `${AVAILABLE_KPIS_TO_LABELS[filtersState.kpi!]} KPI reporting`,
  },
];

export const KPI_SELECT_DISABLED_RULES: Array<{
  rule: (filtersState: FiltersState, kpi: AVAILABLE_KPIS) => boolean;
  message: (filtersState: FiltersState, kpi: AVAILABLE_KPIS) => string;
}> = [
  {
    rule: (filtersState, kpi) =>
      isDefined(filtersState['dataScenarioId']) &&
      KPIS_INCOMPATIBLE_WITH_DATA_SCENARIO.includes(kpi),
    message: () => 'data scenario filtering',
  },
  {
    rule: (filtersState, kpi) =>
      isDefined(filtersState['boundaryType']) &&
      filtersState['boundaryType'] === 'admin2' &&
      KPIS_INCOMPATIBLE_WITH_ADMIN2.includes(kpi),
    message: filtersState =>
      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
      `${BOUNDARY_TYPE_TO_TEXT_LABEL[filtersState.boundaryType!]} boundary reporting`,
  },
  {
    rule: (filtersState, kpi) =>
      isDefined(filtersState['cropIds']) &&
      filtersState['cropIds'].length > 1 &&
      KPIS_INCOMPATIBLE_WITH_MULTIPLE_CROPS.includes(kpi),
    message: () => `multi-crop aggregation`,
  },
];
