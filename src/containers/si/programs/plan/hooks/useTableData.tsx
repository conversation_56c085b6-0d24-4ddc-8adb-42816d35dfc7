import isFunction from 'lodash/isFunction';
import React, {useMemo} from 'react';

import {
  Button,
  Chip,
  Stack,
  SvgIcon,
  TypographyOverflow,
  type Lab,
} from '@regrow-internal/design-system';

import type {MeasurementEnum} from '_common/utils/measurement-unit-options';
import {isDefined, isNil, isUndefined} from '_common/utils/typeGuards';

import type {PlanScenarioResponse, RankingMethod} from 'containers/si/api/apiTypes';
import {useFetchPlanInterventionTypes} from 'containers/si/api/swr/hooks/useFetchPlanInterventionTypes';
import {useGetCropLabelById} from 'containers/si/hooks/useGetCropLabelById';
import {useGetRegionById} from 'containers/si/hooks/useGetRegionById';
import {useTabAccess} from 'containers/si/hooks/useTabAccess';
import {makeAreaM2MetricDetail} from 'containers/si/programs/helpers/area_m2.transformation';
import {COST_PER_AREA_FORMATTER} from 'containers/si/programs/helpers/cost_per_m2.transformation';
import {makePurchaseVolumeMetrics} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.purchase_volume_kg.transformation';
import {
  ChipList,
  ChipListSkeleton,
} from 'containers/si/programs/plan/components/Dashboard/components/ChipList';
import {TableHeaderContentWithTooltip} from 'containers/si/programs/plan/components/TableHeaderContentWithTooltip';
import {
  interventionToIconType,
  interventionToLabel,
  PLAN_PROJECTED_YEAR,
  trendToIconType,
} from 'containers/si/programs/plan/constants';
import {TABLE_TOOLTIP_CONTENT} from 'containers/si/programs/plan/content';
import {
  getCellDisplayFromMetricDetail,
  getSingleInterventionIds,
} from 'containers/si/programs/plan/helpers';
import {makeTotalAbatementCostFromAdoptionRates} from 'containers/si/programs/plan/helpers/helpers';
import {
  transformAbatementPotentialToPlanMetric,
  transformAbatementPotentialToTotalPlanMetric,
} from 'containers/si/programs/plan/transformers/topLevelViewContent.transformation';
import {isHeaderTooltip} from 'containers/si/programs/plan/typeGuards';
import type {RowModel} from 'containers/si/programs/plan/types';
import {getUnit} from 'containers/si/utils/convert';

export const useTableData = ({
  planScenarios,
  rankingMethod,
  userUnitsSystem,
  updateProjection,
  editProjection,
}: {
  planScenarios: Array<PlanScenarioResponse> | undefined;
  rankingMethod: RankingMethod;
  userUnitsSystem: MeasurementEnum;
  updateProjection?: (
    planScenarioId: PlanScenarioResponse['id'],
    isSelected: boolean
  ) => Promise<void>;
  editProjection?: (projectionId: RowModel) => void;
}): {
  columns: Array<Lab.GridColDef<RowModel>>;
  rows: Array<RowModel>;
} => {
  const {getSubsectionById} = useGetRegionById();
  const {
    getCropIconById,
    getSISupportedCropLabelById,
    isLoading: isLoadingCropHelpers,
  } = useGetCropLabelById();
  const {data: planInterventionTypesResponse} = useFetchPlanInterventionTypes();
  const {getHasTabAccess} = useTabAccess();
  const hasVolumesAccess =
    getHasTabAccess('commodity_volumes') && rankingMethod === 'volume_weighted_abatement';

  const rows: Array<RowModel> = useMemo(() => {
    if (isNil(planScenarios) || isUndefined(planInterventionTypesResponse)) return [];

    const scenarioRowMap: Array<RowModel> = planScenarios.map(scenario => {
      return {
        id: scenario.id,
        subregionId: scenario.subregion_id,
        commodityId: scenario.crop_type,
        interventionIds: getSingleInterventionIds(
          scenario.intervention_type_id,
          planInterventionTypesResponse.intervention_types
        ),
        abatementPotential: transformAbatementPotentialToPlanMetric('net_vs_bau_kg_per_m2')(
          scenario.annualized_data?.[PLAN_PROJECTED_YEAR]?.abatement_potential_kg_per_m2,
          userUnitsSystem
        ),
        ghgReductionPotential: transformAbatementPotentialToPlanMetric('ghg_vs_bau_kg_per_m2')(
          scenario.annualized_data?.[PLAN_PROJECTED_YEAR]?.abatement_potential_kg_per_m2,
          userUnitsSystem
        ),
        socSequestrationPotential: transformAbatementPotentialToPlanMetric('soc_vs_bau_kg_per_m2')(
          scenario.annualized_data?.[PLAN_PROJECTED_YEAR]?.abatement_potential_kg_per_m2,
          userUnitsSystem
        ),
        totalAbatementPotential: transformAbatementPotentialToTotalPlanMetric(
          scenario.annualized_data?.[PLAN_PROJECTED_YEAR]?.abatement_potential_kg_per_m2,
          scenario.annualized_data?.[PLAN_PROJECTED_YEAR]?.intervention_area_m2
        ),
        currentArea: makeAreaM2MetricDetail(userUnitsSystem)(
          scenario.annualized_data?.[PLAN_PROJECTED_YEAR]?.total_area_m2
        ),
        currentAreaWithInterventions: makeAreaM2MetricDetail(userUnitsSystem)(
          scenario.avg_historical_practicing_area_m2
        ),
        interventionArea: makeAreaM2MetricDetail(userUnitsSystem)(
          scenario.annualized_data?.[PLAN_PROJECTED_YEAR]?.intervention_area_m2
        ),
        totalAbatementCost: makeTotalAbatementCostFromAdoptionRates({
          annualizedCosts: scenario.annualized_costs,
          annualizedAdoptionRates: scenario.annualized_adoption_rates,
          currentAreaM2: scenario.annualized_data?.[PLAN_PROJECTED_YEAR]?.total_area_m2,
          eligibleAreaM2: scenario.annualized_data?.[PLAN_PROJECTED_YEAR]?.eligible_area_m2,
        }),
        currentPurchaseVolume: makePurchaseVolumeMetrics({
          purchase_volume_kg: scenario.volume_kg,
        }),
        isSelected: scenario.is_selected,
      };
    });

    return scenarioRowMap;
  }, [planScenarios, planInterventionTypesResponse, userUnitsSystem]);

  const columns: Array<Lab.GridColDef<RowModel>> = [
    {
      headerName: 'Subregion',
      field: 'subregion',
      type: 'text',
      minWidth: 150,
      sortable: false,
      renderCell: ({row: {subregionId}}) => {
        return <TypographyOverflow>{getSubsectionById(subregionId)?.name}</TypographyOverflow>;
      },
    },
    {
      headerName: 'Commodity',
      field: 'commodity',
      type: 'text',
      minWidth: 145,
      sortable: false,
      renderCell: ({row: {commodityId}}) => {
        if (isLoadingCropHelpers) return <ChipListSkeleton length={1} />;
        return (
          <Chip
            size="small"
            variant="outlined"
            label={getSISupportedCropLabelById(commodityId)}
            icon={<SvgIcon type={getCropIconById(commodityId)} />}
          />
        );
      },
    },
    {
      headerName: 'Interventions',
      field: 'interventions',
      minWidth: 300,
      type: 'text',
      sortable: false,
      renderCell: ({row: {interventionIds}}) => {
        if (isNil(interventionIds)) return <div>Unavailable interventions</div>;

        return (
          <Stack gap={2} direction="row">
            <ChipList
              labels={interventionIds}
              maxItems={2}
              renderLabel={intervention => interventionToLabel[intervention]}
              renderIcon={intervention => <SvgIcon type={interventionToIconType[intervention]} />}
              color="2"
            />
          </Stack>
        );
      },
    },
    {
      headerName: `Intervention ${
        getUnit(userUnitsSystem).unitName.plural
      } in ${PLAN_PROJECTED_YEAR}`,
      field: 'interventionArea',
      type: 'text',
      flex: 1,
      minWidth: 175,
      sortable: false,
      headerAlign: 'center',
      renderHeader: renderTooltipHeader(userUnitsSystem),
      valueGetter: ({row: {interventionArea}}) => getCellDisplayFromMetricDetail(interventionArea),
    },
    {
      headerName: `Abatement potential per ${
        getUnit(userUnitsSystem).unitName.singular
      } in ${PLAN_PROJECTED_YEAR}`,
      field: 'abatementPotential',
      type: 'text',
      flex: 1,
      minWidth: 175,
      headerAlign: 'center',
      sortable: true,
      renderCell: ({row: {abatementPotential}}) =>
        getCellDisplayFromMetricDetail(abatementPotential),
      valueGetter: ({row: {abatementPotential}}) =>
        isDefined(abatementPotential) ? abatementPotential.value : null,
      renderHeader: renderTooltipHeader(userUnitsSystem),
    },
    {
      headerName: `GHG emission reduction potential per ${
        getUnit(userUnitsSystem).unitName.singular
      } in ${PLAN_PROJECTED_YEAR}`,
      field: 'ghgReductionPotential',
      type: 'text',
      flex: 1,
      minWidth: 175,
      headerAlign: 'center',
      sortable: true,
      renderCell: ({row: {ghgReductionPotential}}) =>
        getCellDisplayFromMetricDetail(ghgReductionPotential),
      valueGetter: ({row: {ghgReductionPotential}}) =>
        isDefined(ghgReductionPotential) ? ghgReductionPotential.value : null,
      renderHeader: renderTooltipHeader(userUnitsSystem),
    },
    {
      headerName: `SOC sequestration potential per ${
        getUnit(userUnitsSystem).unitName.singular
      } in ${PLAN_PROJECTED_YEAR}`,
      field: 'socSequestrationPotential',
      type: 'text',
      flex: 1,
      minWidth: 175,
      headerAlign: 'center',
      sortable: true,
      renderCell: ({row: {socSequestrationPotential}}) =>
        getCellDisplayFromMetricDetail(socSequestrationPotential),
      valueGetter: ({row: {socSequestrationPotential}}) =>
        isDefined(socSequestrationPotential) ? socSequestrationPotential.value : null,
      renderHeader: renderTooltipHeader(userUnitsSystem),
    },
    {
      headerName: `Total Abatement potential in ${PLAN_PROJECTED_YEAR}`,
      field: 'totalAbatementPotential',
      type: 'text',
      flex: 1,
      minWidth: 175,
      headerAlign: 'center',
      sortable: false,
      renderHeader: renderTooltipHeader(userUnitsSystem),
      renderCell: ({row: {totalAbatementPotential}}) => {
        if (isNil(totalAbatementPotential))
          return (
            <Stack direction="row" alignContent="center" gap={1}>
              <SvgIcon type={trendToIconType.none} /> --
            </Stack>
          );

        const {
          formattedValue,
          trend,
          unitName: {abbr},
        } = totalAbatementPotential;

        return (
          <Stack direction="row" alignContent="center" gap={1}>
            <SvgIcon type={trendToIconType[trend]} /> {`${formattedValue} ${abbr}`}
          </Stack>
        );
      },
    },
    {
      headerName: 'Total abatement cost',
      field: 'totalAbatementCost',
      type: 'text',
      flex: 1,
      minWidth: 175,
      sortable: false,
      headerAlign: 'center',
      renderHeader: renderTooltipHeader(userUnitsSystem),
      renderCell: ({row: {totalAbatementCost}}) => {
        if (isNil(totalAbatementCost)) return '--';

        return `$${COST_PER_AREA_FORMATTER(totalAbatementCost)}`;
      },
    },
    {
      headerName: 'Current purchase volume',
      field: 'currentPurchaseVolume',
      type: 'text',
      flex: 1,
      minWidth: 175,
      sortable: false,
      headerAlign: 'center',
      renderHeader: renderTooltipHeader(userUnitsSystem),
      renderCell: ({row: {currentPurchaseVolume}}) => {
        if (isNil(currentPurchaseVolume)) return '--';

        return `${currentPurchaseVolume.volumePurchased.formattedValue} ${currentPurchaseVolume.volumePurchased.unit}`;
      },
    },
    {
      headerName: `Current ${getUnit(userUnitsSystem).unitName.plural}`,
      field: 'currentArea',
      type: 'text',
      flex: 1,
      minWidth: 150,
      sortable: true,
      headerAlign: 'center',
      renderHeader: renderTooltipHeader(userUnitsSystem),
      renderCell: ({row: {currentArea}}) => getCellDisplayFromMetricDetail(currentArea),
      valueGetter: ({row: {currentArea}}) => (isDefined(currentArea) ? currentArea.value : null),
    },
    {
      headerName: `Current ${getUnit(userUnitsSystem).unitName.plural} with intervention practices`,
      field: 'currentAreaWithInterventions',
      type: 'text',
      flex: 1,
      minWidth: 175,
      renderHeader: renderTooltipHeader(userUnitsSystem),
      sortable: true,
      headerAlign: 'center',
      renderCell: ({row: {currentAreaWithInterventions}}) =>
        getCellDisplayFromMetricDetail(currentAreaWithInterventions),
      valueGetter: ({row: {currentAreaWithInterventions}}) =>
        isDefined(currentAreaWithInterventions) ? currentAreaWithInterventions.value : null,
    },
  ];

  // Check for volumes access
  const filteredColumns = hasVolumesAccess
    ? columns
    : columns.filter(col => col.field !== 'currentPurchaseVolume');

  isDefined(updateProjection) &&
    filteredColumns.push({
      headerName: '',
      field: 'actions',
      type: 'actions',
      align: 'left',
      flex: 1,
      minWidth: 230,
      sortable: false,
      getActions: ({id, row, row: {id: pId, isSelected}}) => {
        const options = [
          isSelected ? (
            <Button
              color="secondary"
              variant="outlined"
              size="small"
              onClick={() => updateProjection(pId, false)}
            >
              Remove from plan
            </Button>
          ) : (
            <Button key={id} size="small" onClick={() => updateProjection(pId, true)}>
              Add to plan
            </Button>
          ),
        ];

        if (isDefined(editProjection))
          options.unshift(
            <Button
              key={pId}
              color="secondary"
              variant="outlined"
              size="small"
              onClick={() => editProjection(row)}
            >
              View / adjust
            </Button>
          );

        return options;
      },
    });

  return {
    columns: filteredColumns,
    rows,
  };
};

const renderTooltipHeader: (
  userUnitsSystem?: MeasurementEnum
) => Lab.GridColDef<RowModel>['renderHeader'] =
  userUnitsSystem =>
  ({colDef: {headerName}, field}) => {
    const fieldTooltipContent = isHeaderTooltip(field) ? TABLE_TOOLTIP_CONTENT[field] : '';

    return (
      <TableHeaderContentWithTooltip
        headerName={headerName}
        tooltipContent={
          isFunction(fieldTooltipContent) && isDefined(userUnitsSystem)
            ? fieldTooltipContent(userUnitsSystem)
            : fieldTooltipContent
        }
      />
    );
  };
