import React, {useCallback, useMemo, useState, type FC} from 'react';
import type {KeyedMutator} from 'swr';

import {
  Box,
  DialogContent,
  Lab,
  Pagination,
  Paper,
  SimpleDialog,
  Stack,
  SvgIcon,
} from '@regrow-internal/design-system';

import {useAppSelector} from 'store/useRedux';

import {getTypedKeys} from '_common/utils/object';
import {isDefined} from '_common/utils/typeGuards';

import {selectMeasurement} from 'containers/login/login-selectors';
import type {
  PlanResponse,
  PlanScenarioListRequest,
  PlanScenarioListSort,
  PlanScenarioResponse,
  PlanScenarioSortOptions,
  RankingMethod,
} from 'containers/si/api/apiTypes';
import SIApi from 'containers/si/api/si';
import {useFetchPlanScenarioList} from 'containers/si/api/swr/hooks/useFetchPlanScenarioList';
import {ProgramCropsSelect} from 'containers/si/components/filters/CropsSelect/ProgramCropsSelect';
import {RegionsSelect} from 'containers/si/components/filters/RegionsSelect';
import {useCommonFilters} from 'containers/si/hooks/useCommonFilters';
import {InterventionsSelect} from 'containers/si/programs/plan/components/Dashboard/components/InterventionsSelect';
import {MRVProjectionSettingsDialog} from 'containers/si/programs/plan/components/Dashboard/components/MRVProjectionSettingsDialog/MRVProjectionSettingsDialog';
import {interventionToLabel, PLAN_PROJECTED_YEAR} from 'containers/si/programs/plan/constants';
import {useTableData} from 'containers/si/programs/plan/hooks/useTableData';
import type {RowModel} from 'containers/si/programs/plan/types';

interface ManageMRVProjectionsDialogProps {
  programId: number;
  planAnnualizedAdoptionRates: PlanResponse['annualized_adoption_rates'];
  planAnnualizedCosts: PlanResponse['annualized_costs'];
  onClose: (open: boolean) => void;
  mutatePlan: KeyedMutator<PlanResponse>;
  rankingMethod: RankingMethod;
}

export const ManageMRVProjectionsDialog: FC<ManageMRVProjectionsDialogProps> = ({
  programId,
  rankingMethod,
  planAnnualizedAdoptionRates,
  planAnnualizedCosts,
  onClose,
  mutatePlan,
}) => {
  const userUnitsSystem = useAppSelector(selectMeasurement);
  const {filtersState, setFilterState, validCropIds} = useCommonFilters();
  const [interventionFilter, setInterventionFilter] = React.useState(
    getTypedKeys(interventionToLabel).map(Number) //Getting all selected to start
  );
  const [page, setPage] = React.useState(0);
  const [rowsPerPage, setRowsPerPage] = React.useState(10);
  const [sortPayload, setSortPayload] = React.useState<PlanScenarioListSort>();

  const requestPayload: PlanScenarioListRequest = useMemo(
    () => ({
      year_of_interest: Number(PLAN_PROJECTED_YEAR),
      subregion_ids: filtersState.subsectionIds,
      intervention_type_ids: interventionFilter,
      crop_ids: validCropIds,
      limit: rowsPerPage,
      offset: rowsPerPage * page,
      sort: sortPayload,
    }),
    [rowsPerPage, page, filtersState, interventionFilter, validCropIds, sortPayload]
  );

  const {
    data: planScenarioListResponse,
    isLoading,
    mutate: mutateProjectionsList,
  } = useFetchPlanScenarioList(requestPayload);

  const handleSort = useCallback(async (sortModel: Lab.GridSortModel) => {
    const sortableColumns: Record<string, PlanScenarioSortOptions> = {
      abatementPotential: 'net_vs_bau_kg_per_m2',
      ghgReductionPotential: 'ghg_vs_bau_kg_per_m2',
      socSequestrationPotential: 'soc_vs_bau_kg_per_m2',
      currentAreaWithInterventions: 'avg_historical_practicing_area_m2',
      currentArea: 'total_area_m2',
    };

    if (!isDefined(sortModel[0])) return;

    const {field, sort} = sortModel[0];
    const sortColumn = sortableColumns[field];

    if (isDefined(sortColumn))
      setSortPayload({
        sort_by: sortColumn,
        asc: sort === 'asc',
      });
  }, []);

  /* Adjust projection */
  const [editableProjection, setEditableProjection] = useState<RowModel | null>(null);
  const [isEditProjectionDialogOpen, setIsEditProjectionDialogOpen] = useState(true);
  const editableProjectionAdoptionRates = useMemo(() => {
    return planScenarioListResponse?.data.find(
      projection => projection.id === editableProjection?.id
    )?.annualized_adoption_rates;
  }, [editableProjection?.id, planScenarioListResponse?.data]);

  const editableProjectionEligibleArea = useMemo(() => {
    return planScenarioListResponse?.data.find(
      projection => projection.id === editableProjection?.id
    )?.annualized_data?.[PLAN_PROJECTED_YEAR]?.eligible_area_m2;
  }, [editableProjection?.id, planScenarioListResponse?.data]);

  const editableProjectionCosts = useMemo(() => {
    return planScenarioListResponse?.data.find(
      projection => projection.id === editableProjection?.id
    )?.annualized_costs;
  }, [editableProjection?.id, planScenarioListResponse?.data]);

  const onEditProjection = (projectionRow: RowModel) => {
    setEditableProjection(projectionRow);
    setIsEditProjectionDialogOpen(true);
  };

  const updatePlanScenario = async (
    planScenarioId: PlanScenarioResponse['id'],
    isSelected: boolean
  ) => {
    await SIApi.updatePlanScenario(programId, planScenarioId, {is_selected: isSelected});
    await mutateProjectionsList();
  };

  const {rows, columns} = useTableData({
    planScenarios: planScenarioListResponse?.data,
    rankingMethod,
    userUnitsSystem,
    updateProjection: updatePlanScenario,
    editProjection: onEditProjection,
  });

  const handleClose = async (open: boolean) => {
    await mutatePlan();
    onClose(open);
  };

  return (
    <>
      <SimpleDialog
        open
        title="Manage MRV projections"
        description="Below are a list of the additional MRV projections based on your configured subregions and commodities."
        onClose={() => handleClose(false)}
        maxWidth="xl"
      >
        <DialogContent>
          <Stack gap={5}>
            <Stack direction="row" justifyContent="flex-end">
              <Pagination
                count={planScenarioListResponse?.count ?? 0}
                page={page}
                onSetPage={setPage}
                rowsPerPage={rowsPerPage}
                onSetRowsPerPage={setRowsPerPage}
                showFirstButton
                showLastButton
                showPages
                showDisplayedRowsLabel
                rowsPerPageOptions={[
                  {label: '10', value: 10},
                  {label: '25', value: 25},
                  {label: '50', value: 50},
                ]}
              />
            </Stack>
            <Paper variant="outlined">
              <Stack direction="row" justifyContent="space-between">
                <Stack p={3} direction="row" alignItems="center" gap={3} py={2}>
                  <Box alignSelf="center">
                    <SvgIcon fontSize="h4" type="filter" />
                  </Box>
                  <Box>
                    <RegionsSelect
                      regionIds={filtersState.subsectionIds}
                      onRegionsChange={subsectionIds => setFilterState({subsectionIds})}
                    />
                  </Box>
                  <ProgramCropsSelect
                    cropIds={filtersState.cropIds}
                    onCropsChange={cropIds => setFilterState({cropIds})}
                    regionIds={filtersState.subsectionIds}
                  />
                  <Box minWidth={theme => theme.fixedWidths.xs}>
                    <InterventionsSelect
                      interventionIds={interventionFilter ?? []}
                      setInterventionIds={setInterventionFilter}
                    />
                  </Box>
                </Stack>
              </Stack>
            </Paper>
            <Box minHeight={600}>
              <Paper>
                <Lab.DataGrid
                  columns={columns}
                  rows={rows}
                  disableRowSelectionOnClick
                  sortingMode="server"
                  loading={isLoading}
                  onSortModelChange={handleSort}
                />
              </Paper>
            </Box>
          </Stack>
        </DialogContent>
      </SimpleDialog>
      {isEditProjectionDialogOpen &&
        editableProjectionAdoptionRates &&
        editableProjection &&
        editableProjectionCosts && (
          <MRVProjectionSettingsDialog
            programId={programId}
            projectionRow={editableProjection}
            onClose={() => {
              setIsEditProjectionDialogOpen(false);
              setEditableProjection(null);
            }}
            annualizedAdoptionRates={editableProjectionAdoptionRates}
            annualizedCosts={editableProjectionCosts}
            planAnnualizedAdoptionRates={planAnnualizedAdoptionRates}
            planAnnualizedCosts={planAnnualizedCosts}
            eligibleAreaM2={editableProjectionEligibleArea}
            mutateData={mutateProjectionsList}
            rankingMethod={rankingMethod}
          />
        )}
    </>
  );
};
