import type {ReactNode} from 'react';

import type {Lab} from '@regrow-internal/design-system';

import type {PlanComputeRequest, PlanResponse, PlanYearData} from 'containers/si/api/apiTypes';
import type {PurchaseVolumeKgMetrics} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.purchase_volume_kg.transformation';
import type {MetricDetail, UnitType} from 'containers/si/utils/value.types';

export type Trend = 'up' | 'down' | 'none';

export type MetricDetailTrend<V extends UnitType> = MetricDetail<V> & {
  trend: Trend;
};

export type PlanMetricDetail<UnitT extends UnitType, U extends UnitT = UnitT> = {
  topLevelMetric: MetricDetail<UnitT>;
  yearTrendMetric: MetricDetailTrend<U>;
  BAUTrendMetric: MetricDetailTrend<U>;
};

export type TopLevelViewContent<UnitT extends UnitType, AreaT extends UnitType> = {
  scenarioCount: number | undefined;
  subregionIds: Array<number>;
  cropIds: Array<number>;
  interventionIds: Array<PlanSingleIntervention>;
  totalEmissionsLookup: Record<Emission, PlanMetricDetail<UnitT> | null>;
  regenArea: PlanMetricDetail<AreaT> | null;
  interventionArea: Pick<PlanMetricDetail<AreaT>, 'topLevelMetric'> | null;
  totalProducerIncentives: number | null;
  ROILookup: Record<AbatementROIOptionType, number> | null;
};

export type PlanDashboardData = {
  topLevelViewContent: TopLevelViewContent<UnitType, UnitType>;
  tableViewContent: {
    columns: Array<Lab.GridColDef<RowModel>>;
    rows: Array<RowModel>;
  };
};

export type PlanSingleIntervention = 2 | 3 | 4 | 5;

/**Table types */
export type RowModel = {
  id: number;
  subregionId: number;
  commodityId: number;
  interventionIds: Array<PlanSingleIntervention> | null;
  currentArea: MetricDetail<UnitType> | null;
  currentAreaWithInterventions: MetricDetail<UnitType> | null;
  interventionArea: MetricDetail<UnitType> | null;
  abatementPotential: MetricDetailTrend<UnitType> | null;
  ghgReductionPotential: MetricDetailTrend<UnitType> | null;
  socSequestrationPotential: MetricDetailTrend<UnitType> | null;
  totalAbatementPotential: MetricDetailTrend<UnitType> | null;
  totalAbatementCost: number | null;
  currentPurchaseVolume: PurchaseVolumeKgMetrics | null;
  isSelected: boolean;
};

type HeadersWithTooltip =
  | 'currentAreaWithInterventions'
  | 'interventionArea'
  | 'currentArea'
  | 'abatementPotential'
  | 'ghgReductionPotential'
  | 'socSequestrationPotential'
  | 'totalAbatementPotential'
  | 'totalAbatementCost'
  | 'currentPurchaseVolume';

export type HeaderTooltip = keyof Pick<RowModel, HeadersWithTooltip>;

export type InterventionLabel =
  | 'Business as usual'
  | 'Cover crops'
  | 'Reduced till'
  | 'No till'
  | 'Reduce N by 10%'
  | 'Cover crop & no till'
  | 'Cover crop & reduced till'
  | 'Cover crop & reduce N by 10%'
  | 'No till & reduce N by 10%'
  | 'Reduced till & reduce N by 10%'
  | 'Cover crop & reduced till & reduce N by 10%'
  | 'Cover crop & no till & reduce N by 10%';

export type EmissionPlanYearData = Pick<PlanYearData, 'ghg_kg' | 'soc_kg' | 'net_kg'>;

export type Emission = keyof EmissionPlanYearData;

export type AnnualizedMetricLookup = Record<string, MetricDetail<UnitType>>;

export type Costs =
  | 'producer_incentive_per_m2'
  | 'additional_costs_per_m2'
  | 'benchmark_cost_per_kgco2e';
export type CostsMap = Record<Costs, Record<string, number>>;

export type AbatementROIOptionType = 'total' | 'perArea' | 'perEmissionsReduction';
export enum MRVProjectionSettingsDialogTab {
  ADOPTION_RATES = 'adoptionRates',
  COSTS = 'costs',
}

export enum PlanDialogTab {
  RANKING_METHOD = 'rankingMethod',
  ADOPTION_RATES = 'adoptionRates',
}

export type PlanScope = keyof Pick<PlanResponse, 'all_plan_data' | 'selected_scenarios_only_data'>;

export type ComputeKey = 'plan-scenarios' | 'all';

export type ComputeSelectionOption = {
  computeKey: ComputeKey;
  payload: PlanComputeRequest;
  description: {
    title: ReactNode;
    subtitle: ReactNode;
  };
};
