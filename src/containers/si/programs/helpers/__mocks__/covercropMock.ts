import {
  KPICoverCropAnnualizedMock,
  KPICoverCropCropTypeSummaryMock,
  KPICoverCropMock,
  KPICoverCropSubsectionAnnualizedMock,
  KPICoverCropSubsectionMock,
} from 'containers/si/__mocks__/KPICoverCropMock';
import type {CoverCropMetrics} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.cover_crop.transformation';
import type {UnitDetail, UnitType} from 'containers/si/utils/value.types';

export const COVERCROP_ADOPTION_UNIT_DETAIL_MOCK: UnitDetail<UnitType> = {
  unit: 'unit-interval',
  unitName: {
    singular: '% of cropland area practicing',
    plural: '% of cropland area practicing',
    abbr: 'adoption',
  },
};

export const COVERCROP_METRICUNITS_AREA_UNITDETAIL_MOCK: UnitDetail<UnitType> = {
  unit: 'ha',
  unitName: {
    singular: 'hectare',
    plural: 'hectares',
    abbr: 'ha',
  },
};

export const COVERCROP_IMPERIALUNITS_AREA_UNITDETAIL_MOCK: UnitDetail<UnitType> = {
  unit: 'ac',
  unitName: {
    singular: 'acre',
    plural: 'acres',
    abbr: 'ac',
  },
};
export const COVER_CROP_TOP_LEVEL_INPUT = KPICoverCropMock.metric;
export const COVER_CROP_TOP_LEVEL_EXPECTED_OUTPUT_IMPERIAL_UNITS: CoverCropMetrics = {
  adoption: {
    formattedValue: '4.8%',
    unit: 'unit-interval',
    value: 0.048227530523543095,
  },
  covercroppedArea: {
    formattedValue: '7.4M',
    unit: 'ac',
    value: 7368662.6227280935,
  },
  totalTrackedArea: {
    formattedValue: '153M',
    unit: 'ac',
    value: 152789548.6817629,
  },
  unknownArea: {
    formattedValue: '121',
    unit: 'ac',
    value: 120.84318905705004,
  },
  notApplicableArea: {
    formattedValue: '25',
    unit: 'ac',
    value: 24.710514233241504,
  },
};
export const COVER_CROP_TOP_LEVEL_EXPECTED_OUTPUT_METRIC_UNITS: CoverCropMetrics = {
  adoption: {
    formattedValue: '4.8%',
    unit: 'unit-interval',
    value: 0.048227530523543095,
  },
  covercroppedArea: {
    formattedValue: '3M',
    unit: 'ha',
    value: 2981994.8517362275,
  },
  totalTrackedArea: {
    formattedValue: '62M',
    unit: 'ha',
    value: 61831796.47318901,
  },
  unknownArea: {
    formattedValue: '49',
    unit: 'ha',
    value: 48.9035509,
  },
  notApplicableArea: {
    formattedValue: '10',
    unit: 'ha',
    value: 10,
  },
};
export const COVER_CROP_SUBREGION_INPUT = KPICoverCropSubsectionMock.boundary_summary[872]!;
export const COVER_CROP_SUBREGION_EXPECTED_OUTPUT_IMPERIAL_UNITS: CoverCropMetrics = {
  adoption: {
    formattedValue: '5.3%',
    unit: 'unit-interval',
    value: 0.05319761816474225,
  },
  covercroppedArea: {
    formattedValue: '3.7M',
    unit: 'ac',
    value: 3652480.38676551,
  },
  totalTrackedArea: {
    formattedValue: '69M',
    unit: 'ac',
    value: 68658720.31064469,
  },
  unknownArea: {
    formattedValue: '121',
    unit: 'ac',
    value: 120.84318905705004,
  },
  notApplicableArea: {
    formattedValue: '2.5K',
    unit: 'ac',
    value: 2471.0514233241506,
  },
};
export const COVER_CROP_SUBREGION_EXPECTED_OUTPUT_METRIC_UNITS: CoverCropMetrics = {
  adoption: {
    formattedValue: '5.3%',
    unit: 'unit-interval',
    value: 0.05319761816474225,
  },
  covercroppedArea: {
    formattedValue: '1.5M',
    unit: 'ha',
    value: 1478107.8015171601,
  },
  totalTrackedArea: {
    formattedValue: '28M',
    unit: 'ha',
    value: 27785225.21327477,
  },
  unknownArea: {
    formattedValue: '49',
    unit: 'ha',
    value: 48.9035509,
  },
  notApplicableArea: {
    formattedValue: '1K',
    unit: 'ha',
    value: 1000,
  },
};

export const COVER_CROP_ANNUALIZED_INPUT = KPICoverCropAnnualizedMock.annualized_summary?.['2015'];

export const COVER_CROP_ANNUALIZED_EXPECTED_OUTPUT_IMPERIAL_UNITS: CoverCropMetrics = {
  adoption: {formattedValue: '3%', unit: 'unit-interval', value: 0.03007112081706942},
  covercroppedArea: {formattedValue: '4.4M', unit: 'ac', value: 4392524.832999111},
  totalTrackedArea: {formattedValue: '146M', unit: 'ac', value: 146071204.3199188},
  unknownArea: {formattedValue: '25K', unit: 'ac', value: 24710.514233241505},
  notApplicableArea: {formattedValue: '49K', unit: 'ac', value: 49421.02846648301},
};

export const COVER_CROP_CROP_TYPE_INPUT = KPICoverCropCropTypeSummaryMock.crop_type_summary?.['1'];

export const COVER_CROP_CROP_TYPE_EXPECTED_OUTPUT_IMPERIAL_UNITS: CoverCropMetrics = {
  covercroppedArea: {value: 4308465.917997444, unit: 'ac', formattedValue: '4.3M'},
  totalTrackedArea: {value: 72934601.45616043, unit: 'ac', formattedValue: '73M'},
  adoption: {
    value: 0.05907300282688429,
    unit: 'unit-interval',
    formattedValue: '5.9%',
  },
  unknownArea: {formattedValue: '25K', unit: 'ac', value: 24710.514233241505},
  notApplicableArea: {formattedValue: '49K', unit: 'ac', value: 49421.02846648301},
};

export const COVER_CROP_SUBREGION_ANNUALIZED_INPUT =
  KPICoverCropSubsectionAnnualizedMock.boundary_annualized_summary?.[872]?.[2015];

export const COVER_CROP_SUBREGION_ANNUALIZED_EXPECTED_OUTPUT_IMPERIAL_UNITS: CoverCropMetrics = {
  adoption: {formattedValue: '3.5%', unit: 'unit-interval', value: 0.03509489717548463},
  covercroppedArea: {formattedValue: '2.4M', unit: 'ac', value: 2371246.186037572},
  totalTrackedArea: {formattedValue: '68M', unit: 'ac', value: 67566694.2171295},
  unknownArea: {formattedValue: '2.5K', unit: 'ac', value: 2471.0514233241506},
  notApplicableArea: {formattedValue: '4.9K', unit: 'ac', value: 4942.102846648301},
};

export const COVER_CROP_ZERO_VALUE_EXPECTED_OUTPUT_METRIC_UNITS: CoverCropMetrics = {
  adoption: {
    formattedValue: '0%',
    unit: 'unit-interval',
    value: 0,
  },
  covercroppedArea: {
    formattedValue: '0',
    unit: 'ha',
    value: 0,
  },
  totalTrackedArea: {
    formattedValue: '1',
    unit: 'ha',
    value: 1,
  },
  unknownArea: {
    formattedValue: '0',
    unit: 'ha',
    value: 0,
  },
  notApplicableArea: {
    formattedValue: '0',
    unit: 'ha',
    value: 0,
  },
};

export const COVER_CROP_BY_YEAR_METRICS_PAIRS: Array<[string, CoverCropMetrics | null]> = [
  [
    '2015',
    {
      covercroppedArea: {
        value: 521930.1510443554,
        unit: 'ha',
        formattedValue: '522K',
      },
      totalTrackedArea: {
        value: 17079149.157884974,
        unit: 'ha',
        formattedValue: '17M',
      },
      adoption: {
        value: 0.03055949369722523,
        unit: 'unit-interval',
        formattedValue: '3.1%',
      },
      unknownArea: {
        value: 75775.00608706055,
        unit: 'ha',
        formattedValue: '76K',
      },
      notApplicableArea: {
        value: 1163456.7643154296,
        unit: 'ha',
        formattedValue: '1.2M',
      },
    },
  ],
  [
    '2016',
    {
      covercroppedArea: {
        value: 527638.3912407279,
        unit: 'ha',
        formattedValue: '528K',
      },
      totalTrackedArea: {
        value: 17807831.30132769,
        unit: 'ha',
        formattedValue: '18M',
      },
      adoption: {
        value: 0.029629570401500202,
        unit: 'unit-interval',
        formattedValue: '3%',
      },
      unknownArea: {
        value: 70432.73600009766,
        unit: 'ha',
        formattedValue: '70K',
      },
      notApplicableArea: {
        value: 1182445.5341393554,
        unit: 'ha',
        formattedValue: '1.2M',
      },
    },
  ],
  [
    '2017',
    {
      covercroppedArea: {
        value: 765080.6216872004,
        unit: 'ha',
        formattedValue: '765K',
      },
      totalTrackedArea: {
        value: 18297357.49512607,
        unit: 'ha',
        formattedValue: '18M',
      },
      adoption: {
        value: 0.0418137221121136,
        unit: 'unit-interval',
        formattedValue: '4.2%',
      },
      unknownArea: {
        value: 92993.26736237793,
        unit: 'ha',
        formattedValue: '93K',
      },
      notApplicableArea: {
        value: 1002812.9906592285,
        unit: 'ha',
        formattedValue: '1M',
      },
    },
  ],
  [
    '2018',
    {
      covercroppedArea: {
        value: 713245.4430281384,
        unit: 'ha',
        formattedValue: '713K',
      },
      totalTrackedArea: {
        value: 18336776.996107526,
        unit: 'ha',
        formattedValue: '18M',
      },
      adoption: {
        value: 0.038896990631425786,
        unit: 'unit-interval',
        formattedValue: '3.9%',
      },
      unknownArea: {
        value: 50725.491661914064,
        unit: 'ha',
        formattedValue: '51K',
      },
      notApplicableArea: {
        value: 750209.8631179443,
        unit: 'ha',
        formattedValue: '750K',
      },
    },
  ],
  [
    '2019',
    {
      covercroppedArea: {
        value: 786889.0679040949,
        unit: 'ha',
        formattedValue: '787K',
      },
      totalTrackedArea: {
        value: 17674105.712217335,
        unit: 'ha',
        formattedValue: '18M',
      },
      adoption: {
        value: 0.04452214333878025,
        unit: 'unit-interval',
        formattedValue: '4.5%',
      },
      unknownArea: {
        value: 43543.15094816894,
        unit: 'ha',
        formattedValue: '44K',
      },
      notApplicableArea: {
        value: 418650.2107676758,
        unit: 'ha',
        formattedValue: '419K',
      },
    },
  ],
  [
    '2020',
    {
      covercroppedArea: {
        value: 729150.6055060463,
        unit: 'ha',
        formattedValue: '729K',
      },
      totalTrackedArea: {
        value: 18345475.67869173,
        unit: 'ha',
        formattedValue: '18M',
      },
      adoption: {
        value: 0.039745527359258105,
        unit: 'unit-interval',
        formattedValue: '4%',
      },
      unknownArea: {
        value: 64463.94266081543,
        unit: 'ha',
        formattedValue: '64K',
      },
      notApplicableArea: {
        value: 1132789.5508025635,
        unit: 'ha',
        formattedValue: '1.1M',
      },
    },
  ],
  [
    '2021',
    {
      covercroppedArea: {
        value: 885837.8960584194,
        unit: 'ha',
        formattedValue: '886K',
      },
      totalTrackedArea: {
        value: 18533311.336629257,
        unit: 'ha',
        formattedValue: '19M',
      },
      adoption: {
        value: 0.04779706550915423,
        unit: 'unit-interval',
        formattedValue: '4.8%',
      },
      unknownArea: {
        value: 37054.11667624512,
        unit: 'ha',
        formattedValue: '37K',
      },
      notApplicableArea: {
        value: 614032.1741799804,
        unit: 'ha',
        formattedValue: '614K',
      },
    },
  ],
  [
    '2022',
    {
      covercroppedArea: {
        value: 858807.5605105195,
        unit: 'ha',
        formattedValue: '859K',
      },
      totalTrackedArea: {
        value: 16901981.120291766,
        unit: 'ha',
        formattedValue: '17M',
      },
      adoption: {
        value: 0.05081105903493605,
        unit: 'unit-interval',
        formattedValue: '5.1%',
      },
      unknownArea: {
        value: 36910.119520288084,
        unit: 'ha',
        formattedValue: '37K',
      },
      notApplicableArea: {
        value: 676199.6444428955,
        unit: 'ha',
        formattedValue: '676K',
      },
    },
  ],
  [
    '2023',
    {
      covercroppedArea: {
        value: 79116.07603935547,
        unit: 'ha',
        formattedValue: '79K',
      },
      totalTrackedArea: {
        value: 4962773.662281591,
        unit: 'ha',
        formattedValue: '5M',
      },
      adoption: {
        value: 0.015941906970422373,
        unit: 'unit-interval',
        formattedValue: '1.6%',
      },
      unknownArea: {
        value: 129099.91246381836,
        unit: 'ha',
        formattedValue: '129K',
      },
      notApplicableArea: {
        value: 635319.3247447754,
        unit: 'ha',
        formattedValue: '635K',
      },
    },
  ],
];

export const COVER_CROP_BY_CROP_METRICS_PAIRS: Array<[string, CoverCropMetrics | null]> = [
  [
    '1',
    {
      covercroppedArea: {
        value: 434033.73225933645,
        unit: 'ha',
        formattedValue: '434K',
      },
      totalTrackedArea: {
        value: 7416735.6126174545,
        unit: 'ha',
        formattedValue: '7.4M',
      },
      adoption: {
        value: 0.05852085808761367,
        unit: 'unit-interval',
        formattedValue: '5.9%',
      },
      unknownArea: {
        value: 47.2033125,
        unit: 'ha',
        formattedValue: '47',
      },
      notApplicableArea: {
        value: 0,
        unit: 'ha',
        formattedValue: '0',
      },
    },
  ],
  [
    '5',
    {
      covercroppedArea: {
        value: 380653.32346000144,
        unit: 'ha',
        formattedValue: '381K',
      },
      totalTrackedArea: {
        value: 3781639.1847600066,
        unit: 'ha',
        formattedValue: '3.8M',
      },
      adoption: {
        value: 0.10065828728294149,
        unit: 'unit-interval',
        formattedValue: '10.1%',
      },
      unknownArea: {
        value: 0,
        unit: 'ha',
        formattedValue: '0',
      },
      notApplicableArea: {
        value: 0,
        unit: 'ha',
        formattedValue: '0',
      },
    },
  ],
  [
    '23',
    {
      covercroppedArea: {
        value: 18392.42181118164,
        unit: 'ha',
        formattedValue: '18K',
      },
      totalTrackedArea: {
        value: 916491.8577843262,
        unit: 'ha',
        formattedValue: '916K',
      },
      adoption: {
        value: 0.020068287192039458,
        unit: 'unit-interval',
        formattedValue: '2%',
      },
      unknownArea: {
        value: 36862.91620778808,
        unit: 'ha',
        formattedValue: '37K',
      },
      notApplicableArea: {
        value: 0,
        unit: 'ha',
        formattedValue: '0',
      },
    },
  ],
  [
    '24',
    {
      covercroppedArea: {
        value: 25728.082979999974,
        unit: 'ha',
        formattedValue: '26K',
      },
      totalTrackedArea: {
        value: 4787114.46512998,
        unit: 'ha',
        formattedValue: '4.8M',
      },
      adoption: {
        value: 0.0053744449119416254,
        unit: 'unit-interval',
        formattedValue: '0.5%',
      },
      unknownArea: {
        value: 0,
        unit: 'ha',
        formattedValue: '0',
      },
      notApplicableArea: {
        value: 676199.6444428955,
        unit: 'ha',
        formattedValue: '676K',
      },
    },
  ],
];

export const COVER_CROP_BY_SUBREGION_METRICS_PAIRS: Array<[string, CoverCropMetrics | null]> = [
  [
    '873',
    {
      covercroppedArea: {
        value: 18392.42181118164,
        unit: 'ha',
        formattedValue: '18K',
      },
      totalTrackedArea: {
        value: 916491.8577843262,
        unit: 'ha',
        formattedValue: '916K',
      },
      adoption: {
        value: 0.020068287192039458,
        unit: 'unit-interval',
        formattedValue: '2%',
      },
      unknownArea: {
        value: 36862.91620778808,
        unit: 'ha',
        formattedValue: '37K',
      },
      notApplicableArea: {
        value: 676199.6444428955,
        unit: 'ha',
        formattedValue: '676K',
      },
    },
  ],
  [
    '875',
    {
      covercroppedArea: {
        value: 669669.5204800019,
        unit: 'ha',
        formattedValue: '670K',
      },
      totalTrackedArea: {
        value: 12559602.880769964,
        unit: 'ha',
        formattedValue: '13M',
      },
      adoption: {
        value: 0.05331932281914219,
        unit: 'unit-interval',
        formattedValue: '5.3%',
      },
      unknownArea: {
        value: 0,
        unit: 'ha',
        formattedValue: '0',
      },
      notApplicableArea: {
        value: 0,
        unit: 'ha',
        formattedValue: '0',
      },
    },
  ],
  [
    '16648',
    {
      covercroppedArea: {
        value: 170745.61821933594,
        unit: 'ha',
        formattedValue: '171K',
      },
      totalTrackedArea: {
        value: 3425886.3817374757,
        unit: 'ha',
        formattedValue: '3.4M',
      },
      adoption: {
        value: 0.04983983681698762,
        unit: 'unit-interval',
        formattedValue: '5%',
      },
      unknownArea: {
        value: 47.2033125,
        unit: 'ha',
        formattedValue: '47',
      },
      notApplicableArea: {
        value: 0,
        unit: 'ha',
        formattedValue: '0',
      },
    },
  ],
];
