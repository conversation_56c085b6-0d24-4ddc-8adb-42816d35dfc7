import sum from 'lodash/sum';

import {type Theme} from '@regrow-internal/design-system';

import type {MeasurementEnum} from '_common/utils/measurement-unit-options';
import {getTypedValues} from '_common/utils/object';
import {isNil} from '_common/utils/typeGuards';

import type {CropRotationFallowMetricData} from 'containers/si/api/apiTypes';
import {convertValue, getUnit} from 'containers/si/utils/convert';
import {formatAllNumbers} from 'containers/si/utils/formatters/number.format';
import type {Metric, UnitDetail, UnitType} from 'containers/si/utils/value.types';

export type CropRotationFallowMetric = {
  noFallowSeasonsArea: Metric;
  oneFallowSeasonArea: Metric;
  twoFallowSeasonsArea: Metric;
  threePlusFallowSeasonsArea: Metric;
  totalFallowDataArea: Metric;
};

export const private__cropRotationFallowUnit = (unitsSystem: MeasurementEnum) =>
  getUnit(unitsSystem);

export const CROP_ROTATION_FALLOW_METRICS_FORMATTER_MAP: Record<
  keyof CropRotationFallowMetric,
  (value: number) => string
> = {
  noFallowSeasonsArea: formatAllNumbers,
  oneFallowSeasonArea: formatAllNumbers,
  twoFallowSeasonsArea: formatAllNumbers,
  threePlusFallowSeasonsArea: formatAllNumbers,
  totalFallowDataArea: formatAllNumbers,
};

export const CROP_ROTATION_FALLOW_METRICS_UNIT_MAP: Record<
  keyof CropRotationFallowMetric,
  (unitsSystem: MeasurementEnum) => UnitDetail<UnitType>
> = {
  noFallowSeasonsArea: private__cropRotationFallowUnit,
  oneFallowSeasonArea: private__cropRotationFallowUnit,
  twoFallowSeasonsArea: private__cropRotationFallowUnit,
  threePlusFallowSeasonsArea: private__cropRotationFallowUnit,
  totalFallowDataArea: private__cropRotationFallowUnit,
};

export const CROP_ROTATION_FALLOW_METRICS_LABEL_MAP: Record<
  keyof CropRotationFallowMetric,
  {label: string; categoryPaletteColorKey: keyof Theme['palette']['categoryPalette']}
> = {
  noFallowSeasonsArea: {label: 'no fallow', categoryPaletteColorKey: '1'},
  oneFallowSeasonArea: {label: '1 season', categoryPaletteColorKey: '7'},
  twoFallowSeasonsArea: {label: '2 seasons', categoryPaletteColorKey: '6'},
  threePlusFallowSeasonsArea: {label: '3+ seasons', categoryPaletteColorKey: '5'},
  totalFallowDataArea: {label: 'total', categoryPaletteColorKey: '0'},
} as const;

/**
 * This should only be used as a transformer alone with one of the standard kpi.transformation.helpers
 * Example usage:
 * const metricData = makeTopLevelMetric(response, makeCropRotationFallowMetrics);
 */
export const makeCropRotationFallowMetric =
  (unitsSystem: MeasurementEnum) =>
  (data: CropRotationFallowMetricData): CropRotationFallowMetric | null => {
    const allFallowValues = getTypedValues(data);

    // These can be removed once a response deserializer and type validator is built
    // https://regrow.atlassian.net/browse/SI-1379
    // This method should handle
    // expected nulls or undefineds and
    // 0 and Finite checks as those are valid numbers
    if (
      isNil(data.nofallow_m2) ||
      isNil(data.lowfallow_m2) ||
      isNil(data.midfallow_m2) ||
      isNil(data.highfallow_m2) ||
      allFallowValues.some(r => !Number.isFinite(r))
    ) {
      return null;
    }

    const makeRotationValue = (key: keyof CropRotationFallowMetric, rtn_num_m2: number): Metric => {
      const value = convertValue({value: rtn_num_m2, from: 'm2', to: unitsSystem});

      return {
        value,
        unit: CROP_ROTATION_FALLOW_METRICS_UNIT_MAP[key](unitsSystem).unit,
        formattedValue: CROP_ROTATION_FALLOW_METRICS_FORMATTER_MAP[key](value),
      };
    };

    const totalFallowDataM2 = sum(allFallowValues);

    return {
      totalFallowDataArea: makeRotationValue('totalFallowDataArea', totalFallowDataM2),
      noFallowSeasonsArea: makeRotationValue('noFallowSeasonsArea', data.nofallow_m2),
      oneFallowSeasonArea: makeRotationValue('oneFallowSeasonArea', data.lowfallow_m2),
      twoFallowSeasonsArea: makeRotationValue('twoFallowSeasonsArea', data.midfallow_m2),
      threePlusFallowSeasonsArea: makeRotationValue(
        'threePlusFallowSeasonsArea',
        data.highfallow_m2
      ),
    };
  };
