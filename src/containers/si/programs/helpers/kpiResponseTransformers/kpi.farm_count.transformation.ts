import {isNil} from '_common/utils/typeGuards';

import type {FarmCountKPIMetricData} from 'containers/si/api/apiTypes';
import {formatAllNumbers} from 'containers/si/utils/formatters/number.format';
import type {Metric, UnitDetail, UnitType} from 'containers/si/utils/value.types';

export type FarmCountMetrics = {
  farm_count: Metric;
};

export const private__farmCountUnit = () => ({
  unit: 'count' as const,
  unitName: {
    singular: 'farm',
    plural: 'farms',
    abbr: '',
  },
});

export const FARMCOUNTMETRICS_FORMATTER_MAP: Record<
  keyof FarmCountMetrics,
  (value: number) => string
> = {
  farm_count: formatAllNumbers,
};

export const FARMCOUNTMETRICS_UNIT_MAP: Record<keyof FarmCountMetrics, () => UnitDetail<UnitType>> =
  {
    farm_count: private__farmCountUnit,
  };

/**
 * This should only be used as a transformer alone with one of the standard kpi.transformation.helpers
 * Example usage:
 * const farmCountMetric = makeTopLevelMetric(farmCountResponse, makeFarmCountMetrics);
 */
export const makeFarmCountMetrics = (data: FarmCountKPIMetricData): FarmCountMetrics | null => {
  // These can be removed once a response deserializer and type validator is built
  // https://regrow.atlassian.net/browse/SI-1379
  // This method should handle
  // expected nulls or undefineds and
  // 0 and Finite checks as those are valid numbers

  if (isNil(data.farm_count) || !Number.isFinite(data.farm_count)) {
    return null;
  }

  const unitDetails = FARMCOUNTMETRICS_UNIT_MAP.farm_count();

  return {
    farm_count: {
      value: data.farm_count,
      unit: unitDetails.unit,
      formattedValue: FARMCOUNTMETRICS_FORMATTER_MAP.farm_count(data.farm_count),
    },
  };
};
