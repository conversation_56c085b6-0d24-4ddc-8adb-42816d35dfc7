import {type MeasurementEnum} from '_common/utils/measurement-unit-options';
import {isNil} from '_common/utils/typeGuards';

import type {NetImpactKgPerM2MetricData} from 'containers/si/api/apiTypes';
import {CO2E} from 'containers/si/constants';
import {convertValue, getRatioUnits} from 'containers/si/utils/convert';
import {formatSmallNumbers} from 'containers/si/utils/formatters/number.format';
import {type Metric, type UnitDetail, type UnitType} from 'containers/si/utils/value.types';

export type NetImpactMetrics = {
  netImpact: Metric;
  netImpactStdErr?: Metric;
};

export const private__netUnit = (unitsSystem: MeasurementEnum) =>
  getRatioUnits({unit1: 'mt', unit2: unitsSystem, subUnit1: CO2E});

export const NET_IMPACT_METRICS_FORMATTER_MAP: Record<
  keyof NetImpactMetrics,
  (value: number) => string
> = {
  netImpact: formatSmallNumbers,
  netImpactStdErr: formatSmallNumbers, // note this should always be the same as the netImpact formatter
};

export const NET_IMPACT_METRICS_UNIT_MAP: Record<
  keyof NetImpactMetrics,
  (unitsSystem: MeasurementEnum) => UnitDetail<UnitType>
> = {
  netImpact: private__netUnit,
  netImpactStdErr: private__netUnit, // note this should always be the same as the netImpact unit
};

/**
 * This should only be used as a transformer alone with one of the standard kpi.transformation.helpers
 * Example usage:
 * const metricData = makeTopLevelMetric(response, makeNetImpactMetrics(MeasurementEnum.Metric));
 */
export const makeNetImpactMetrics =
  (unitsSystem: MeasurementEnum) =>
  (data: NetImpactKgPerM2MetricData): NetImpactMetrics | null => {
    // These can be removed once a response deserializer and type validator is built
    // https://regrow.atlassian.net/browse/SI-1379
    // This method should handle
    // expected nulls or undefineds and
    // 0 and Finite checks as those are valid numbers
    if (isNil(data.net_impact_kg_per_m2) || !Number.isFinite(data.net_impact_kg_per_m2)) {
      return null;
    }

    const massPerAreavalue =
      convertValue({
        value: data.net_impact_kg_per_m2,
        from: 'kg',
        to: 'mt',
      }) / convertValue({value: 1, from: 'm2', to: unitsSystem});

    const stdErrValue =
      isNil(data.net_impact_kg_per_m2_stderr) ||
      !Number.isFinite(data.net_impact_kg_per_m2_stderr) ||
      data.net_impact_kg_per_m2_stderr === 0
        ? undefined
        : convertValue({
            value: data.net_impact_kg_per_m2_stderr,
            from: 'kg',
            to: 'mt',
          }) / convertValue({value: 1, from: 'm2', to: unitsSystem});

    return {
      netImpact: {
        value: massPerAreavalue,
        unit: NET_IMPACT_METRICS_UNIT_MAP.netImpact(unitsSystem).unit,
        formattedValue: NET_IMPACT_METRICS_FORMATTER_MAP.netImpact(massPerAreavalue),
      },
      ...(isNil(stdErrValue)
        ? {}
        : {
            netImpactStdErr: {
              value: stdErrValue,
              unit: NET_IMPACT_METRICS_UNIT_MAP.netImpactStdErr(unitsSystem).unit,
              formattedValue: NET_IMPACT_METRICS_FORMATTER_MAP.netImpactStdErr(stdErrValue),
            },
          }),
    };
  };
