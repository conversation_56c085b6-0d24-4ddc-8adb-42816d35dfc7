import {isNil} from '_common/utils/typeGuards';

import type {VolumeWeightedGhgKgKPIMetricData} from 'containers/si/api/apiTypes';
import {CO2E} from 'containers/si/constants';
import {convertValue, getUnit} from 'containers/si/utils/convert';
import {formatAllNumbers} from 'containers/si/utils/formatters/number.format';
import type {Metric, UnitDetail, UnitType} from 'containers/si/utils/value.types';

export type VolumeWeightedGhgMetrics = {
  volumeWeightedGhgMass: Metric;
};

const private__volumeWeightedGhgUnit = () => getUnit('mt', CO2E);

export const VOLUMEWEIGHTEDGHGMETRICS_FORMATTER_MAP: Record<
  keyof VolumeWeightedGhgMetrics,
  (value: number) => string
> = {
  volumeWeightedGhgMass: formatAllNumbers,
};

export const VOLUMEWEIGHTEDGHGMETRICS_UNIT_MAP: Record<
  keyof VolumeWeightedGhgMetrics,
  () => UnitDetail<UnitType>
> = {
  volumeWeightedGhgMass: private__volumeWeightedGhgUnit,
};

/**
 * This should only be used as a transformer alone with one of the standard kpi.transformation.helpers
 * Example usage:
 * const metricData = makeTopLevelMetric(response, makeVolumeWeightedGhgMetrics);
 */
export const makeVolumeWeightedGhgMetrics = (
  data: VolumeWeightedGhgKgKPIMetricData
): VolumeWeightedGhgMetrics | null => {
  // These can be removed once a response deserializer and type validator is built
  // https://regrow.atlassian.net/browse/SI-1379
  // This method should handle
  // expected nulls or undefineds and
  // 0 and Finite checks as those are valid numbers
  if (isNil(data.volume_weighted_ghg_kg) || !Number.isFinite(data.volume_weighted_ghg_kg))
    return null;

  const value = convertValue({
    value: data.volume_weighted_ghg_kg,
    from: 'kg',
    to: 'mt',
  });

  const unitDetails = VOLUMEWEIGHTEDGHGMETRICS_UNIT_MAP.volumeWeightedGhgMass();

  return {
    volumeWeightedGhgMass: {
      value,
      unit: unitDetails.unit,
      formattedValue: VOLUMEWEIGHTEDGHGMETRICS_FORMATTER_MAP.volumeWeightedGhgMass(value),
    },
  };
};
