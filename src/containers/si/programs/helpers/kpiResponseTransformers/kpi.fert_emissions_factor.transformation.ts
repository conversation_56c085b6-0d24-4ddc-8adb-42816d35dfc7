import {isNil} from '_common/utils/typeGuards';

import type {FertEmissionFactorMetricData} from 'containers/si/api/apiTypes';
import {CO2E} from 'containers/si/constants';
import {getRatioUnits} from 'containers/si/utils/convert';
import {formatAllNumbers} from 'containers/si/utils/formatters/number.format';
import {type Metric, type UnitDetail, type UnitType} from 'containers/si/utils/value.types';

export type FertEFMetrics = {
  fertEmissionsPerYield: Metric;
};

export const private__fertEFUnit = () =>
  getRatioUnits({
    unit1: 'kg',
    unit2: 'kg',
    subUnit1: CO2E,
    subUnit2: `yield`,
  });

export const FERT_EF_METRICS_FORMATTER_MAP: Record<keyof FertEFMetrics, (value: number) => string> =
  {
    fertEmissionsPerYield: formatAllNumbers,
  };

export const FERT_EF_METRICS_UNIT_MAP: Record<keyof FertEFMetrics, () => UnitDetail<UnitType>> = {
  fertEmissionsPerYield: private__fertEFUnit,
};

/**
 * This should only be used as a transformer alone with one of the standard kpi.transformation.helpers
 * Example usage:
 * const metricData = makeTopLevelMetric(response, makeFertEFMetrics);
 */
export const makeFertEFMetrics = (data: FertEmissionFactorMetricData): FertEFMetrics | null => {
  // These can be removed once a response deserializer and type validator is built
  // https://regrow.atlassian.net/browse/SI-1379
  // This method should handle
  // expected nulls or undefineds and
  // 0 and Finite checks as those are valid numbers
  if (isNil(data.fert_emissions_factor) || !Number.isFinite(data.fert_emissions_factor)) {
    return null;
  }

  const value = data.fert_emissions_factor;

  const unitDetails = FERT_EF_METRICS_UNIT_MAP.fertEmissionsPerYield();

  return {
    fertEmissionsPerYield: {
      value,
      unit: unitDetails.unit,
      formattedValue: FERT_EF_METRICS_FORMATTER_MAP.fertEmissionsPerYield(value),
    },
  };
};
