import {isNil} from '_common/utils/typeGuards';

import type {VolumeWeightedNetKgKPIMetricData} from 'containers/si/api/apiTypes';
import {CO2E} from 'containers/si/constants';
import {convertValue, getUnit} from 'containers/si/utils/convert';
import {formatAllNumbers} from 'containers/si/utils/formatters/number.format';
import type {Metric, UnitDetail, UnitType} from 'containers/si/utils/value.types';

export type VolumeWeightedNetMetrics = {
  volumeWeightedNetMass: Metric;
};

const private__volumeWeightedNetUnit = () => getUnit('mt', CO2E);

export const VOLUMEWEIGHTEDNETMETRICS_FORMATTER_MAP: Record<
  keyof VolumeWeightedNetMetrics,
  (value: number) => string
> = {
  volumeWeightedNetMass: formatAllNumbers,
};

export const VOLUMEWEIGHTEDNETMETRICS_UNIT_MAP: Record<
  keyof VolumeWeightedNetMetrics,
  () => UnitDetail<UnitType>
> = {
  volumeWeightedNetMass: private__volumeWeightedNetUnit,
};

/**
 * This should only be used as a transformer alone with one of the standard kpi.transformation.helpers
 * Example usage:
 * const metricData = makeTopLevelMetric(response, makeVolumeWeightedNetMetrics);
 */
export const makeVolumeWeightedNetMetrics = (
  data: VolumeWeightedNetKgKPIMetricData
): VolumeWeightedNetMetrics | null => {
  // These can be removed once a response deserializer and type validator is built
  // https://regrow.atlassian.net/browse/SI-1379
  // This method should handle
  // expected nulls or undefineds and
  // 0 and Finite checks as those are valid numbers
  if (isNil(data.volume_weighted_net_kg) || !Number.isFinite(data.volume_weighted_net_kg))
    return null;

  const value = convertValue({
    value: data.volume_weighted_net_kg,
    from: 'kg',
    to: 'mt',
  });

  const unitDetails = VOLUMEWEIGHTEDNETMETRICS_UNIT_MAP.volumeWeightedNetMass();

  return {
    volumeWeightedNetMass: {
      value,
      unit: unitDetails.unit,
      formattedValue: VOLUMEWEIGHTEDNETMETRICS_FORMATTER_MAP.volumeWeightedNetMass(value),
    },
  };
};
