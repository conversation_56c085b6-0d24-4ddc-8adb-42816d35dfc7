import {getTypedValues} from '_common/utils/object';
import {isDefined, isNil} from '_common/utils/typeGuards';

import type {LivingRootConfidenceMetricData} from 'containers/si/api/apiTypes';
import type {LivingRootMonthKeys} from 'containers/si/programs/helpers/types';
import {formatFloatToPercent} from 'containers/si/utils/formatters/number.format';
import type {Metric, UnitDetail, UnitType} from 'containers/si/utils/value.types';

export type LivingRootConfidenceMetrics = Record<LivingRootMonthKeys, Metric | null>;

export const private__livingRootConfidenceUnit: UnitDetail<UnitType> = {
  unit: 'unit-interval' as const,
  unitName: {
    singular: '% confidence',
    plural: '% confidence',
    abbr: 'confidence',
  },
};

/**
 * This should only be used as a transformer alone with one of the standard kpi.transformation.helpers
 * Example usage:
 * const metrics = makeTopLevelMetric(response, makeLivingRootConfidenceMetrics);
 */
export const makeLivingRootConfidenceMetrics = (
  data: LivingRootConfidenceMetricData
): LivingRootConfidenceMetrics | null => {
  const livingRootConfidenceMonths = getTypedValues(data);

  // These can be removed once a response deserializer and type validator is built
  // https://regrow.atlassian.net/browse/SI-1379
  // This method should handle
  // expected null, undefined, Finite checks as those are valid numbers
  if (
    // Check if ALL values are nil (individual months could be if we do not have data for them)
    livingRootConfidenceMonths.every(isNil) ||
    // or any value is invalid
    livingRootConfidenceMonths.some(monthVal => !Number.isFinite(monthVal ?? 0))
  ) {
    return null;
  }

  const makeMetric = (value?: number | null): Metric | null =>
    isDefined(value)
      ? {
          value,
          unit: private__livingRootConfidenceUnit.unit,
          formattedValue: formatFloatToPercent(value, {sigDigits: 1}),
        }
      : null;

  return {
    Jan: makeMetric(data.living_root_confidence_1),
    Feb: makeMetric(data.living_root_confidence_2),
    Mar: makeMetric(data.living_root_confidence_3),
    Apr: makeMetric(data.living_root_confidence_4),
    May: makeMetric(data.living_root_confidence_5),
    Jun: makeMetric(data.living_root_confidence_6),
    Jul: makeMetric(data.living_root_confidence_7),
    Aug: makeMetric(data.living_root_confidence_8),
    Sep: makeMetric(data.living_root_confidence_9),
    Oct: makeMetric(data.living_root_confidence_10),
    Nov: makeMetric(data.living_root_confidence_11),
    Dec: makeMetric(data.living_root_confidence_12),
  };
};
