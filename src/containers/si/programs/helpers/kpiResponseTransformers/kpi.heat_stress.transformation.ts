import type {HeatStressKPIMetricData} from 'containers/si/api/apiTypes';
import {
  isValidTier,
  tierFormatter,
  tierUnit,
} from 'containers/si/programs/helpers/kpi.quantification_level.helpers';
import {formatAllNumbers} from 'containers/si/utils/formatters/number.format';
import {isFiniteNumber} from 'containers/si/utils/utils';
import type {Metric, UnitDetail, UnitType} from 'containers/si/utils/value.types';

export const HEAT_STRESS_CONTENT = {
  name: 'heat stress',
  explainer: (panelLabel: string) => [
    `The average number of days in a year under heat stress (over 40°C) broken down ${panelLabel}.`,
    `Note: If field level data is available for the entire specified geography, heat stress is determined for the areas which grow the specified commodities. Otherwise, heat stress is determined over the entire geography.`,
  ],
};
export const HEATWAVE_OCCURRENCES_CONTENT = {
  name: 'heatwave occurrences',
  explainer: (panelLabel: string) => [
    `The average annual occurrences of heatwaves broken down ${panelLabel} A heatwave is defined as two or more consecutive days with a heat index (apparent temperature) over 40°C.`,
    `Note: If field level data is available for the entire specified geography, heatwave occurrences are determined for the areas which grow the specified commodities. Otherwise, heatwave occurrences are determined over the entire geography.`,
  ],
};

export type HeatStressMetrics = {
  daysOfHeatStress: Metric;
  heatStressOccurrences: Metric;
  tier: Metric;
};

export const private__heatStressDaysUnit = (): UnitDetail<UnitType> => {
  return {
    unit: 'd' as const,
    unitName: {
      singular: 'day under stress',
      plural: 'days under stress',
      abbr: 'days under stress',
    },
  };
};

export const private__heatStressOccurrencesUnit = (): UnitDetail<UnitType> => {
  return {
    unit: 'count' as const,
    unitName: {
      singular: 'occurrence',
      plural: 'occurrences',
      abbr: 'occurrences',
    },
  };
};

export const HEAT_STRESS_METRICS_FORMATTER_MAP: Record<
  keyof HeatStressMetrics,
  (value: number) => string
> = {
  daysOfHeatStress: formatAllNumbers,
  heatStressOccurrences: formatAllNumbers,
  tier: tierFormatter,
};

export const HEAT_STRESS_METRICS_UNIT_MAP: Record<
  keyof HeatStressMetrics,
  () => UnitDetail<UnitType>
> = {
  daysOfHeatStress: private__heatStressDaysUnit,
  heatStressOccurrences: private__heatStressOccurrencesUnit,
  tier: tierUnit,
};

/**
 * This should only be used as a transformer alone with one of the standard kpi.transformation.helpers
 * Example usage:
 * const metricData = makeTopLevelMetric(response, makeHeatStressMetrics);
 */
export const makeHeatStressMetrics = (data: HeatStressKPIMetricData): HeatStressMetrics | null => {
  const {avg_heat_stress_days, avg_heat_wave_events, quantification_level} = data;

  // These can be removed once a response deserializer and type validator is built
  // https://regrow.atlassian.net/browse/SI-1379
  // This method should handle
  // expected nulls or undefineds and
  // 0 and Finite checks as those are valid numbers
  if (
    !isFiniteNumber(avg_heat_stress_days) ||
    !isFiniteNumber(avg_heat_wave_events) ||
    !isFiniteNumber(quantification_level) ||
    !isValidTier(quantification_level)
  ) {
    return null;
  }

  return {
    daysOfHeatStress: {
      value: avg_heat_stress_days,
      unit: HEAT_STRESS_METRICS_UNIT_MAP.daysOfHeatStress().unit,
      formattedValue: HEAT_STRESS_METRICS_FORMATTER_MAP.daysOfHeatStress(avg_heat_stress_days),
    },
    heatStressOccurrences: {
      value: avg_heat_wave_events,
      unit: HEAT_STRESS_METRICS_UNIT_MAP.heatStressOccurrences().unit,
      formattedValue: HEAT_STRESS_METRICS_FORMATTER_MAP.heatStressOccurrences(avg_heat_wave_events),
    },
    tier: {
      value: quantification_level,
      unit: HEAT_STRESS_METRICS_UNIT_MAP.tier().unit,
      formattedValue: HEAT_STRESS_METRICS_FORMATTER_MAP.tier(quantification_level),
    },
  };
};
