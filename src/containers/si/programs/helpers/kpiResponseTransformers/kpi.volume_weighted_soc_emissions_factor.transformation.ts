import {isNil} from '_common/utils/typeGuards';

import type {VolumeWeightedSocEmissionsFactorKPIMetricData} from 'containers/si/api/apiTypes';
import {CO2E} from 'containers/si/constants';
import {getRatioUnits} from 'containers/si/utils/convert';
import {formatAllNumbers} from 'containers/si/utils/formatters/number.format';
import {type Metric, type UnitDetail, type UnitType} from 'containers/si/utils/value.types';

export type VolumeWeightedSocEFMetrics = {
  volumeWeighteSocEmissionsPerYeild: Metric;
};

export const private__socEFUnit = () =>
  getRatioUnits({
    unit1: 'kg',
    unit2: 'kg',
    subUnit1: CO2E,
    subUnit2: `yield`,
  });

export const VOLUMEWEIGHTEDSOCEFMETRICS_FORMATTER_MAP: Record<
  keyof VolumeWeightedSocEFMetrics,
  (value: number) => string
> = {
  volumeWeighteSocEmissionsPerYeild: formatAllNumbers,
};

export const VOLUMEWEIGHTEDSOCEFMETRICS_UNIT_MAP: Record<
  keyof VolumeWeightedSocEFMetrics,
  () => UnitDetail<UnitType>
> = {
  volumeWeighteSocEmissionsPerYeild: private__socEFUnit,
};

/**
 * This should only be used as a transformer alone with one of the standard kpi.transformation.helpers
 * Example usage:
 * const metricData = makeTopLevelMetric(response, makeVolumeWeightedSocEFMetrics(MeasurementEnum.Metric));
 */
export const makeVolumeWeightedSocEFMetrics = (
  data: VolumeWeightedSocEmissionsFactorKPIMetricData
): VolumeWeightedSocEFMetrics | null => {
  // These can be removed once a response deserializer and type validator is built
  // https://regrow.atlassian.net/browse/SI-1379
  // This method should handle
  // expected nulls or undefineds and
  // 0 and Finite checks as those are valid numbers
  if (
    isNil(data.volume_weighted_soc_emissions_factor) ||
    !Number.isFinite(data.volume_weighted_soc_emissions_factor)
  ) {
    return null;
  }

  const value = data.volume_weighted_soc_emissions_factor;
  const unitDetails = VOLUMEWEIGHTEDSOCEFMETRICS_UNIT_MAP.volumeWeighteSocEmissionsPerYeild();

  return {
    volumeWeighteSocEmissionsPerYeild: {
      value,
      unit: unitDetails.unit,
      formattedValue:
        VOLUMEWEIGHTEDSOCEFMETRICS_FORMATTER_MAP.volumeWeighteSocEmissionsPerYeild(value),
    },
  };
};
