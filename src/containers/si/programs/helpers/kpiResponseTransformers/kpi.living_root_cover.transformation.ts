import meanBy from 'lodash/meanBy';
import merge from 'lodash/merge';

import {getTypedEntries, getTypedObjectFromEntries, getTypedValues} from '_common/utils/object';
import {isDefined, isNil} from '_common/utils/typeGuards';

import type {LivingRootCoverMetricData} from 'containers/si/api/apiTypes';
import {DAYS_IN_YEAR} from 'containers/si/constants';
import type {LivingRootKeys} from 'containers/si/programs/helpers/types';
import {getUnit} from 'containers/si/utils/convert';
import {formatFloatToPercent} from 'containers/si/utils/formatters/number.format';
import type {Metric, UnitDetail, UnitType} from 'containers/si/utils/value.types';

export type LivingRootCoverMetrics = Record<LivingRootKeys, Metric | null>;

export const private__livingRootCoverPercentOfYearUnit = (): UnitDetail<UnitType> => ({
  unit: 'unit-interval' as const,
  unitName: {
    singular: '% of year with cover',
    plural: '% of year with cover',
    abbr: 'yearly coverage',
  },
});

export const private__livingRootCoverPercentOfMonthUnit = (): UnitDetail<UnitType> => ({
  unit: 'unit-interval' as const,
  unitName: {
    singular: '% of month with cover',
    plural: '% of month with cover',
    abbr: 'monthly coverage',
  },
});

const livingRootFormatFn = (value: number) => formatFloatToPercent(value, {sigDigits: 1});

export const LIVING_ROOT_COVER_METRICS_FORMATTER_MAP: Record<
  keyof LivingRootCoverMetrics,
  (value: number) => string
> = {
  yearAvg: livingRootFormatFn,
  Jan: livingRootFormatFn,
  Feb: livingRootFormatFn,
  Mar: livingRootFormatFn,
  Apr: livingRootFormatFn,
  May: livingRootFormatFn,
  Jun: livingRootFormatFn,
  Jul: livingRootFormatFn,
  Aug: livingRootFormatFn,
  Sep: livingRootFormatFn,
  Oct: livingRootFormatFn,
  Nov: livingRootFormatFn,
  Dec: livingRootFormatFn,
};

export const LIVING_ROOT_COVER_METRICS_UNIT_MAP: Record<
  keyof LivingRootCoverMetrics,
  () => UnitDetail<UnitType>
> = {
  yearAvg: private__livingRootCoverPercentOfYearUnit,
  Jan: private__livingRootCoverPercentOfMonthUnit,
  Feb: private__livingRootCoverPercentOfMonthUnit,
  Mar: private__livingRootCoverPercentOfMonthUnit,
  Apr: private__livingRootCoverPercentOfMonthUnit,
  May: private__livingRootCoverPercentOfMonthUnit,
  Jun: private__livingRootCoverPercentOfMonthUnit,
  Jul: private__livingRootCoverPercentOfMonthUnit,
  Aug: private__livingRootCoverPercentOfMonthUnit,
  Sep: private__livingRootCoverPercentOfMonthUnit,
  Oct: private__livingRootCoverPercentOfMonthUnit,
  Nov: private__livingRootCoverPercentOfMonthUnit,
  Dec: private__livingRootCoverPercentOfMonthUnit,
};

/**
 * This should only be used as a transformer alone with one of the standard kpi.transformation.helpers
 * Example usage:
 * const metrics = makeTopLevelMetric(response, makeLivingRootCoverMetrics);
 */
export const makeLivingRootCoverMetrics = (
  data: LivingRootCoverMetricData
): LivingRootCoverMetrics | null => {
  const livingRootCoverMonths = getTypedValues(data);

  // These can be removed once a response deserializer and type validator is built
  // https://regrow.atlassian.net/browse/SI-1379
  // This method should handle
  // expected null, undefined, Finite checks as those are valid numbers
  if (
    // Check if ALL values are nil (individual months could be if we do not have data for them)
    livingRootCoverMonths.every(isNil) ||
    // or any value is invalid
    livingRootCoverMonths.some(monthVal => !Number.isFinite(monthVal ?? 0))
  ) {
    return null;
  }

  const yearAvgValue = meanBy(getTypedValues(data));

  const makeMetric = (key: LivingRootKeys, value?: number | null): Metric | null =>
    isDefined(value)
      ? {
          value,
          unit: LIVING_ROOT_COVER_METRICS_UNIT_MAP[key]().unit,
          formattedValue: LIVING_ROOT_COVER_METRICS_FORMATTER_MAP[key](value),
        }
      : null;

  return {
    yearAvg: Number.isFinite(yearAvgValue) ? makeMetric('yearAvg', yearAvgValue) : null,
    Jan: makeMetric('Jan', data.living_root_cover_1),
    Feb: makeMetric('Feb', data.living_root_cover_2),
    Mar: makeMetric('Mar', data.living_root_cover_3),
    Apr: makeMetric('Apr', data.living_root_cover_4),
    May: makeMetric('May', data.living_root_cover_5),
    Jun: makeMetric('Jun', data.living_root_cover_6),
    Jul: makeMetric('Jul', data.living_root_cover_7),
    Aug: makeMetric('Aug', data.living_root_cover_8),
    Sep: makeMetric('Sep', data.living_root_cover_9),
    Oct: makeMetric('Oct', data.living_root_cover_10),
    Nov: makeMetric('Nov', data.living_root_cover_11),
    Dec: makeMetric('Dec', data.living_root_cover_12),
  };
};

export type LivingRootCoverDaysMetrics = Record<
  `${keyof LivingRootCoverMetrics}Days`,
  Metric | null
>;

export const LIVING_ROOT_COVER_DAYS_UNIT = (): UnitDetail<UnitType> => {
  const unitDetails = getUnit('d');

  return merge(unitDetails, {
    unitName: {
      abbr: 'days',
    },
  });
};

export const LIVING_ROOT_COVER_DAYS_FORMATTER = (v: number) => v.toFixed();

export const makeLivingRootCoverDaysMetricfromMetrics = (
  metrics: LivingRootCoverMetrics | null,
  metricKey: keyof LivingRootCoverMetrics
) => {
  const livingRootCoverMetric = isDefined(metrics?.[metricKey]) ? metrics[metricKey] : null;

  if (isNil(livingRootCoverMetric)) return null;

  const jsStandardMonth = new Date(`${metricKey} 1 2015`).getMonth(); // Must provide year to firefox (defaulting to non leap year) that is the Regrow default start year
  const daysInMonth = new Date(0, jsStandardMonth + 1, 0).getDate();
  const dayMultiplier = metricKey === 'yearAvg' ? DAYS_IN_YEAR : daysInMonth;

  const value = Math.round(livingRootCoverMetric.value * dayMultiplier);

  return {
    value,
    unit: LIVING_ROOT_COVER_DAYS_UNIT().unit,
    formattedValue: LIVING_ROOT_COVER_DAYS_FORMATTER(value),
  };
};

export const makeAllLivingRootCoverDaysMetricsFromMetrics = (
  metrics: LivingRootCoverMetrics | null
): LivingRootCoverDaysMetrics | null => {
  if (isNil(metrics)) return null;

  const pairs = getTypedEntries(metrics).map<
    [`${keyof LivingRootCoverMetrics}Days`, Metric | null]
  >(([metricKey]) => [
    `${metricKey}Days`,
    makeLivingRootCoverDaysMetricfromMetrics(metrics, metricKey),
  ]);

  return getTypedObjectFromEntries(pairs);
};
