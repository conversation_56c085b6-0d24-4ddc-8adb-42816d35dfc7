import {isNil} from '_common/utils/typeGuards';

import type {VolumeWeightedGhgEmissionsFactorKPIMetricData} from 'containers/si/api/apiTypes';
import {CO2E} from 'containers/si/constants';
import {getRatioUnits} from 'containers/si/utils/convert';
import {formatAllNumbers} from 'containers/si/utils/formatters/number.format';
import {type Metric, type UnitDetail, type UnitType} from 'containers/si/utils/value.types';

export type VolumeWeightedGhgEFMetrics = {
  volumeWeighteGhgEmissionsPerYeild: Metric;
};

export const private__ghgEFUnit = () =>
  getRatioUnits({
    unit1: 'kg',
    unit2: 'kg',
    subUnit1: CO2E,
    subUnit2: `yield`,
  });

export const VOLUMEWEIGHTEDGHGEFMETRICS_FORMATTER_MAP: Record<
  keyof VolumeWeightedGhgEFMetrics,
  (value: number) => string
> = {
  volumeWeighteGhgEmissionsPerYeild: formatAllNumbers,
};

export const VOLUMEWEIGHTEDGHGEFMETRICS_UNIT_MAP: Record<
  keyof VolumeWeightedGhgEFMetrics,
  () => UnitDetail<UnitType>
> = {
  volumeWeighteGhgEmissionsPerYeild: private__ghgEFUnit,
};

/**
 * This should only be used as a transformer alone with one of the standard kpi.transformation.helpers
 * Example usage:
 * const metricData = makeTopLevelMetric(response, makeVolumeWeightedGhgEFMetrics(MeasurementEnum.Metric));
 */
export const makeVolumeWeightedGhgEFMetrics = (
  data: VolumeWeightedGhgEmissionsFactorKPIMetricData
): VolumeWeightedGhgEFMetrics | null => {
  // These can be removed once a response deserializer and type validator is built
  // https://regrow.atlassian.net/browse/SI-1379
  // This method should handle
  // expected nulls or undefineds and
  // 0 and Finite checks as those are valid numbers
  if (
    isNil(data.volume_weighted_ghg_emissions_factor) ||
    !Number.isFinite(data.volume_weighted_ghg_emissions_factor)
  ) {
    return null;
  }

  const value = data.volume_weighted_ghg_emissions_factor;
  const unitDetails = VOLUMEWEIGHTEDGHGEFMETRICS_UNIT_MAP.volumeWeighteGhgEmissionsPerYeild();

  return {
    volumeWeighteGhgEmissionsPerYeild: {
      value,
      unit: unitDetails.unit,
      formattedValue:
        VOLUMEWEIGHTEDGHGEFMETRICS_FORMATTER_MAP.volumeWeighteGhgEmissionsPerYeild(value),
    },
  };
};
