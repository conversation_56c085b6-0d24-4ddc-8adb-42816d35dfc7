import {type MeasurementEnum} from '_common/utils/measurement-unit-options';
import {isNil} from '_common/utils/typeGuards';

import type {CoverCropMetricData} from 'containers/si/api/apiTypes';
import {convertValue, getUnit} from 'containers/si/utils/convert';
import {
  formatFloatToPercent,
  formatLargeNumbers,
} from 'containers/si/utils/formatters/number.format';
import {isFiniteNumber} from 'containers/si/utils/utils';
import {type Metric, type UnitDetail, type UnitType} from 'containers/si/utils/value.types';

export const COVER_CROP_CONTENT = {
  name: 'cover cropping',
  // TODO: SI-3446 update explainer value to use inclusive if inclusive is rolled out
  explainer:
    (panelLabel: string) =>
    (userUnitsSystem: MeasurementEnum, isCoverCropNaInclusiveEnabled: boolean | undefined) => {
      if (isNil(userUnitsSystem)) return [];

      return isCoverCropNaInclusiveEnabled
        ? COVER_CROP_CONTENT_EXPLAINER_NOT_APPLICABLE_INCLUSIVE({panelLabel, userUnitsSystem})
        : COVER_CROP_CONTENT_EXPLAINER_NOT_APPLICABLE_EXCLUSIVE({panelLabel, userUnitsSystem});
    },
  categoryColor: '1' as const,
};

// TODO: SI-3446 delete this constant and update explainer value above to use this value instead if inclusive is rolled out.
const COVER_CROP_CONTENT_EXPLAINER_NOT_APPLICABLE_INCLUSIVE = ({
  panelLabel,
  userUnitsSystem,
}: {
  userUnitsSystem: MeasurementEnum;
  panelLabel: string;
}) => [
  `The percentage of cropland practicing cover cropping broken down ${panelLabel}.`,
  `The percentage adoption is based on verified ${getUnit(userUnitsSystem).unitName.plural} only (${
    getUnit(userUnitsSystem).unitName.plural
  } that we can confidently determine cover cropping on through remote-sensing). This includes "not applicable" ${
    getUnit(userUnitsSystem).unitName.plural
  } (${
    getUnit(userUnitsSystem).unitName.plural
  } where green cover is classified as perennials, growth of weeds, or volunteer crops).`,
  `Adoption is not determined on unverified ${getUnit(userUnitsSystem).unitName.plural}.`,
];

// TODO: SI-3446 delete this constant
const COVER_CROP_CONTENT_EXPLAINER_NOT_APPLICABLE_EXCLUSIVE = ({
  panelLabel,
  userUnitsSystem,
}: {
  userUnitsSystem: MeasurementEnum;
  panelLabel: string;
}) => [
  `The percentage of cropland practicing cover cropping broken down ${panelLabel}.`,
  `The percentage adoption is based on verified ${getUnit(userUnitsSystem).unitName.plural} only (${
    getUnit(userUnitsSystem).unitName.plural
  } that we can confidently determine cover cropping on through remote-sensing). Adoption is not determined on unverified ${
    getUnit(userUnitsSystem).unitName.plural
  } or not applicable ${getUnit(userUnitsSystem).unitName.plural} (${
    getUnit(userUnitsSystem).unitName.plural
  } where green cover is classified as perennials, growth of weeds, or volunteer crops).`,
];

// TODO: SI-3064 and SI-3446 clean up type, unit map, formatter map and label map depending on expected value and na exclusive roll out
export type CoverCropMetrics = {
  adoption: Metric;
  adoptionStdDev?: Metric; // TODO: SI-3064 when rolled out this should not be optional
  totalTrackedArea: Metric;
  adoptionNotApplicableExclusive?: Metric | null;
  adoptionStdDevNotApplicableExclusive?: Metric | null;
  totalTrackedAreaNaExclusive?: Metric;
  covercroppedArea: Metric;
  covercroppedAreaStdDev?: Metric | null; // TODO: SI-3064 when rolled out this should not be optional
  unknownArea: Metric;
  notApplicableArea: Metric;
};

export const private__covercropAreaUnit = (unitsSystem: MeasurementEnum) => getUnit(unitsSystem);

export const private__covercropAdoptionUnit = (): UnitDetail<UnitType> => {
  return {
    unit: 'unit-interval' as const,
    unitName: {
      singular: `% of cropland area practicing`,
      plural: `% of cropland area practicing`,
      abbr: `adoption`,
    },
  };
};

// TODO: SI-3064 clean up type, unit map, formatter map and label map depending on expected value roll out
export const COVERCROPMETRICS_FORMATTER_MAP: Record<
  keyof CoverCropMetrics,
  (value: number) => string
> = {
  adoption: value => formatFloatToPercent(value, {sigDigits: 1}),
  adoptionStdDev: value => formatFloatToPercent(value, {sigDigits: 1}), // This should always match adoption formatter, to keep them consistent
  totalTrackedArea: formatLargeNumbers,
  adoptionNotApplicableExclusive: value => formatFloatToPercent(value, {sigDigits: 1}),
  adoptionStdDevNotApplicableExclusive: value => formatFloatToPercent(value, {sigDigits: 1}), // This should always match adoption formatter, to keep them consistent
  totalTrackedAreaNaExclusive: formatLargeNumbers,
  covercroppedArea: formatLargeNumbers,
  covercroppedAreaStdDev: formatLargeNumbers, // Should always match covercroppedArea formatter, to keep them consistent
  unknownArea: formatLargeNumbers,
  notApplicableArea: formatLargeNumbers,
};

// TODO: SI-3064 clean up type, unit map, formatter map and label map depending on expected value roll out
export const COVERCROPMETRICS_UNIT_MAP: Record<
  keyof CoverCropMetrics,
  (unitsSystem: MeasurementEnum) => UnitDetail<UnitType>
> = {
  adoption: private__covercropAdoptionUnit,
  adoptionStdDev: private__covercropAdoptionUnit, // This should always match adoption unit, to keep them consistent
  totalTrackedArea: private__covercropAreaUnit,
  adoptionNotApplicableExclusive: private__covercropAdoptionUnit,
  adoptionStdDevNotApplicableExclusive: private__covercropAdoptionUnit, // This should always match adoptionNaExclusive unit, to keep them consistent
  totalTrackedAreaNaExclusive: private__covercropAreaUnit,
  covercroppedArea: private__covercropAreaUnit,
  covercroppedAreaStdDev: private__covercropAreaUnit, // Should always match covercroppedArea unit, to keep them consistent
  unknownArea: private__covercropAreaUnit,
  notApplicableArea: private__covercropAreaUnit,
};

// TODO: SI-3064 remove transform depending on expected value roll out
/**
 * This should only be used as a transformer alone with one of the standard kpi.transformation.helpers
 * Example usage:
 * const metricData = makeTopLevelMetric(response, makeCoverCropMetrics(MeasurementEnum.Metric));
 */
export const makeCoverCropMetrics =
  (unitsSystem: MeasurementEnum) =>
  (data: CoverCropMetricData): CoverCropMetrics | null => {
    const {
      cover_cc_area_m2,
      cover_nocc_area_m2,
      cover_unknowncc_area_m2,
      cover_not_applicable_cc_area_m2,
    } = data;

    // area metrics coalesce to 0; if they total 0 below, we will return null
    const cover_cc_area_m2_ = cover_cc_area_m2 ?? 0;
    const cover_nocc_area_m2_ = cover_nocc_area_m2 ?? 0;
    const cover_unknowncc_area_m2_ = cover_unknowncc_area_m2 ?? 0;
    const cover_not_applicable_cc_area_m2_ = cover_not_applicable_cc_area_m2 ?? 0;

    // These can be removed once a response deserializer and type validator is built
    // https://regrow.atlassian.net/browse/SI-1379
    // This method should handle
    // expected nulls or undefineds and
    // 0 and Finite checks as those are valid numbers
    if (
      !Number.isFinite(cover_cc_area_m2_) ||
      !Number.isFinite(cover_nocc_area_m2_) ||
      !Number.isFinite(cover_unknowncc_area_m2_) ||
      !Number.isFinite(cover_not_applicable_cc_area_m2_)
    ) {
      return null;
    }

    const total_area_m2 = cover_cc_area_m2_ + cover_nocc_area_m2_;

    if (total_area_m2 === 0) return null;

    const cover_cc_adoption_rate = cover_cc_area_m2_ / total_area_m2;

    const ccCropFieldAreaValue = convertValue({
      value: cover_cc_area_m2_,
      from: 'm2',
      to: unitsSystem,
    });

    const totalCropFieldAreaValue = convertValue({
      value: total_area_m2,
      from: 'm2',
      to: unitsSystem,
    });

    const unknownAreaValue = convertValue({
      value: cover_unknowncc_area_m2_,
      from: 'm2',
      to: unitsSystem,
    });

    const notApplicableAreaValue = convertValue({
      value: cover_not_applicable_cc_area_m2_,
      from: 'm2',
      to: unitsSystem,
    });

    return {
      adoption: {
        value: cover_cc_adoption_rate,
        unit: COVERCROPMETRICS_UNIT_MAP.adoption(unitsSystem).unit,
        formattedValue: COVERCROPMETRICS_FORMATTER_MAP.adoption(cover_cc_adoption_rate),
      },
      covercroppedArea: {
        value: ccCropFieldAreaValue,
        unit: COVERCROPMETRICS_UNIT_MAP.covercroppedArea(unitsSystem).unit,
        formattedValue: COVERCROPMETRICS_FORMATTER_MAP.covercroppedArea(ccCropFieldAreaValue),
      },
      totalTrackedArea: {
        value: totalCropFieldAreaValue,
        unit: COVERCROPMETRICS_UNIT_MAP.totalTrackedArea(unitsSystem).unit,
        formattedValue: COVERCROPMETRICS_FORMATTER_MAP.totalTrackedArea(totalCropFieldAreaValue),
      },
      unknownArea: {
        value: unknownAreaValue,
        unit: COVERCROPMETRICS_UNIT_MAP.unknownArea(unitsSystem).unit,
        formattedValue: COVERCROPMETRICS_FORMATTER_MAP.unknownArea(unknownAreaValue),
      },
      notApplicableArea: {
        value: notApplicableAreaValue,
        unit: COVERCROPMETRICS_UNIT_MAP.notApplicableArea(unitsSystem).unit,
        formattedValue: COVERCROPMETRICS_FORMATTER_MAP.notApplicableArea(notApplicableAreaValue),
      },
    };
  };

// TODO: SI-3064 remove transform depending on expected value roll out
/**
 * This should only be used as a transformer alone with one of the standard kpi.transformation.helpers
 * Example usage:
 * const metricData = makeTopLevelMetric(response, makeCoverCropMetrics(MeasurementEnum.Metric));
 */
export const makeCoverCropExpectedMetrics =
  (unitsSystem: MeasurementEnum) =>
  (data: CoverCropMetricData): CoverCropMetrics | null => {
    const {
      cover_cc_adoption_rate,
      cover_cc_adoption_rate_stddev,
      cover_cc_expected_area_m2,
      cover_cc_area_m2_stddev,
      cover_nocc_expected_area_m2,
      cover_unknowncc_area_m2,
      cover_not_applicable_cc_area_m2,
    } = data;

    // area metrics coalesce to 0; if they total 0 below, we will return null
    const cover_cc_expected_area_m2_ = cover_cc_expected_area_m2 ?? 0;
    const cover_cc_area_m2_stddev_ = cover_cc_area_m2_stddev ?? 0;
    const cover_nocc_expected_area_m2_ = cover_nocc_expected_area_m2 ?? 0;
    const cover_unknowncc_area_m2_ = cover_unknowncc_area_m2 ?? 0;
    const cover_not_applicable_cc_area_m2_ = cover_not_applicable_cc_area_m2 ?? 0;

    // These can be removed once a response deserializer and type validator is built
    // https://regrow.atlassian.net/browse/SI-1379
    // This method should handle
    // expected nulls or undefineds and
    // 0 and Finite checks as those are valid numbers
    if (
      !isFiniteNumber(cover_cc_adoption_rate) ||
      !isFiniteNumber(cover_cc_adoption_rate_stddev) ||
      !isFiniteNumber(cover_cc_expected_area_m2_) ||
      !isFiniteNumber(cover_cc_area_m2_stddev_) ||
      !isFiniteNumber(cover_nocc_expected_area_m2_) ||
      !isFiniteNumber(cover_unknowncc_area_m2_) ||
      !isFiniteNumber(cover_not_applicable_cc_area_m2_)
    ) {
      return null;
    }

    const total_area_m2 = cover_cc_expected_area_m2_ + cover_nocc_expected_area_m2_;

    if (total_area_m2 === 0) return null;

    const totalCropFieldAreaValue = convertValue({
      value: total_area_m2,
      from: 'm2',
      to: unitsSystem,
    });

    const ccCropFieldAreaValue = convertValue({
      value: cover_cc_expected_area_m2_,
      from: 'm2',
      to: unitsSystem,
    });

    const ccCropFieldAreaStdDevValue = convertValue({
      value: cover_cc_area_m2_stddev_,
      from: 'm2',
      to: unitsSystem,
    });

    const unknownAreaValue = convertValue({
      value: cover_unknowncc_area_m2_,
      from: 'm2',
      to: unitsSystem,
    });

    const notApplicableAreaValue = convertValue({
      value: cover_not_applicable_cc_area_m2_,
      from: 'm2',
      to: unitsSystem,
    });

    return {
      adoption: {
        value: cover_cc_adoption_rate,
        unit: COVERCROPMETRICS_UNIT_MAP.adoption(unitsSystem).unit,
        formattedValue: COVERCROPMETRICS_FORMATTER_MAP.adoption(cover_cc_adoption_rate),
      },
      adoptionStdDev: {
        value: cover_cc_adoption_rate_stddev,
        unit: COVERCROPMETRICS_UNIT_MAP.adoptionStdDev(unitsSystem).unit,
        formattedValue: COVERCROPMETRICS_FORMATTER_MAP.adoptionStdDev(
          cover_cc_adoption_rate_stddev
        ),
      },

      covercroppedArea: {
        value: ccCropFieldAreaValue,
        unit: COVERCROPMETRICS_UNIT_MAP.covercroppedArea(unitsSystem).unit,
        formattedValue: COVERCROPMETRICS_FORMATTER_MAP.covercroppedArea(ccCropFieldAreaValue),
      },
      covercroppedAreaStdDev: isNil(cover_cc_expected_area_m2)
        ? null
        : {
            value: ccCropFieldAreaStdDevValue,
            unit: COVERCROPMETRICS_UNIT_MAP.covercroppedAreaStdDev(unitsSystem).unit,
            formattedValue: COVERCROPMETRICS_FORMATTER_MAP.covercroppedAreaStdDev(
              ccCropFieldAreaStdDevValue
            ),
          },
      totalTrackedArea: {
        value: totalCropFieldAreaValue,
        unit: COVERCROPMETRICS_UNIT_MAP.totalTrackedArea(unitsSystem).unit,
        formattedValue: COVERCROPMETRICS_FORMATTER_MAP.totalTrackedArea(totalCropFieldAreaValue),
      },
      unknownArea: {
        value: unknownAreaValue,
        unit: COVERCROPMETRICS_UNIT_MAP.unknownArea(unitsSystem).unit,
        formattedValue: COVERCROPMETRICS_FORMATTER_MAP.unknownArea(unknownAreaValue),
      },
      notApplicableArea: {
        value: notApplicableAreaValue,
        unit: COVERCROPMETRICS_UNIT_MAP.notApplicableArea(unitsSystem).unit,
        formattedValue: COVERCROPMETRICS_FORMATTER_MAP.notApplicableArea(notApplicableAreaValue),
      },
    };
  };

// TODO: SI-3064 SI-3446 remove transform depending on expected value roll out and cover crop not inclusive rollout
/**
 * This should only be used as a transformer alone with one of the standard kpi.transformation.helpers
 * Example usage:
 * const metricData = makeTopLevelMetric(response, makeCoverCropMetrics(MeasurementEnum.Metric));
 */
export const makeCoverCropExpectedWithNaExclusiveMetrics =
  (unitsSystem: MeasurementEnum) =>
  (data: CoverCropMetricData): CoverCropMetrics | null => {
    const {
      cover_cc_adoption_rate_na_inclusive,
      cover_cc_adoption_rate_na_inclusive_stddev,
      cover_cc_adoption_rate,
      cover_cc_adoption_rate_stddev,
      cover_cc_expected_area_m2,
      cover_cc_area_m2_stddev,
      cover_nocc_expected_area_m2,
      cover_unknowncc_area_m2,
      cover_not_applicable_cc_area_m2,
    } = data;

    // cover cc na exclusive adoption rate can be null because the denominator could be 0 area in the BE
    // when verified area is entirely N/A (ex. alfalfa only)
    const cover_cc_adoption_rate_ = cover_cc_adoption_rate ?? 0;
    const cover_cc_adoption_rate_stddev_ = cover_cc_adoption_rate_stddev ?? 0;

    // BE returns null for na inclusive adoption rate std dev
    // when cover_cc_adoption_rate_na_inclusive is 0 / verified area is entirely N/A (ex. alfalfa only)
    const cover_cc_adoption_rate_na_inclusive_stddev_ =
      isNil(cover_cc_adoption_rate_na_inclusive_stddev) && cover_cc_adoption_rate_na_inclusive === 0
        ? 0
        : cover_cc_adoption_rate_na_inclusive_stddev;

    // area metrics coalesce to 0; if they total 0 below, we will return null
    const cover_cc_expected_area_m2_ = cover_cc_expected_area_m2 ?? 0;
    const cover_cc_area_m2_stddev_ = cover_cc_area_m2_stddev ?? 0;
    const cover_nocc_expected_area_m2_ = cover_nocc_expected_area_m2 ?? 0;
    const cover_unknowncc_area_m2_ = cover_unknowncc_area_m2 ?? 0;
    const cover_not_applicable_cc_area_m2_ = cover_not_applicable_cc_area_m2 ?? 0;

    // These can be removed once a response deserializer and type validator is built
    // https://regrow.atlassian.net/browse/SI-1379
    // This method should handle
    // expected nulls or undefineds and
    // 0 and Finite checks as those are valid numbers
    if (
      !isFiniteNumber(cover_cc_adoption_rate_na_inclusive) ||
      !isFiniteNumber(cover_cc_adoption_rate_na_inclusive_stddev_) ||
      !isFiniteNumber(cover_cc_adoption_rate_) ||
      !isFiniteNumber(cover_cc_adoption_rate_stddev_) ||
      !isFiniteNumber(cover_cc_expected_area_m2_) ||
      !isFiniteNumber(cover_cc_area_m2_stddev_) ||
      !isFiniteNumber(cover_nocc_expected_area_m2_) ||
      !isFiniteNumber(cover_unknowncc_area_m2_) ||
      !isFiniteNumber(cover_not_applicable_cc_area_m2_)
    ) {
      return null;
    }

    const total_area_na_exclusive_m2 = cover_cc_expected_area_m2_ + cover_nocc_expected_area_m2_;
    const total_area_m2 = total_area_na_exclusive_m2 + cover_not_applicable_cc_area_m2_;

    if (total_area_m2 === 0) return null; // area is entirely unverified

    const totalCropFieldAreaValue = convertValue({
      value: total_area_m2,
      from: 'm2',
      to: unitsSystem,
    });

    const totalCropFieldAreaNaExclusiveValue = convertValue({
      value: total_area_na_exclusive_m2,
      from: 'm2',
      to: unitsSystem,
    });

    const ccCropFieldAreaValue = convertValue({
      value: cover_cc_expected_area_m2_,
      from: 'm2',
      to: unitsSystem,
    });

    const ccCropFieldAreaStdDevValue = convertValue({
      value: cover_cc_area_m2_stddev_,
      from: 'm2',
      to: unitsSystem,
    });

    const unknownAreaValue = convertValue({
      value: cover_unknowncc_area_m2_,
      from: 'm2',
      to: unitsSystem,
    });

    const notApplicableAreaValue = convertValue({
      value: cover_not_applicable_cc_area_m2_,
      from: 'm2',
      to: unitsSystem,
    });

    // todo: make sure every non-required stddev requires its mated kpi to be defined (pre-coalesce)
    return {
      adoption: {
        value: cover_cc_adoption_rate_na_inclusive,
        unit: COVERCROPMETRICS_UNIT_MAP.adoption(unitsSystem).unit,
        formattedValue: COVERCROPMETRICS_FORMATTER_MAP.adoption(
          cover_cc_adoption_rate_na_inclusive
        ),
      },
      adoptionStdDev: {
        value: cover_cc_adoption_rate_na_inclusive_stddev_,
        unit: COVERCROPMETRICS_UNIT_MAP.adoptionStdDev(unitsSystem).unit,
        formattedValue: COVERCROPMETRICS_FORMATTER_MAP.adoptionStdDev(
          cover_cc_adoption_rate_na_inclusive_stddev_
        ),
      },
      totalTrackedArea: {
        value: totalCropFieldAreaValue,
        unit: COVERCROPMETRICS_UNIT_MAP.totalTrackedArea(unitsSystem).unit,
        formattedValue: COVERCROPMETRICS_FORMATTER_MAP.totalTrackedArea(totalCropFieldAreaValue),
      },
      adoptionNotApplicableExclusive:
        cover_not_applicable_cc_area_m2_ === 0 || total_area_na_exclusive_m2 === 0 // this will allow us to have a distinct hidden (vs no data state) for the metric if there is no N/A area or the full verified area ia N/A
          ? undefined
          : isNil(cover_cc_adoption_rate)
          ? null
          : {
              value: cover_cc_adoption_rate,
              unit: COVERCROPMETRICS_UNIT_MAP.adoption(unitsSystem).unit,
              formattedValue: COVERCROPMETRICS_FORMATTER_MAP.adoption(cover_cc_adoption_rate),
            },
      adoptionStdDevNotApplicableExclusive:
        cover_not_applicable_cc_area_m2_ === 0 || total_area_na_exclusive_m2 === 0 // this will allow us to have a distinct hidden (vs no data state) for the metric if there is no N/A area or the full verified area ia N/A
          ? undefined
          : isNil(cover_cc_adoption_rate)
          ? null
          : {
              value: cover_cc_adoption_rate_stddev_,
              unit: COVERCROPMETRICS_UNIT_MAP.adoption(unitsSystem).unit,
              formattedValue: COVERCROPMETRICS_FORMATTER_MAP.adoption(
                cover_cc_adoption_rate_stddev_
              ),
            },
      totalTrackedAreaNaExclusive: {
        value: totalCropFieldAreaNaExclusiveValue,
        unit: COVERCROPMETRICS_UNIT_MAP.totalTrackedArea(unitsSystem).unit,
        formattedValue: COVERCROPMETRICS_FORMATTER_MAP.totalTrackedArea(
          totalCropFieldAreaNaExclusiveValue
        ),
      },
      covercroppedArea: {
        value: ccCropFieldAreaValue,
        unit: COVERCROPMETRICS_UNIT_MAP.covercroppedArea(unitsSystem).unit,
        formattedValue: COVERCROPMETRICS_FORMATTER_MAP.covercroppedArea(ccCropFieldAreaValue),
      },
      covercroppedAreaStdDev: isNil(cover_cc_expected_area_m2)
        ? null
        : {
            value: ccCropFieldAreaStdDevValue,
            unit: COVERCROPMETRICS_UNIT_MAP.covercroppedAreaStdDev(unitsSystem).unit,
            formattedValue: COVERCROPMETRICS_FORMATTER_MAP.covercroppedAreaStdDev(
              ccCropFieldAreaStdDevValue
            ),
          },
      unknownArea: {
        value: unknownAreaValue,
        unit: COVERCROPMETRICS_UNIT_MAP.unknownArea(unitsSystem).unit,
        formattedValue: COVERCROPMETRICS_FORMATTER_MAP.unknownArea(unknownAreaValue),
      },
      notApplicableArea: {
        value: notApplicableAreaValue,
        unit: COVERCROPMETRICS_UNIT_MAP.notApplicableArea(unitsSystem).unit,
        formattedValue: COVERCROPMETRICS_FORMATTER_MAP.notApplicableArea(notApplicableAreaValue),
      },
    };
  };
