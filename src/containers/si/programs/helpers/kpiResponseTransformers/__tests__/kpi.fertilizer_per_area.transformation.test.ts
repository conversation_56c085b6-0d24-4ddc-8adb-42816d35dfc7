import {MeasurementEnum} from '_common/utils/measurement-unit-options';

import {
  KPIFertPerAreaMock,
  KPIFertPerAreaSubsectionSummaryMock,
} from 'containers/si/__mocks__/KPIFertPerAreaMock';
import type {FertilizerPerAreaMetricData} from 'containers/si/api/apiTypes';
import {
  makeFertPerAreaMetrics,
  private__fertPerAreaUnit,
  type FertPerAreaMetrics,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.fertilizer_per_area.transformation';

const topLevelInput = KPIFertPerAreaMock.metric;

const topLevelOutputImperialUnits: FertPerAreaMetrics = {
  fertKPerArea: {
    formattedValue: '21.572',
    unit: 'kg/ac',
    value: 21.571878653249453,
  },
  fertMassPerArea: {
    formattedValue: '88.564',
    unit: 'kg/ac',
    value: 88.56393320648955,
  },
  fertNPerArea: {
    formattedValue: '39.502',
    unit: 'kg/ac',
    value: 39.502291486734755,
  },
  fertPPerArea: {
    formattedValue: '21.886',
    unit: 'kg/ac',
    value: 21.8863283559413,
  },
  fertSPerArea: {
    formattedValue: '6.086',
    unit: 'kg/ac',
    value: 6.086412201508672,
  },
};

const topLevelOutputMetricUnits: FertPerAreaMetrics = {
  fertMassPerArea: {
    formattedValue: '218.846',
    unit: 'kg/ha',
    value: 218.846033205081,
  },
  fertKPerArea: {
    formattedValue: '53.305',
    unit: 'kg/ha',
    value: 53.305221449887924,
  },
  fertNPerArea: {
    formattedValue: '97.612',
    unit: 'kg/ha',
    value: 97.61219360286138,
  },
  fertPPerArea: {
    formattedValue: '54.082',
    unit: 'kg/ha',
    value: 54.08224283528847,
  },
  fertSPerArea: {
    formattedValue: '15.04',
    unit: 'kg/ha',
    value: 15.039837533475481,
  },
};

const subregionInput = KPIFertPerAreaSubsectionSummaryMock.boundary_summary?.[872];

const subregionOutputImperialUnits: FertPerAreaMetrics = {
  fertKPerArea: {
    formattedValue: '45.272',
    unit: 'kg/ac',
    value: 45.272231950992015,
  },
  fertMassPerArea: {
    formattedValue: '122.117',
    unit: 'kg/ac',
    value: 122.11731045629934,
  },
  fertNPerArea: {
    formattedValue: '39.54',
    unit: 'kg/ac',
    value: 39.54026142893675,
  },
  fertPPerArea: {
    formattedValue: '29.972',
    unit: 'kg/ac',
    value: 29.971574653176805,
  },
  fertSPerArea: {
    formattedValue: '7.333',
    unit: 'kg/ac',
    value: 7.33324242319748,
  },
};

const subregionOutputMetricUnits: FertPerAreaMetrics = {
  fertMassPerArea: {
    formattedValue: '301.758',
    unit: 'kg/ha',
    value: 301.75815381555566,
  },
  fertKPerArea: {
    formattedValue: '111.87',
    unit: 'kg/ha',
    value: 111.8700131995599,
  },
  fertNPerArea: {
    formattedValue: '97.706',
    unit: 'kg/ha',
    value: 97.70601928258317,
  },
  fertPPerArea: {
    formattedValue: '74.061',
    unit: 'kg/ha',
    value: 74.06130220599857,
  },
  fertSPerArea: {
    formattedValue: '18.121',
    unit: 'kg/ha',
    value: 18.120819127423175,
  },
};

const expectedInputOutputMetric: {input: FertilizerPerAreaMetricData; output: FertPerAreaMetrics} =
  {
    input: {
      fert_kg_per_m2: 0.001,
      fert_k_kg_per_m2: 0.001,
      fert_n_kg_per_m2: 0.002,
      fert_p_kg_per_m2: 0.003,
      fert_s_kg_per_m2: 0.004,
    },
    output: {
      fertMassPerArea: {
        formattedValue: '10',
        unit: 'kg/ha',
        value: 10,
      },
      fertKPerArea: {
        formattedValue: '10',
        unit: 'kg/ha',
        value: 10,
      },
      fertNPerArea: {
        formattedValue: '20',
        unit: 'kg/ha',
        value: 20,
      },
      fertPPerArea: {
        formattedValue: '30',
        unit: 'kg/ha',
        value: 30,
      },
      fertSPerArea: {
        formattedValue: '40',
        unit: 'kg/ha',
        value: 40,
      },
    },
  };

const expectedInputOutputImperial: {
  input: FertilizerPerAreaMetricData;
  output: FertPerAreaMetrics;
} = {
  input: {
    fert_kg_per_m2: 0.02,
    fert_k_kg_per_m2: 0.01,
    fert_n_kg_per_m2: 0.02,
    fert_p_kg_per_m2: 0.03,
    fert_s_kg_per_m2: 0.04,
  },
  output: {
    fertMassPerArea: {
      formattedValue: '80.937',
      unit: 'kg/ac',
      value: 80.93720677449623,
    },
    fertKPerArea: {
      formattedValue: '40.469',
      unit: 'kg/ac',
      value: 40.468603387248116,
    },
    fertNPerArea: {
      formattedValue: '80.937',
      unit: 'kg/ac',
      value: 80.93720677449623,
    },
    fertPPerArea: {
      formattedValue: '121.406',
      unit: 'kg/ac',
      value: 121.40581016174434,
    },
    fertSPerArea: {
      formattedValue: '161.874',
      unit: 'kg/ac',
      value: 161.87441354899246,
    },
  },
};

describe('fertPerAreaAreaUnit', () => {
  it('should return the correct fertPerArea  unit given Imperial or Metrics system', () => {
    expect(private__fertPerAreaUnit(MeasurementEnum.ImperialUnits)).toEqual({
      unit: 'kg/ac',
      unitName: {
        singular: 'kilogram applied / acre',
        plural: 'kilograms applied / acre',
        abbr: 'kg applied / ac',
      },
    });
    expect(private__fertPerAreaUnit(MeasurementEnum.MetricUnits)).toEqual({
      unit: 'kg/ha',
      unitName: {
        singular: 'kilogram applied / hectare',
        plural: 'kilograms applied / hectare',
        abbr: 'kg applied / ha',
      },
    });
  });
});

describe('makeFertPerAreaMetrics', () => {
  it('Should return a FertPerAreaMetrics in ac given Imperial units system', () => {
    expect(
      makeFertPerAreaMetrics(MeasurementEnum.ImperialUnits)(expectedInputOutputImperial.input)
    ).toEqual(expectedInputOutputImperial.output);
  });

  it('Should return a FertPerAreaMetrics in ac given Metric units system', () => {
    expect(
      makeFertPerAreaMetrics(MeasurementEnum.MetricUnits)(expectedInputOutputMetric.input)
    ).toEqual(expectedInputOutputMetric.output);
  });

  it('Should return null when ALL null parameters are provided', () => {
    expect(
      makeFertPerAreaMetrics(MeasurementEnum.MetricUnits)({
        fert_kg_per_m2: undefined,
        fert_k_kg_per_m2: undefined,
        fert_n_kg_per_m2: undefined,
        fert_p_kg_per_m2: undefined,
        fert_s_kg_per_m2: undefined,
      })
    ).toBeNull();
    expect(
      makeFertPerAreaMetrics(MeasurementEnum.MetricUnits)({
        //@ts-expect-error checking all nil vals
        fert_kg_per_m2: null,
        //@ts-expect-error checking all nil vals
        fert_k_kg_per_m2: null,
        //@ts-expect-error checking all nil vals
        fert_n_kg_per_m2: null,
        //@ts-expect-error checking all nil vals
        fert_p_kg_per_m2: null,
        //@ts-expect-error checking all nil vals
        fert_s_kg_per_m2: null,
      })
    ).toBeNull();

    expect(makeFertPerAreaMetrics(MeasurementEnum.MetricUnits)({})).toEqual(null);
  });

  it('Should return null on fert_kg_per_m2 nil or 0', () => {
    expect(
      makeFertPerAreaMetrics(MeasurementEnum.MetricUnits)({
        ...expectedInputOutputMetric.input,
        fert_kg_per_m2: 0,
      })
    ).toBeNull();
    expect(
      makeFertPerAreaMetrics(MeasurementEnum.MetricUnits)({
        ...expectedInputOutputMetric.input,
        fert_kg_per_m2: undefined,
      })
    ).toBeNull();
  });

  it('Should not return null on partial nil', () => {
    expect(
      makeFertPerAreaMetrics(MeasurementEnum.MetricUnits)({
        ...expectedInputOutputMetric.input,
        fert_n_kg_per_m2: undefined,
      })
    ).not.toBeNull();

    expect(
      makeFertPerAreaMetrics(MeasurementEnum.MetricUnits)({
        ...expectedInputOutputMetric.input,
        // @ts-expect-error checking all nil vals
        fert_p_kg_per_m2: null,
      })
    ).not.toBeNull();

    expect(
      makeFertPerAreaMetrics(MeasurementEnum.MetricUnits)({
        fert_kg_per_m2: 1,
        fert_k_kg_per_m2: 1,
        fert_n_kg_per_m2: 2,
        fert_p_kg_per_m2: 3,
      })
    ).not.toBeNull();
  });

  it('Should return null when non finite parameters are provided', () => {
    expect(
      makeFertPerAreaMetrics(MeasurementEnum.MetricUnits)({
        ...expectedInputOutputMetric.input,
        fert_k_kg_per_m2: NaN,
      })
    ).toEqual(null);
    expect(
      makeFertPerAreaMetrics(MeasurementEnum.MetricUnits)({
        ...expectedInputOutputMetric.input,
        fert_s_kg_per_m2: Infinity,
      })
    ).toEqual(null);
  });

  it('Should return partial null values for missing chemicals and should still sum those present', () => {
    expect(
      makeFertPerAreaMetrics(MeasurementEnum.MetricUnits)({
        ...expectedInputOutputMetric.input,
        // @ts-expect-error checking all nil vals
        fert_p_kg_per_m2: null,
      })
    ).toEqual({
      ...expectedInputOutputMetric.output,
      fertPPerArea: null,
    });

    expect(
      makeFertPerAreaMetrics(MeasurementEnum.MetricUnits)({
        ...expectedInputOutputMetric.input,
        fert_n_kg_per_m2: undefined,
      })
    ).toEqual({
      ...expectedInputOutputMetric.output,
      fertNPerArea: null,
    });

    expect(
      makeFertPerAreaMetrics(MeasurementEnum.MetricUnits)({
        fert_kg_per_m2: 0.001,
        fert_k_kg_per_m2: 0.001,
        fert_n_kg_per_m2: 0.002,
        fert_p_kg_per_m2: 0.003,
      })
    ).toEqual({
      ...expectedInputOutputMetric.output,
      fertSPerArea: null,
    });
  });

  it('Should return a FertPerAreaMetrics in acres units given KPI response data and an Imperial units system', () => {
    expect(makeFertPerAreaMetrics(MeasurementEnum.ImperialUnits)(topLevelInput)).toEqual(
      topLevelOutputImperialUnits
    );
    expect(makeFertPerAreaMetrics(MeasurementEnum.ImperialUnits)(subregionInput!)).toEqual(
      subregionOutputImperialUnits
    );
  });
  it('Should return a FertPerAreaMetrics in hectares units given KPI response data and a Metric units system', () => {
    expect(makeFertPerAreaMetrics(MeasurementEnum.MetricUnits)(topLevelInput)).toEqual(
      topLevelOutputMetricUnits
    );
    expect(makeFertPerAreaMetrics(MeasurementEnum.MetricUnits)(subregionInput!)).toEqual(
      subregionOutputMetricUnits
    );
  });
});
