import {
  KPIHeatStressMock,
  KPIHeatStressummarizeBySubsectionMock,
} from 'containers/si/__mocks__/KPIHeatStressMock';
import type {HeatStressMetrics} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.heat_stress.transformation';
import {makeHeatStressMetrics} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.heat_stress.transformation';

describe('makePurchaseVolumeMetrics', () => {
  it('Should reture PurchaseVolumeMetrics for a crop_type_summary with appropriate units', () => {
    const expectedHeatStressMetric: HeatStressMetrics = {
      daysOfHeatStress: {value: 5.4, formattedValue: '5.4', unit: 'd'},
      heatStressOccurrences: {value: 3.9, formattedValue: '3.9', unit: 'count'},
      tier: {value: 3, formattedValue: 'field-based', unit: 'metric-tier'},
    };

    expect(makeHeatStressMetrics(KPIHeatStressMock['metric'])).toEqual(expectedHeatStressMetric);

    const expectedHeatStressBoundaryMetric: HeatStressMetrics = {
      daysOfHeatStress: {value: 2.0882936288144, formattedValue: '2.088', unit: 'd'},
      heatStressOccurrences: {value: 3.00097327606, formattedValue: '3.001', unit: 'count'},
      tier: {value: 3, formattedValue: 'field-based', unit: 'metric-tier'},
    };

    expect(
      makeHeatStressMetrics(KPIHeatStressummarizeBySubsectionMock.boundary_summary[872]!)
    ).toEqual(expectedHeatStressBoundaryMetric);
  });
  it('Should return null when null parameters are provided', () => {
    expect(
      makeHeatStressMetrics({
        ...KPIHeatStressMock['metric'],
        avg_heat_stress_days: undefined,
      })
    ).toEqual(null);
    expect(
      makeHeatStressMetrics({
        ...KPIHeatStressMock['metric'],
        quantification_level: undefined,
      })
    ).toEqual(null);
    expect(
      makeHeatStressMetrics({
        ...KPIHeatStressMock['metric'],
        avg_heat_wave_events: undefined,
      })
    ).toEqual(null);
    expect(makeHeatStressMetrics({})).toEqual(null);
  });

  it('Should return null when non finite parameters are provided', () => {
    expect(
      makeHeatStressMetrics({
        ...KPIHeatStressMock['metric'],
        avg_heat_stress_days: Infinity,
      })
    ).toEqual(null);
    expect(
      makeHeatStressMetrics({
        ...KPIHeatStressMock['metric'],
        quantification_level: -Infinity,
      })
    ).toEqual(null);
    expect(
      makeHeatStressMetrics({
        ...KPIHeatStressMock['metric'],
        avg_heat_wave_events: NaN,
      })
    ).toEqual(null);
    expect(makeHeatStressMetrics({})).toEqual(null);
  });
});
