import {CO2E} from 'containers/si/constants';
import {
  makeVolumeWeightedGhgEFMetrics,
  private__ghgEFUnit,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.volume_weighted_ghg_emissions_factor.transformation';

describe('private__ghgEFUnit', () => {
  it('should return the correct ghg ef unit', () => {
    expect(private__ghgEFUnit()).toEqual({
      unit: 'kg/kg',
      unitName: {
        singular: `kilogram ${CO2E} / kilogram yield`,
        plural: `kilograms ${CO2E} / kilogram yield`,
        abbr: `kg ${CO2E} / kg yield`,
      },
    });
  });
});

describe('makeVolumeWeightedGhgEFMetrics', () => {
  it('Should return VolumeWeightedGhgEFMetrics with appropriate units', () => {
    expect(
      makeVolumeWeightedGhgEFMetrics({
        volume_weighted_ghg_emissions_factor: 0.5408840070399256,
      })
    ).toEqual({
      volumeWeighteGhgEmissionsPerYeild: {
        formattedValue: '0.541',
        unit: 'kg/kg',
        value: 0.5408840070399256,
      },
    });
  });

  it('Should return VolumeWeightedGhgEFMetrics in appropriate units given 0', () => {
    expect(
      makeVolumeWeightedGhgEFMetrics({
        volume_weighted_ghg_emissions_factor: 0,
      })
    ).toEqual({
      volumeWeighteGhgEmissionsPerYeild: {
        formattedValue: '0',
        unit: 'kg/kg',
        value: 0,
      },
    });
  });

  it('Should return null when null parameters are provided', () => {
    expect(
      makeVolumeWeightedGhgEFMetrics({
        //@ts-expect-error covering all nil cases
        volume_weighted_ghg_emissions_factor: null,
      })
    ).toEqual(null);
    expect(
      makeVolumeWeightedGhgEFMetrics({
        volume_weighted_ghg_emissions_factor: undefined,
      })
    ).toEqual(null);
    expect(makeVolumeWeightedGhgEFMetrics({})).toEqual(null);
  });

  it('Should return null when non finite parameters are provided', () => {
    expect(
      makeVolumeWeightedGhgEFMetrics({
        volume_weighted_ghg_emissions_factor: Infinity,
      })
    ).toEqual(null);
  });
});
