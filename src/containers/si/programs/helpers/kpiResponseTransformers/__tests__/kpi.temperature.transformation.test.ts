import {
  KPITemperatureMock,
  KPITemperatureSummarizeBySubsectionMock,
} from 'containers/si/__mocks__/KPITemperatureMock';
import {
  makeTemperatureMetrics,
  type TemperatureMetrics,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.temperature.transformation';

describe('makeTemperatureMetrics', () => {
  it('Should return temperature metrics for a crop_type_summary with appropriate units', () => {
    const expectedTemperatureMetric: TemperatureMetrics = {
      avgTemperature: {value: 20.444, formattedValue: '20.44', unit: 'C'},
      avgMaxTemperature: {value: 33.56, formattedValue: '33.56', unit: 'C'},
      yoyTemperatureDelta: {value: 5, formattedValue: '5', unit: 'C'},
      tier: {value: 3, formattedValue: 'field-based', unit: 'metric-tier'},
    };

    expect(makeTemperatureMetrics(KPITemperatureMock['metric'])).toEqual(expectedTemperatureMetric);

    const expectedTemperatureBoundaryMetric: TemperatureMetrics = {
      avgTemperature: {value: 23.00097327606, formattedValue: '23', unit: 'C'},
      avgMaxTemperature: {
        value: 32.0882936288144,
        formattedValue: '32.09',
        unit: 'C',
      },
      yoyTemperatureDelta: {value: 5, formattedValue: '5', unit: 'C'},
      tier: {value: 3, formattedValue: 'field-based', unit: 'metric-tier'},
    };

    expect(
      makeTemperatureMetrics(KPITemperatureSummarizeBySubsectionMock.boundary_summary[872]!)
    ).toEqual(expectedTemperatureBoundaryMetric);
  });
  it('Should return null when null parameters are provided', () => {
    expect(
      makeTemperatureMetrics({
        ...KPITemperatureMock['metric'],
        avg_max_temperature: undefined,
      })
    ).toEqual(null);
    expect(
      makeTemperatureMetrics({
        ...KPITemperatureMock['metric'],
        quantification_level: undefined,
      })
    ).toEqual(null);
    expect(
      makeTemperatureMetrics({
        ...KPITemperatureMock['metric'],
        avg_temperature: undefined,
      })
    ).toEqual(null);
    expect(makeTemperatureMetrics({})).toEqual(null);
  });

  it('Should return null when non finite parameters are provided', () => {
    expect(
      makeTemperatureMetrics({
        ...KPITemperatureMock['metric'],
        avg_max_temperature: Infinity,
      })
    ).toEqual(null);
    expect(
      makeTemperatureMetrics({
        ...KPITemperatureMock['metric'],
        quantification_level: -Infinity,
      })
    ).toEqual(null);
    expect(
      makeTemperatureMetrics({
        ...KPITemperatureMock['metric'],
        yoy_temperature_delta: NaN,
      })
    ).toEqual(null);
    expect(makeTemperatureMetrics({})).toEqual(null);
  });
});
