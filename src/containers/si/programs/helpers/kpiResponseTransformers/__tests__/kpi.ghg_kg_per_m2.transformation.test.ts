import pick from 'lodash/pick';

import {MeasurementEnum} from '_common/utils/measurement-unit-options';

import {
  KPIGhgKgPerM2Mock,
  KPIGhgKgPerM2SummarizeBySubsectionMock,
} from 'containers/si/__mocks__/KPIGhgKgPerM2Mock';
import type {GhgKgPerM2MetricData} from 'containers/si/api/apiTypes';
import {CO2E} from 'containers/si/constants';
import {
  makeGhgMetrics,
  private__ghgUnit,
  type GhgMetrics,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.ghg_kg_per_m2.transformation';

const topLevelInput = KPIGhgKgPerM2Mock.metric;

const subregionInput = KPIGhgKgPerM2SummarizeBySubsectionMock.boundary_summary[874]!;

describe('private__ghgUnit', () => {
  it('should return the correct ghg unit given Imperial or Metrics system', () => {
    expect(private__ghgUnit(MeasurementEnum.ImperialUnits)).toEqual({
      unit: 'mt/ac',
      unitName: {
        singular: `metric tonne ${CO2E} / acre`,
        plural: `metric tonnes ${CO2E} / acre`,
        abbr: `mt ${CO2E} / ac`,
      },
    });
    expect(private__ghgUnit(MeasurementEnum.MetricUnits)).toEqual({
      unit: 'mt/ha',
      unitName: {
        singular: `metric tonne ${CO2E} / hectare`,
        plural: `metric tonnes ${CO2E} / hectare`,
        abbr: `mt ${CO2E} / ha`,
      },
    });
  });
});

describe('makeGhgKgPerM2Metrics', () => {
  const input: GhgKgPerM2MetricData = {
    ghg_kg_per_m2: 0.1375152183104355,
    ghg_kg_per_m2_stderr: 0.001375152183104355,
  };
  const expectedImperial: GhgMetrics = {
    ghgMassPerArea: {
      formattedValue: '0.557',
      unit: 'mt/ac',
      value: 0.5565048829515854,
    },
    ghgStdErr: {
      formattedValue: '0.00557',
      unit: 'mt/ac',
      value: 0.005565048829515854,
    },
  };
  const expectedMetric: GhgMetrics = {
    ghgMassPerArea: {
      formattedValue: '1.375',
      unit: 'mt/ha',
      value: 1.3751521831043552,
    },
    ghgStdErr: {
      formattedValue: '0.0138',
      unit: 'mt/ha',
      value: 0.01375152183104355,
    },
  };
  it('Should return GhgMetrics with stderr with appropriate units given Imperial or Metric units system', () => {
    expect(makeGhgMetrics(MeasurementEnum.ImperialUnits)(input)).toEqual(expectedImperial);
    expect(makeGhgMetrics(MeasurementEnum.MetricUnits)(input)).toEqual(expectedMetric);
  });

  it('Should return GhgMetrics without stderr if invalid/no stderr is provided', () => {
    const noStdErrInput: GhgKgPerM2MetricData = pick(input, ['ghg_kg_per_m2']);
    const noStdErrExpected: GhgMetrics = pick(expectedMetric, ['ghgMassPerArea']);
    expect(makeGhgMetrics(MeasurementEnum.MetricUnits)(noStdErrInput)).toEqual(noStdErrExpected);
    expect(
      //@ts-expect-error covering all nil cases
      makeGhgMetrics(MeasurementEnum.MetricUnits)({...noStdErrInput, ghg_kg_per_m2_stderr: null})
    ).toEqual(noStdErrExpected);
    expect(
      makeGhgMetrics(MeasurementEnum.MetricUnits)({...noStdErrInput, ghg_kg_per_m2_stderr: NaN})
    ).toEqual(noStdErrExpected);
    expect(
      makeGhgMetrics(MeasurementEnum.MetricUnits)({...noStdErrInput, ghg_kg_per_m2_stderr: 0})
    ).toEqual(noStdErrExpected);
  });

  it('Should return GhgMetrics in appropriate units given 0 and Imperial or Metric system', () => {
    expect(
      makeGhgMetrics(MeasurementEnum.MetricUnits)({
        ghg_kg_per_m2: 0,
      })
    ).toEqual({
      ghgMassPerArea: {
        formattedValue: '0',
        unit: 'mt/ha',
        value: 0,
      },
    });
  });

  it('Should return null when null parameters are provided', () => {
    expect(
      makeGhgMetrics(MeasurementEnum.MetricUnits)({
        //@ts-expect-error covering all nil cases
        ghg_kg_per_m2: null,
      })
    ).toEqual(null);
    expect(
      makeGhgMetrics(MeasurementEnum.MetricUnits)({
        ghg_kg_per_m2: undefined,
      })
    ).toEqual(null);
    expect(makeGhgMetrics(MeasurementEnum.MetricUnits)({})).toEqual(null);
  });

  it('Should return null when non finite parameters are provided', () => {
    expect(
      makeGhgMetrics(MeasurementEnum.MetricUnits)({
        ghg_kg_per_m2: Infinity,
      })
    ).toEqual(null);
  });
});

it('Should return GhgMetrics with appropriate units given Imperial or Metric units system', () => {
  const expectedTopLevelImperial: GhgMetrics = {
    ghgMassPerArea: {
      formattedValue: '0.45',
      unit: 'mt/ac',
      value: 0.4499924086487088,
    },
    ghgStdErr: {
      formattedValue: '0.0045',
      unit: 'mt/ac',
      value: 0.004499924086487088,
    },
  };
  expect(makeGhgMetrics(MeasurementEnum.ImperialUnits)(topLevelInput)).toEqual(
    expectedTopLevelImperial
  );

  const expectedTopLevelMetric: GhgMetrics = {
    ghgMassPerArea: {
      formattedValue: '1.112',
      unit: 'mt/ha',
      value: 1.1119543818764548,
    },
    ghgStdErr: {
      formattedValue: '0.0111',
      unit: 'mt/ha',
      value: 0.011119543818764547,
    },
  };
  expect(makeGhgMetrics(MeasurementEnum.MetricUnits)(topLevelInput)).toEqual(
    expectedTopLevelMetric
  );

  const expectedSubregionImperial: GhgMetrics = {
    ghgMassPerArea: {
      formattedValue: '0.665',
      unit: 'mt/ac',
      value: 0.6650195816873421,
    },
    ghgStdErr: {
      formattedValue: '0.00665',
      unit: 'mt/ac',
      value: 0.006650195816873421,
    },
  };
  expect(makeGhgMetrics(MeasurementEnum.ImperialUnits)(subregionInput)).toEqual(
    expectedSubregionImperial
  );

  const expectedSubregionMetric: GhgMetrics = {
    ghgMassPerArea: {
      formattedValue: '1.643',
      unit: 'mt/ha',
      value: 1.643297583866938,
    },
    ghgStdErr: {
      formattedValue: '0.0164',
      unit: 'mt/ha',
      value: 0.01643297583866938,
    },
  };
  expect(makeGhgMetrics(MeasurementEnum.MetricUnits)(subregionInput)).toEqual(
    expectedSubregionMetric
  );
});
