import pick from 'lodash/pick';

import {MeasurementEnum} from '_common/utils/measurement-unit-options';

import {
  KPINetImpactMock,
  KPINetImpactSummarizeBySubsectionMock,
} from 'containers/si/__mocks__/KPINetImpactMock';
import type {NetImpactKgPerM2MetricData} from 'containers/si/api/apiTypes';
import {CO2E} from 'containers/si/constants';
import {
  makeNetImpactMetrics,
  private__netUnit,
  type NetImpactMetrics,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.net_impact_kg_per_m2.transformation';

const topLevelInput = KPINetImpactMock.metric;

const subregionInput = KPINetImpactSummarizeBySubsectionMock.boundary_summary[872]!;

describe('private__netUnit', () => {
  it('should return the correct net impact unit given Imperial or Metrics system', () => {
    expect(private__netUnit(MeasurementEnum.ImperialUnits)).toEqual({
      unit: 'mt/ac',
      unitName: {
        singular: `metric tonne ${CO2E} / acre`,
        plural: `metric tonnes ${CO2E} / acre`,
        abbr: `mt ${CO2E} / ac`,
      },
    });
    expect(private__netUnit(MeasurementEnum.MetricUnits)).toEqual({
      unit: 'mt/ha',
      unitName: {
        singular: `metric tonne ${CO2E} / hectare`,
        plural: `metric tonnes ${CO2E} / hectare`,
        abbr: `mt ${CO2E} / ha`,
      },
    });
  });
});

describe('makeNetImpactMetrics', () => {
  const input: NetImpactKgPerM2MetricData = {
    net_impact_kg_per_m2: 0.1375152183104355,
    net_impact_kg_per_m2_stderr: 0.001375152183104355,
  };
  const expectedImperial: NetImpactMetrics = {
    netImpact: {
      formattedValue: '0.557',
      unit: 'mt/ac',
      value: 0.5565048829515854,
    },
    netImpactStdErr: {
      formattedValue: '0.00557',
      unit: 'mt/ac',
      value: 0.005565048829515854,
    },
  };
  const expectedMetric: NetImpactMetrics = {
    netImpact: {
      formattedValue: '1.375',
      unit: 'mt/ha',
      value: 1.3751521831043552,
    },
    netImpactStdErr: {
      formattedValue: '0.0138',
      unit: 'mt/ha',
      value: 0.01375152183104355,
    },
  };
  it('Should return NetImpactMetrics with stderr with appropriate units given Imperial or Metric units system', () => {
    expect(makeNetImpactMetrics(MeasurementEnum.ImperialUnits)(input)).toEqual(expectedImperial);
    expect(makeNetImpactMetrics(MeasurementEnum.MetricUnits)(input)).toEqual(expectedMetric);
  });

  it('Should return NetImpactMetrics without stderr if invalid/no stderr is provided', () => {
    const noStdErrInput: NetImpactKgPerM2MetricData = pick(input, ['net_impact_kg_per_m2']);
    const noStdErrExpected: NetImpactMetrics = pick(expectedMetric, ['netImpact']);
    expect(makeNetImpactMetrics(MeasurementEnum.MetricUnits)(noStdErrInput)).toEqual(
      noStdErrExpected
    );
    expect(
      makeNetImpactMetrics(MeasurementEnum.MetricUnits)({
        ...noStdErrInput,
        //@ts-expect-error covering all nil cases
        net_impact_kg_per_m2_stderr: null,
      })
    ).toEqual(noStdErrExpected);
    expect(
      makeNetImpactMetrics(MeasurementEnum.MetricUnits)({
        ...noStdErrInput,
        net_impact_kg_per_m2_stderr: NaN,
      })
    ).toEqual(noStdErrExpected);
    expect(
      makeNetImpactMetrics(MeasurementEnum.MetricUnits)({
        ...noStdErrInput,
        net_impact_kg_per_m2_stderr: 0,
      })
    ).toEqual(noStdErrExpected);
  });

  it('Should return NetImpactMetrics in hectares given 0 and a Metric units system', () => {
    expect(
      makeNetImpactMetrics(MeasurementEnum.MetricUnits)({
        net_impact_kg_per_m2: 0,
      })
    ).toEqual({
      netImpact: {
        formattedValue: '0',
        unit: 'mt/ha',
        value: 0,
      },
    });
  });

  it('Should return null when null parameters are provided', () => {
    expect(
      makeNetImpactMetrics(MeasurementEnum.MetricUnits)({
        //@ts-expect-error checking all nil values
        net_impact_kg_per_m2: null,
      })
    ).toEqual(null);
    expect(
      makeNetImpactMetrics(MeasurementEnum.MetricUnits)({
        net_impact_kg_per_m2: undefined,
      })
    ).toEqual(null);
    expect(makeNetImpactMetrics(MeasurementEnum.MetricUnits)({})).toEqual(null);
  });

  it('Should return null when non finite parameters are provided', () => {
    expect(
      makeNetImpactMetrics(MeasurementEnum.MetricUnits)({
        net_impact_kg_per_m2: Infinity,
      })
    ).toEqual(null);
  });
  it('Should return a NetImpactMetrics with appropriate units given Imperial or Metric units system', () => {
    const expectedTopLevelImperial: NetImpactMetrics = {
      netImpact: {
        formattedValue: '0.135',
        unit: 'mt/ac',
        value: 0.13532313546919603,
      },
      netImpactStdErr: {
        formattedValue: '0.00135',
        unit: 'mt/ac',
        value: 0.0013532313546919604,
      },
    };
    expect(
      makeNetImpactMetrics(MeasurementEnum.ImperialUnits)({
        ...topLevelInput,
      })
    ).toEqual(expectedTopLevelImperial);

    const expectedTopLevelMetric: NetImpactMetrics = {
      netImpact: {
        formattedValue: '0.334',
        unit: 'mt/ha',
        value: 0.3343904265098437,
      },
      netImpactStdErr: {
        formattedValue: '0.00334',
        unit: 'mt/ha',
        value: 0.003343904265098437,
      },
    };
    expect(makeNetImpactMetrics(MeasurementEnum.MetricUnits)(topLevelInput)).toEqual(
      expectedTopLevelMetric
    );

    const expectedSubregionImperial: NetImpactMetrics = {
      netImpact: {
        formattedValue: '-0.00763',
        unit: 'mt/ac',
        value: -0.00763315579905804,
      },
      netImpactStdErr: {
        formattedValue: '0.00008',
        unit: 'mt/ac',
        value: 0.00007633155799058037,
      },
    };
    expect(makeNetImpactMetrics(MeasurementEnum.ImperialUnits)(subregionInput)).toEqual(
      expectedSubregionImperial
    );

    const expectedSubregionMetric: NetImpactMetrics = {
      netImpact: {
        formattedValue: '-0.0189',
        unit: 'mt/ha',
        value: -0.018861920501717366,
      },
      netImpactStdErr: {
        formattedValue: '0.00019',
        unit: 'mt/ha',
        value: 0.00018861920501717357,
      },
    };
    expect(makeNetImpactMetrics(MeasurementEnum.MetricUnits)(subregionInput)).toEqual(
      expectedSubregionMetric
    );
  });
});
