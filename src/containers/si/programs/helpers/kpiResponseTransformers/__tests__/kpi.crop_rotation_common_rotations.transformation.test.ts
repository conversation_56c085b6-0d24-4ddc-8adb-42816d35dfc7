import {MeasurementEnum} from '_common/utils/measurement-unit-options';

import {
  CROP_ROTATION_COMMON_ROTATIONS_EXPECTED_OUTPUT_IMPERIAL_MOCK,
  CROP_ROTATION_COMMON_ROTATIONS_EXPECTED_OUTPUT_METRIC_MOCK,
  CROP_ROTATION_COMMON_ROTATIONS_INPUT_MOCK,
} from 'containers/si/programs/helpers/__mocks__/commonRotationsMock';
import {
  makeCommonCropRotationsMetrics,
  private__commonCropRotationUnit,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.crop_rotation_common_rotations.transformation';

describe('private__commonCropRotationUnit', () => {
  it('should return the correct unit given Imperial or Metrics system', () => {
    expect(private__commonCropRotationUnit(MeasurementEnum.ImperialUnits)).toEqual({
      unit: 'ac',
      unitName: {
        singular: 'acre',
        plural: 'acres',
        abbr: 'ac',
      },
    });
    expect(private__commonCropRotationUnit(MeasurementEnum.MetricUnits)).toEqual({
      unit: 'ha',
      unitName: {
        singular: 'hectare',
        plural: 'hectares',
        abbr: 'ha',
      },
    });
  });
});

describe('makeCommonCropRotationsMetrics', () => {
  it('Should return CommonCropRotationMetric given Imperial or Metric units system', () => {
    expect(
      makeCommonCropRotationsMetrics(MeasurementEnum.ImperialUnits)({
        common_crop_rtn_m2: 10,
      })
    ).toEqual({
      rotationArea: {formattedValue: '0.00247', unit: 'ac', value: 0.0024710514233241506},
    });
    expect(
      makeCommonCropRotationsMetrics(MeasurementEnum.MetricUnits)({
        common_crop_rtn_m2: 10,
      })
    ).toEqual({rotationArea: {formattedValue: '0.001', unit: 'ha', value: 0.001}});
  });

  it('Should return a CommonCropRotationMetric when a 0 parameter is provided', () => {
    expect(
      makeCommonCropRotationsMetrics(MeasurementEnum.MetricUnits)({common_crop_rtn_m2: 0})
    ).toEqual({
      rotationArea: {
        value: 0,
        unit: 'ha',
        formattedValue: '0',
      },
    });
  });

  it('Should return null when nil parameters are provided', () => {
    expect(
      makeCommonCropRotationsMetrics(MeasurementEnum.MetricUnits)({
        // @ts-expect-error being extra safe to handle all nil values
        common_crop_rtn_m2: null,
      })
    ).toEqual(null);
    expect(
      makeCommonCropRotationsMetrics(MeasurementEnum.MetricUnits)({
        common_crop_rtn_m2: undefined,
      })
    ).toEqual(null);
    expect(makeCommonCropRotationsMetrics(MeasurementEnum.MetricUnits)({})).toEqual(null);
  });

  it('Should return null when non finite parameters are provided', () => {
    expect(
      makeCommonCropRotationsMetrics(MeasurementEnum.MetricUnits)({
        common_crop_rtn_m2: Infinity,
      })
    ).toEqual(null);
    expect(
      makeCommonCropRotationsMetrics(MeasurementEnum.MetricUnits)({
        common_crop_rtn_m2: NaN,
      })
    ).toEqual(null);
  });
  it('Should return a CropRotationFallowMetric with converted area values (m2 to ac), formattedValue string values, and units given Imperial units system', () => {
    expect(
      makeCommonCropRotationsMetrics(MeasurementEnum.ImperialUnits)(
        CROP_ROTATION_COMMON_ROTATIONS_INPUT_MOCK!
      )
    ).toEqual(CROP_ROTATION_COMMON_ROTATIONS_EXPECTED_OUTPUT_IMPERIAL_MOCK);
  });
  it('Should return a CropRotationFallowMetric with converted area values (m2 to ha), formattedValue string values, and units given Metric units system', () => {
    expect(
      makeCommonCropRotationsMetrics(MeasurementEnum.MetricUnits)(
        CROP_ROTATION_COMMON_ROTATIONS_INPUT_MOCK!
      )
    ).toEqual(CROP_ROTATION_COMMON_ROTATIONS_EXPECTED_OUTPUT_METRIC_MOCK);
  });
});
