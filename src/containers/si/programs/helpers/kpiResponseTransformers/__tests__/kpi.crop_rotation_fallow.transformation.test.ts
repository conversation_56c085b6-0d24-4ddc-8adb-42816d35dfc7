import {MeasurementEnum} from '_common/utils/measurement-unit-options';

import {KPICropRotationFallowMock} from 'containers/si/__mocks__/KPICropRotationFallowMock';
import type {CropRotationFallowMetricData} from 'containers/si/api/apiTypes';
import {
  makeCropRotationFallowMetric,
  private__cropRotationFallowUnit,
  type CropRotationFallowMetric,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.crop_rotation_fallow.transformation';

const topLevelInput = KPICropRotationFallowMock.metric;

const topLevelOutputImperialUnits: CropRotationFallowMetric = {
  noFallowSeasonsArea: {formattedValue: '0', unit: 'ac', value: 0},
  oneFallowSeasonArea: {formattedValue: '25M', unit: 'ac', value: 25369867.78465404},
  threePlusFallowSeasonsArea: {formattedValue: '3.1M', unit: 'ac', value: 3103076.7926715775},
  totalFallowDataArea: {formattedValue: '41M', unit: 'ac', value: 40538463.686452955},
  twoFallowSeasonsArea: {formattedValue: '12M', unit: 'ac', value: 12065519.109127348},
};

const topLevelOutputMetricUnits: CropRotationFallowMetric = {
  noFallowSeasonsArea: {formattedValue: '0', unit: 'ha', value: 0},
  oneFallowSeasonArea: {formattedValue: '10M', unit: 'ha', value: 10266831.173640873},
  threePlusFallowSeasonsArea: {formattedValue: '1.3M', unit: 'ha', value: 1255771.8400280003},
  totalFallowDataArea: {formattedValue: '16M', unit: 'ha', value: 16405350.088554252},
  twoFallowSeasonsArea: {formattedValue: '4.9M', unit: 'ha', value: 4882747.074885379},
};

const baseInput: CropRotationFallowMetricData = {
  nofallow_m2: 500,
  lowfallow_m2: 1000,
  midfallow_m2: 2000,
  highfallow_m2: 3000,
};

const baseOutputMetric: CropRotationFallowMetric = {
  noFallowSeasonsArea: {formattedValue: '0.05', unit: 'ha', value: 0.05},
  oneFallowSeasonArea: {formattedValue: '0.1', unit: 'ha', value: 0.1},
  twoFallowSeasonsArea: {formattedValue: '0.2', unit: 'ha', value: 0.2},
  threePlusFallowSeasonsArea: {formattedValue: '0.3', unit: 'ha', value: 0.3},
  totalFallowDataArea: {formattedValue: '0.65', unit: 'ha', value: 0.65},
};

const baseOutputImperial: CropRotationFallowMetric = {
  noFallowSeasonsArea: {formattedValue: '0.124', unit: 'ac', value: 0.12355257116620752},
  oneFallowSeasonArea: {formattedValue: '0.247', unit: 'ac', value: 0.24710514233241504},
  twoFallowSeasonsArea: {formattedValue: '0.494', unit: 'ac', value: 0.4942102846648301},
  threePlusFallowSeasonsArea: {formattedValue: '0.741', unit: 'ac', value: 0.7413154269972451},
  totalFallowDataArea: {formattedValue: '1.606', unit: 'ac', value: 1.6061834251606977},
};

describe('private__cropRotationFallowUnit', () => {
  it('should return the correct unit given Imperial or Metrics system', () => {
    expect(private__cropRotationFallowUnit(MeasurementEnum.ImperialUnits)).toEqual({
      unit: 'ac',
      unitName: {
        singular: 'acre',
        plural: 'acres',
        abbr: 'ac',
      },
    });
    expect(private__cropRotationFallowUnit(MeasurementEnum.MetricUnits)).toEqual({
      unit: 'ha',
      unitName: {
        singular: 'hectare',
        plural: 'hectares',
        abbr: 'ha',
      },
    });
  });
});

describe('makeCropRotationFallowMetric', () => {
  it('Should return CropRotationFallowMetric given Imperial or Metric units system', () => {
    expect(makeCropRotationFallowMetric(MeasurementEnum.ImperialUnits)(baseInput)).toEqual(
      baseOutputImperial
    );
    expect(makeCropRotationFallowMetric(MeasurementEnum.MetricUnits)(baseInput)).toEqual(
      baseOutputMetric
    );
  });

  it('Should return a CropRotationFallowMetric when a 0 parameter is provided', () => {
    expect(
      makeCropRotationFallowMetric(MeasurementEnum.MetricUnits)({...baseInput, nofallow_m2: 0})
    ).toEqual({
      ...baseOutputMetric,
      noFallowSeasonsArea: {
        value: 0,
        unit: 'ha',
        formattedValue: '0',
      },
      totalFallowDataArea: {
        value: 0.6,
        unit: 'ha',
        formattedValue: '0.6',
      },
    });
  });

  it('Should return null when nil parameters are provided', () => {
    expect(
      makeCropRotationFallowMetric(MeasurementEnum.MetricUnits)({
        ...baseInput,
        // @ts-expect-error being extra safe to handle all nil values
        lowfallow_m2: null,
      })
    ).toEqual(null);
    expect(
      makeCropRotationFallowMetric(MeasurementEnum.MetricUnits)({
        ...baseInput,
        nofallow_m2: undefined,
      })
    ).toEqual(null);
    expect(makeCropRotationFallowMetric(MeasurementEnum.MetricUnits)({})).toEqual(null);
    expect(
      makeCropRotationFallowMetric(MeasurementEnum.MetricUnits)({
        nofallow_m2: 100,
        lowfallow_m2: 200,
        midfallow_m2: 300,
      })
    ).toEqual(null);
  });

  it('Should return null when non finite parameters are provided', () => {
    expect(
      makeCropRotationFallowMetric(MeasurementEnum.MetricUnits)({
        ...baseInput,
        midfallow_m2: Infinity,
      })
    ).toEqual(null);
    expect(
      makeCropRotationFallowMetric(MeasurementEnum.MetricUnits)({
        ...baseInput,
        highfallow_m2: NaN,
      })
    ).toEqual(null);
  });
  it('Should return a CropRotationFallowMetric with converted area values (m2 to ac), formattedValue string values, and units given Imperial units system', () => {
    expect(makeCropRotationFallowMetric(MeasurementEnum.ImperialUnits)(topLevelInput)).toEqual(
      topLevelOutputImperialUnits
    );
  });
  it('Should return a CropRotationFallowMetric with converted area values (m2 to ha), formattedValue string values, and units given Metric units system', () => {
    expect(makeCropRotationFallowMetric(MeasurementEnum.MetricUnits)(topLevelInput)).toEqual(
      topLevelOutputMetricUnits
    );
  });
});
