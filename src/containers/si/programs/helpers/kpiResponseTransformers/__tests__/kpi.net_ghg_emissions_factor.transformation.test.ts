import {
  KPINetGhgEFMock,
  KPINetGhgEFSubsectionSummaryMock,
} from 'containers/si/__mocks__/KPINetGhgEFMock';
import {CO2E} from 'containers/si/constants';
import {
  makeNetGhgEFMetrics,
  private__netGhgEFUnit,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.net_ghg_emissions_factor.transformation';

const topLevelInput = KPINetGhgEFMock.metric;

const subregionInput = KPINetGhgEFSubsectionSummaryMock.boundary_summary[874]!;

describe('private__netGhgEFUnit', () => {
  it('should return the correct net ef unit', () => {
    expect(private__netGhgEFUnit()).toEqual({
      unit: 'kg/kg',
      unitName: {
        singular: `kilogram ${CO2E} / kilogram yield`,
        plural: `kilograms ${CO2E} / kilogram yield`,
        abbr: `kg ${CO2E} / kg yield`,
      },
    });
  });
});

describe('makeNetGhgEFMetrics', () => {
  it('Should return NetGhgEFMetrics with appropriate units', () => {
    expect(
      makeNetGhgEFMetrics({
        net_ghg_emissions_factor: 0.1375152183104355,
        net_ghg_emissions_factor_stderr: 0.001375152183104355,
      })
    ).toEqual({
      netGhgEmissionsPerYield: {
        formattedValue: '0.138',
        unit: 'kg/kg',
        value: 0.1375152183104355,
      },
      netGhgEFStdErr: {
        formattedValue: '0.00138',
        unit: 'kg/kg',
        value: 0.001375152183104355,
      },
    });
  });

  it('Should return NetGhgEFMetrics in appropriate units given 0', () => {
    expect(
      makeNetGhgEFMetrics({
        net_ghg_emissions_factor: 0,
      })
    ).toEqual({
      netGhgEmissionsPerYield: {
        formattedValue: '0',
        unit: 'kg/kg',
        value: 0,
      },
    });
  });

  it('Should not return netGhgEFStdErr when net_ghg_emissions_factor_stderr is 0', () => {
    expect(
      makeNetGhgEFMetrics({
        net_ghg_emissions_factor: 0.1375152183104355,
        net_ghg_emissions_factor_stderr: 0,
      })
    ).toEqual({
      netGhgEmissionsPerYield: {
        formattedValue: '0.138',
        unit: 'kg/kg',
        value: 0.1375152183104355,
      },
    });
  });

  it('Should return null when null parameters are provided', () => {
    expect(
      makeNetGhgEFMetrics({
        //@ts-expect-error covering all nil cases
        net_ghg_emissions_factor: null,
      })
    ).toEqual(null);
    expect(
      makeNetGhgEFMetrics({
        net_ghg_emissions_factor: undefined,
      })
    ).toEqual(null);
    expect(makeNetGhgEFMetrics({})).toEqual(null);
  });

  it('Should not return netGhgEFStdErr when net_ghg_emissions_factor_stderr is null', () => {
    expect(
      makeNetGhgEFMetrics({
        net_ghg_emissions_factor: 0.1375152183104355,
        //@ts-expect-error covering all nil cases
        net_ghg_emissions_factor_stderr: null,
      })
    ).toEqual({
      netGhgEmissionsPerYield: {
        formattedValue: '0.138',
        unit: 'kg/kg',
        value: 0.1375152183104355,
      },
    });
    expect(
      makeNetGhgEFMetrics({
        net_ghg_emissions_factor: 0.1375152183104355,
        net_ghg_emissions_factor_stderr: undefined,
      })
    ).toEqual({
      netGhgEmissionsPerYield: {
        formattedValue: '0.138',
        unit: 'kg/kg',
        value: 0.1375152183104355,
      },
    });
  });

  it('Should return null when non finite parameters are provided', () => {
    expect(
      makeNetGhgEFMetrics({
        net_ghg_emissions_factor: Infinity,
      })
    ).toEqual(null);
  });

  it('Should not return netGhgEFStdErr when net_ghg_emissions_factor_stderr is non finite', () => {
    expect(
      makeNetGhgEFMetrics({
        net_ghg_emissions_factor: 0.1375152183104355,
        net_ghg_emissions_factor_stderr: Infinity,
      })
    ).toEqual({
      netGhgEmissionsPerYield: {
        formattedValue: '0.138',
        unit: 'kg/kg',
        value: 0.1375152183104355,
      },
    });
    expect(
      makeNetGhgEFMetrics({
        net_ghg_emissions_factor: 0.1375152183104355,
        net_ghg_emissions_factor_stderr: NaN,
      })
    ).toEqual({
      netGhgEmissionsPerYield: {
        formattedValue: '0.138',
        unit: 'kg/kg',
        value: 0.1375152183104355,
      },
    });
  });

  it('Should return NetGhgEFMetrics with appropriate units', () => {
    expect(makeNetGhgEFMetrics(topLevelInput)).toEqual({
      netGhgEmissionsPerYield: {
        formattedValue: '0.0657',
        unit: 'kg/kg',
        value: 0.06569877033581449,
      },
      netGhgEFStdErr: {
        formattedValue: '0.00082',
        unit: 'kg/kg',
        value: 0.00082123587919768,
      },
    });

    expect(makeNetGhgEFMetrics(subregionInput)).toEqual({
      netGhgEmissionsPerYield: {
        formattedValue: '0.193',
        unit: 'kg/kg',
        value: 0.19295553945759336,
      },
      netGhgEFStdErr: {
        formattedValue: '0.00241',
        unit: 'kg/kg',
        value: 0.00241194424321992,
      },
    });
  });
});
