import {
  KPIFarmCountMock,
  KPIFarmCountSummarizeBySubsectionMock,
} from 'containers/si/__mocks__/KPIFarmCountMock';
import {
  makeFarmCountMetrics,
  private__farmCountUnit,
  type FarmCountMetrics,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.farm_count.transformation';

const topLevelInput = KPIFarmCountMock.metric;

const topLevelOutput: FarmCountMetrics = {
  farm_count: {
    formattedValue: '1.1M',
    unit: 'count',
    value: 1111749,
  },
};

const subregionInput = KPIFarmCountSummarizeBySubsectionMock.boundary_summary[872]!;

const subregionOutput: FarmCountMetrics = {
  farm_count: {
    formattedValue: '638K',
    unit: 'count',
    value: 638368,
  },
};

const zeroOutput: FarmCountMetrics = {
  farm_count: {
    formattedValue: '0',
    unit: private__farmCountUnit().unit,
    value: 0,
  },
};

describe('farmCountUnit', () => {
  it('should return the correct farm count unit', () => {
    expect(private__farmCountUnit()).toEqual({
      unit: 'count',
      unitName: {
        singular: 'farm',
        plural: 'farms',
        abbr: '',
      },
    });
  });
});

describe('makeFarmCountMetrics', () => {
  it('Should return a FarmCountMetrics independent of Imperial or Metric units system', () => {
    const expected = {
      farm_count: {
        formattedValue: '1.5K',
        unit: 'count',
        value: 1500,
      },
    };
    expect(
      makeFarmCountMetrics({
        farm_count: 1500,
      })
    ).toEqual(expected);
    expect(
      makeFarmCountMetrics({
        farm_count: 1500,
      })
    ).toEqual(expected);
  });

  it('Should return a FarmCountMetrics when a 0 farm_count parameter is provided', () => {
    expect(
      makeFarmCountMetrics({
        farm_count: 0,
      })
    ).toEqual(zeroOutput);
  });

  it('Should return null when null parameters are provided', () => {
    expect(
      makeFarmCountMetrics({
        // @ts-expect-error testing runtime nulls. remove when deserializer is implemented. (https://regrow.atlassian.net/browse/SI-1379)
        farm_count: null,
      })
    ).toEqual(null);
    expect(
      makeFarmCountMetrics({
        farm_count: undefined,
      })
    ).toEqual(null);
    expect(makeFarmCountMetrics({})).toEqual(null);
  });

  it('Should return null when non finite parameters are provided', () => {
    expect(
      makeFarmCountMetrics({
        farm_count: Infinity,
      })
    ).toEqual(null);
  });

  it('Should return a FarmCountMetrics that is independent of the Imperial or Metric units system', () => {
    expect(makeFarmCountMetrics(topLevelInput)).toEqual(topLevelOutput);
  });

  it('Should return a FarmCountMetrics neutral to the Imperial or Metric units system', () => {
    expect(makeFarmCountMetrics(subregionInput)).toEqual(subregionOutput);
  });
});
