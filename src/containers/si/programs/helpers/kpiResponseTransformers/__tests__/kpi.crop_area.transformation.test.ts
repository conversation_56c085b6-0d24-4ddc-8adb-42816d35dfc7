import {MeasurementEnum} from '_common/utils/measurement-unit-options';

import {KPICropAreaMock} from 'containers/si/__mocks__/KPICropAreaMock';
import {
  makeCropAreaMetrics,
  private__cropAreaUnit,
  type CropAreaMetrics,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.crop_area.transformation';

const topLevelInput = KPICropAreaMock.metric;

const topLevelOutputImperialUnits: CropAreaMetrics = {
  crop_area: {
    formattedValue: '153M',
    unit: 'ac',
    value: 152789548.68176255,
  },
};

const topLevelOutputMetricUnits: CropAreaMetrics = {
  crop_area: {
    formattedValue: '62M',
    unit: 'ha',
    value: 61831796.47318887,
  },
};

const zeroValueOutputMetricUnits: CropAreaMetrics = {
  crop_area: {
    formattedValue: '0',
    unit: 'ha',
    value: 0,
  },
};

describe('private__cropAreaUnit', () => {
  it('should return the correct ghg unit given Imperial or Metrics system', () => {
    expect(private__cropAreaUnit(MeasurementEnum.ImperialUnits)).toEqual({
      unit: 'ac',
      unitName: {
        singular: 'acre',
        plural: 'acres',
        abbr: 'ac',
      },
    });
    expect(private__cropAreaUnit(MeasurementEnum.MetricUnits)).toEqual({
      unit: 'ha',
      unitName: {
        singular: 'hectare',
        plural: 'hectares',
        abbr: 'ha',
      },
    });
  });
});

describe('makeCropAreaMetrics', () => {
  it('Should return CropAreaMetrics given Imperial or Metric units system', () => {
    expect(makeCropAreaMetrics(MeasurementEnum.ImperialUnits)({area_m2: 1000})).toEqual({
      crop_area: {
        formattedValue: '0.25',
        unit: 'ac',
        value: 0.24710514233241504,
      },
    });
    expect(makeCropAreaMetrics(MeasurementEnum.MetricUnits)({area_m2: 1000})).toEqual({
      crop_area: {
        formattedValue: '0.1',
        unit: 'ha',
        value: 0.1,
      },
    });
  });

  it('Should return a CropAreaMetrics when a 0 area_m2 parameter is provided', () => {
    expect(makeCropAreaMetrics(MeasurementEnum.MetricUnits)({area_m2: 0})).toEqual(
      zeroValueOutputMetricUnits
    );
  });

  it('Should return null when nil parameters are provided', () => {
    expect(
      makeCropAreaMetrics(MeasurementEnum.MetricUnits)(
        // @ts-expect-error being extra safe to handle all nil values
        {area_m2: null}
      )
    ).toEqual(null);
    expect(makeCropAreaMetrics(MeasurementEnum.MetricUnits)({area_m2: undefined})).toEqual(null);
    expect(makeCropAreaMetrics(MeasurementEnum.MetricUnits)({})).toEqual(null);
  });

  it('Should return null when non finite parameters are provided', () => {
    expect(makeCropAreaMetrics(MeasurementEnum.MetricUnits)({area_m2: Infinity})).toEqual(null);
  });
  it('Should return a CropAreaMetrics with converted area values (m2 to ac), formattedValue string values, and units given Imperial units system', () => {
    expect(makeCropAreaMetrics(MeasurementEnum.ImperialUnits)(topLevelInput)).toEqual(
      topLevelOutputImperialUnits
    );
  });
  it('Should return a CropAreaMetrics with converted area values (m2 to ha), formattedValue string values, and units given Metric units system', () => {
    expect(makeCropAreaMetrics(MeasurementEnum.MetricUnits)(topLevelInput)).toEqual(
      topLevelOutputMetricUnits
    );
  });
});
