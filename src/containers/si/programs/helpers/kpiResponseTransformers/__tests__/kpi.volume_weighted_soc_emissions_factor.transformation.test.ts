import {CO2E} from 'containers/si/constants';
import {
  makeVolumeWeightedSocEFMetrics,
  private__socEFUnit,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.volume_weighted_soc_emissions_factor.transformation';

describe('private__socEFUnit', () => {
  it('should return the correct soc ef unit', () => {
    expect(private__socEFUnit()).toEqual({
      unit: 'kg/kg',
      unitName: {
        singular: `kilogram ${CO2E} / kilogram yield`,
        plural: `kilograms ${CO2E} / kilogram yield`,
        abbr: `kg ${CO2E} / kg yield`,
      },
    });
  });
});

describe('makeVolumeWeightedSocEFMetrics', () => {
  it('Should return VolumeWeightedSocEFMetrics with appropriate units', () => {
    expect(
      makeVolumeWeightedSocEFMetrics({
        volume_weighted_soc_emissions_factor: 0.5408840070399256,
      })
    ).toEqual({
      volumeWeighteSocEmissionsPerYeild: {
        formattedValue: '0.541',
        unit: 'kg/kg',
        value: 0.5408840070399256,
      },
    });
  });

  it('Should return VolumeWeightedSocEFMetrics in appropriate units given 0', () => {
    expect(
      makeVolumeWeightedSocEFMetrics({
        volume_weighted_soc_emissions_factor: 0,
      })
    ).toEqual({
      volumeWeighteSocEmissionsPerYeild: {
        formattedValue: '0',
        unit: 'kg/kg',
        value: 0,
      },
    });
  });

  it('Should return null when null parameters are provided', () => {
    expect(
      makeVolumeWeightedSocEFMetrics({
        //@ts-expect-error covering all nil cases
        volume_weighted_soc_emissions_factor: null,
      })
    ).toEqual(null);
    expect(
      makeVolumeWeightedSocEFMetrics({
        volume_weighted_soc_emissions_factor: undefined,
      })
    ).toEqual(null);
    expect(makeVolumeWeightedSocEFMetrics({})).toEqual(null);
  });

  it('Should return null when non finite parameters are provided', () => {
    expect(
      makeVolumeWeightedSocEFMetrics({
        volume_weighted_soc_emissions_factor: Infinity,
      })
    ).toEqual(null);
  });
});
