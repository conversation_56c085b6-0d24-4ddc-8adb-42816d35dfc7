import {
  KPIFertEFMock,
  KPIFertEFSubsectionSummaryMock,
} from 'containers/si/__mocks__/KPIFertEFMock ';
import {CO2E} from 'containers/si/constants';
import {
  makeFertEFMetrics,
  private__fertEFUnit,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.fert_emissions_factor.transformation';

const topLevelInput = KPIFertEFMock.metric;

const subregionInput = KPIFertEFSubsectionSummaryMock.boundary_summary[874]!;

describe('private__fertEFUnit', () => {
  it('should return the correct ghg ef unit', () => {
    expect(private__fertEFUnit()).toEqual({
      unit: 'kg/kg',
      unitName: {
        singular: `kilogram ${CO2E} / kilogram yield`,
        plural: `kilograms ${CO2E} / kilogram yield`,
        abbr: `kg ${CO2E} / kg yield`,
      },
    });
  });
});

describe('makeFertEFMetrics', () => {
  it('Should return FertEFMetrics with appropriate units', () => {
    expect(
      makeFertEFMetrics({
        fert_emissions_factor: 0.1375152183104355,
      })
    ).toEqual({
      fertEmissionsPerYield: {
        formattedValue: '0.138',
        unit: 'kg/kg',
        value: 0.1375152183104355,
      },
    });
  });

  it('Should return FertEFMetrics in appropriate units given 0', () => {
    expect(
      makeFertEFMetrics({
        fert_emissions_factor: 0,
      })
    ).toEqual({
      fertEmissionsPerYield: {
        formattedValue: '0',
        unit: 'kg/kg',
        value: 0,
      },
    });
  });

  it('Should return null when null parameters are provided', () => {
    expect(
      makeFertEFMetrics({
        //@ts-expect-error covering all nil cases
        fert_emissions_factor: null,
      })
    ).toEqual(null);
    expect(
      makeFertEFMetrics({
        fert_emissions_factor: undefined,
      })
    ).toEqual(null);
    expect(makeFertEFMetrics({})).toEqual(null);
  });

  it('Should return null when non finite parameters are provided', () => {
    expect(
      makeFertEFMetrics({
        fert_emissions_factor: Infinity,
      })
    ).toEqual(null);
  });
});

it('Should return FertEFMetrics with appropriate units', () => {
  expect(makeFertEFMetrics(topLevelInput)).toEqual({
    fertEmissionsPerYield: {
      formattedValue: '0.0484',
      unit: 'kg/kg',
      value: 0.048388065837828904,
    },
  });

  expect(makeFertEFMetrics(subregionInput)).toEqual({
    fertEmissionsPerYield: {
      formattedValue: '0.0517',
      unit: 'kg/kg',
      value: 0.051666766976263045,
    },
  });
});
