import {
  KPILivingRootHealthMock,
  KPILivingRootHealthSubsectionSummaryMock,
} from 'containers/si/__mocks__/KPILivingRootHealthMock';
import {
  getNDVIcolor,
  makeLivingRootHealthMetrics,
  type LivingRootHealthMetrics,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.living_root_health.transformation';

const topLevelInput = KPILivingRootHealthMock.metric;

const topLevelOutput: LivingRootHealthMetrics = {
  Jan: {value: 2.328596834757658, unit: 'ndvi', formattedValue: '2.329'},
  Feb: {value: 2.227703564615117, unit: 'ndvi', formattedValue: '2.228'},
  Mar: {value: 2.5536043748757438, unit: 'ndvi', formattedValue: '2.554'},
  Apr: {value: 2.7539995780119946, unit: 'ndvi', formattedValue: '2.754'},
  May: {value: 3.316055575726038, unit: 'ndvi', formattedValue: '3.316'},
  Jun: {value: 4.93660260713131, unit: 'ndvi', formattedValue: '4.937'},
  Jul: {value: 5.809627701374056, unit: 'ndvi', formattedValue: '5.81'},
  Aug: {value: 5.361883079371136, unit: 'ndvi', formattedValue: '5.362'},
  Sep: {value: 5.035002305516264, unit: 'ndvi', formattedValue: '5.035'},
  Oct: {value: 3.5402809546786753, unit: 'ndvi', formattedValue: '3.54'},
  Nov: {value: 2.989739769340116, unit: 'ndvi', formattedValue: '2.99'},
  Dec: {value: 2.5433016358523943, unit: 'ndvi', formattedValue: '2.543'},
};

const subregionInput = KPILivingRootHealthSubsectionSummaryMock.boundary_summary[872]!;

const subregionOutput: LivingRootHealthMetrics = {
  Jan: {value: 2.2415594070676432, unit: 'ndvi', formattedValue: '2.242'},
  Feb: {value: 2.0617740337944337, unit: 'ndvi', formattedValue: '2.062'},
  Mar: {value: 2.617639125467034, unit: 'ndvi', formattedValue: '2.618'},
  Apr: {value: 2.8546162752898283, unit: 'ndvi', formattedValue: '2.855'},
  May: {value: 3.2190692091118414, unit: 'ndvi', formattedValue: '3.219'},
  Jun: {value: 4.870565953984518, unit: 'ndvi', formattedValue: '4.871'},
  Jul: {value: 6.85067516324656, unit: 'ndvi', formattedValue: '6.851'},
  Aug: {value: 6.945091008366656, unit: 'ndvi', formattedValue: '6.945'},
  Sep: {value: 6.494210519607397, unit: 'ndvi', formattedValue: '6.494'},
  Oct: {value: 3.2853744891489436, unit: 'ndvi', formattedValue: '3.285'},
  Nov: {value: 3.03261419481255, unit: 'ndvi', formattedValue: '3.033'},
  Dec: {value: 2.897029869428117, unit: 'ndvi', formattedValue: '2.897'},
};

describe('makeLivingRootHealthMetrics', () => {
  it('Should return null when only null parameters are provided', () => {
    expect(
      makeLivingRootHealthMetrics({
        living_root_health_1: undefined,
        living_root_health_2: undefined,
        living_root_health_3: undefined,
        living_root_health_4: undefined,
        living_root_health_5: undefined,
        living_root_health_6: undefined,
        living_root_health_7: undefined,
        living_root_health_8: undefined,
        living_root_health_9: undefined,
        living_root_health_10: undefined,
        living_root_health_11: undefined,
        living_root_health_12: undefined,
      })
    ).toEqual(null);
    expect(
      makeLivingRootHealthMetrics({
        //@ts-expect-error checking all nil values
        living_root_health_1: null,
        //@ts-expect-error checking all nil values
        living_root_health_2: null,
        //@ts-expect-error checking all nil values
        living_root_health_3: null,
        //@ts-expect-error checking all nil values
        living_root_health_4: null,
        //@ts-expect-error checking all nil values
        living_root_health_5: null,
        //@ts-expect-error checking all nil values
        living_root_health_6: null,
        //@ts-expect-error checking all nil values
        living_root_health_7: null,
        //@ts-expect-error checking all nil values
        living_root_health_8: null,
        //@ts-expect-error checking all nil values
        living_root_health_9: null,
        //@ts-expect-error checking all nil values
        living_root_health_10: null,
        //@ts-expect-error checking all nil values
        living_root_health_11: null,
        //@ts-expect-error checking all nil values
        living_root_health_12: null,
      })
    ).toEqual(null);
    expect(makeLivingRootHealthMetrics({})).toEqual(null);
  });

  it('Should not return null on partial nil', () => {
    expect(
      makeLivingRootHealthMetrics({
        ...topLevelInput,
        living_root_health_11: undefined,
      })
    ).not.toBeNull();

    expect(
      makeLivingRootHealthMetrics({
        ...topLevelInput,
        // @ts-expect-error checking all nil vals
        living_root_health_2: null,
      })
    ).not.toBeNull();

    expect(
      makeLivingRootHealthMetrics({
        living_root_health_1: 2.328596834757658,
        living_root_health_2: 2.227703564615117,
        living_root_health_3: 2.5536043748757438,
        living_root_health_4: 2.7539995780119946,
      })
    ).not.toBeNull();
  });

  it('Should return null when non finite parameters are provided', () => {
    expect(
      makeLivingRootHealthMetrics({
        ...topLevelInput,
        living_root_health_1: NaN,
      })
    ).toEqual(null);

    expect(
      makeLivingRootHealthMetrics({
        ...topLevelInput,
        living_root_health_12: Infinity,
      })
    ).toEqual(null);
  });

  it('Should return 0 when 0 provided', () => {
    expect(
      makeLivingRootHealthMetrics({
        ...topLevelInput,
        living_root_health_3: 0,
      })
    ).toEqual({
      ...topLevelOutput,
      Mar: {
        unit: 'ndvi',
        value: 0,
        formattedValue: '0',
      },
    });
  });

  it('Should return parital data when one value is undefined', () => {
    expect(
      makeLivingRootHealthMetrics({
        ...topLevelInput,
        living_root_health_10: undefined,
      })
    ).toEqual({
      ...topLevelOutput,
      Oct: null,
    });
  });

  it('Should return a LivingRootHealthMetrics output given KPI response data', () => {
    expect(makeLivingRootHealthMetrics(topLevelInput)).toEqual(topLevelOutput);
    expect(makeLivingRootHealthMetrics(subregionInput)).toEqual(subregionOutput);
  });
});

describe('getNDVIcolor', () => {
  it('should return the correct color for an living root health binned ndvi value', () => {
    expect(getNDVIcolor(topLevelOutput.Jan)).toEqual('6');
    expect(getNDVIcolor(topLevelOutput.Jul)).toEqual('1');
  });
  it('should return null if given null metric', () => {
    expect(getNDVIcolor(null)).toEqual(null);
  });
  it('should return null if given a metric without an ndvi unit', () => {
    expect(getNDVIcolor({value: 1, unit: 'ha', formattedValue: '1'})).toEqual(null);
  });
});
