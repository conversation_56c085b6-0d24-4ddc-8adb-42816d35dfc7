import {KPIVolumeWeightedGhgKgSummarizeByCropTypeMock} from 'containers/si/__mocks__/KPIVolumeWeightedGhgMetricsMock';
import {makeVolumeWeightedGhgMetrics} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.volume_weighted_ghg_kg.transformation';

describe('makeVolumeWeightedGhgMetrics', () => {
  it('Should return VolumeWeightedGhgMetrics for a crop_type_summary with appropriate units', () => {
    expect(
      makeVolumeWeightedGhgMetrics(
        KPIVolumeWeightedGhgKgSummarizeByCropTypeMock.crop_type_summary[1]!
      )
    ).toEqual({volumeWeightedGhgMass: {formattedValue: '0.01', unit: 'mt', value: 0.01}});
    expect(
      makeVolumeWeightedGhgMetrics(
        KPIVolumeWeightedGhgKgSummarizeByCropTypeMock.crop_type_summary[2]!
      )
    ).toEqual({volumeWeightedGhgMass: {formattedValue: '0.02', unit: 'mt', value: 0.02}});
    expect(
      makeVolumeWeightedGhgMetrics(
        KPIVolumeWeightedGhgKgSummarizeByCropTypeMock.crop_type_summary[3]!
      )
    ).toEqual({volumeWeightedGhgMass: {formattedValue: '0.03', unit: 'mt', value: 0.03}});
  });
  it('Should return null when null parameters are provided', () => {
    expect(
      makeVolumeWeightedGhgMetrics({
        //@ts-expect-error covering all nil cases
        volume_weighted_ghg_kg: null,
      })
    ).toEqual(null);
    expect(
      makeVolumeWeightedGhgMetrics({
        volume_weighted_ghg_kg: undefined,
      })
    ).toEqual(null);
    expect(makeVolumeWeightedGhgMetrics({})).toEqual(null);
  });

  it('Should return null when non finite parameters are provided', () => {
    expect(
      makeVolumeWeightedGhgMetrics({
        volume_weighted_ghg_kg: Infinity,
      })
    ).toEqual(null);
  });
});
