import omit from 'lodash/omit';

import {MeasurementEnum} from '_common/utils/measurement-unit-options';

import {KPITillageMock, KPITillageSubsectionMock} from 'containers/si/__mocks__/KPITillageMock';
import type {TillageMetricData} from 'containers/si/api/apiTypes';
import {
  makeTillageExpectedMetrics,
  makeTillageMetrics,
  private__tillageAdoptionUnit,
  private__tillageAreaUnit,
  type TillageMetrics,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.tillage.transformation';

const defaultInput: TillageMetricData = {
  till_conv_area_m2: 10_000_000,
  till_conv_expected_area_m2: 11_000_000,
  till_conv_area_m2_stddev: 125_000,
  till_redu_area_m2: 20_000_000,
  till_redu_expected_area_m2: 21_000_000,
  till_redu_area_m2_stddev: 250_000,
  till_notill_area_m2: 30_000_000,
  till_notill_expected_area_m2: 31_000_000,
  till_notill_area_m2_stddev: 375_000,
  till_unknown_area_m2: 5_000_000,
  till_conserv_expected_area_m2: 52_000_000,
  till_conserv_area_m2_stddev: 95_000,
  till_conserv_adoption_rate: 0.82539682539,
  till_conserv_adoption_rate_stddev: 0.0104166666666666,
};

const KPITillageSubregionInputMock = KPITillageSubsectionMock.boundary_summary[873]!;

describe('tillageAdoptionUnit', () => {
  it('should return the correct tillage adoption unit independent of Imperial or Metrics system', () => {
    expect(private__tillageAdoptionUnit()).toEqual({
      unit: 'unit-interval',
      unitName: {
        singular: '% of cropland area practicing',
        plural: '% of cropland area practicing',
        abbr: 'adoption',
      },
    });
  });
});

describe('tillageAreaUnit', () => {
  it('should return the correct tillage area unit given Imperial or Metrics system', () => {
    expect(private__tillageAreaUnit(MeasurementEnum.ImperialUnits)).toEqual({
      unit: 'ac',
      unitName: {
        singular: 'acre',
        plural: 'acres',
        abbr: 'ac',
      },
    });
    expect(private__tillageAreaUnit(MeasurementEnum.MetricUnits)).toEqual({
      unit: 'ha',
      unitName: {
        singular: 'hectare',
        plural: 'hectares',
        abbr: 'ha',
      },
    });
  });
});

describe('makeTillageMetrics', () => {
  const requiredTillageMetricKeys: Array<keyof TillageMetricData> = [
    'till_conv_area_m2',
    'till_redu_area_m2',
    'till_notill_area_m2',
  ];

  const defaultOutputImperial: TillageMetrics = {
    adoption: {
      formattedValue: '83.3%',
      unit: 'unit-interval',
      value: 0.8333333333333334,
    },
    conservationTillageArea: {
      formattedValue: '12K',
      unit: 'ac',
      value: 12355.257116620753,
    },
    conventionalTillageArea: {
      formattedValue: '2.5K',
      unit: 'ac',
      value: 2471.0514233241506,
    },
    noTillageArea: {
      formattedValue: '7.4K',
      unit: 'ac',
      value: 7413.154269972451,
    },
    reducedTillageArea: {
      formattedValue: '4.9K',
      unit: 'ac',
      value: 4942.102846648301,
    },
    totalTrackedArea: {
      formattedValue: '15K',
      unit: 'ac',
      value: 14826.308539944903,
    },
    unknownArea: {
      formattedValue: '1.2K',
      unit: 'ac',
      value: 1235.5257116620753,
    },
  };

  const defaultOutputMetric: TillageMetrics = {
    adoption: {
      formattedValue: '83.3%',
      unit: 'unit-interval',
      value: 0.8333333333333334,
    },
    conservationTillageArea: {
      formattedValue: '5K',
      unit: 'ha',
      value: 5000,
    },
    conventionalTillageArea: {
      formattedValue: '1K',
      unit: 'ha',
      value: 1000,
    },
    noTillageArea: {
      formattedValue: '3K',
      unit: 'ha',
      value: 3000,
    },
    reducedTillageArea: {
      formattedValue: '2K',
      unit: 'ha',
      value: 2000,
    },
    totalTrackedArea: {
      formattedValue: '6K',
      unit: 'ha',
      value: 6000,
    },
    unknownArea: {
      formattedValue: '500',
      unit: 'ha',
      value: 500,
    },
  };

  const KPITillageTopLevelOutputMetricUnitsMock: TillageMetrics = {
    adoption: {
      formattedValue: '78.4%',
      unit: 'unit-interval',
      value: 0.7841008967594141,
    },
    conservationTillageArea: {
      formattedValue: '44M',
      unit: 'ha',
      value: 43808675.334584996,
    },
    conventionalTillageArea: {
      formattedValue: '12M',
      unit: 'ha',
      value: 12062546.743645614,
    },
    noTillageArea: {
      formattedValue: '14M',
      unit: 'ha',
      value: 13760964.906423412,
    },
    reducedTillageArea: {
      formattedValue: '30M',
      unit: 'ha',
      value: 30047710.428161584,
    },
    totalTrackedArea: {
      formattedValue: '56M',
      unit: 'ha',
      value: 55871222.07823061,
    },
    unknownArea: {
      formattedValue: '12K',
      unit: 'ha',
      value: 12389.069869002999,
    },
  };

  const KPITillageSubregionOutputMetricUnitsMock: TillageMetrics = {
    adoption: {
      formattedValue: '76.2%',
      unit: 'unit-interval',
      value: 0.7615638946173972,
    },
    conservationTillageArea: {
      formattedValue: '1.8M',
      unit: 'ha',
      value: 1811096.8112100055,
    },
    conventionalTillageArea: {
      formattedValue: '567K',
      unit: 'ha',
      value: 567031.7004100003,
    },
    noTillageArea: {
      formattedValue: '1M',
      unit: 'ha',
      value: 1026338.478100005,
    },
    reducedTillageArea: {
      formattedValue: '785K',
      unit: 'ha',
      value: 784758.3331100005,
    },
    totalTrackedArea: {
      formattedValue: '2.4M',
      unit: 'ha',
      value: 2378128.5116200056,
    },
    unknownArea: {
      formattedValue: '6.8K',
      unit: 'ha',
      value: 6809.3276907876,
    },
  };

  it('Should return a TillageMetrics object with converted area values (m2 to ac), formattedValue string values, and units for stdDevs, adoption, conservation tillage field area and total field area given Imperial units system', () => {
    expect(makeTillageMetrics(MeasurementEnum.ImperialUnits)(defaultInput)).toEqual(
      defaultOutputImperial
    );
  });

  it('Should return a TillageMetrics object with converted area values (m2 to ha), formattedValue string values, and units for stdDevs, adoption, conservation tillage field area and total field area given Metric units system', () => {
    expect(makeTillageMetrics(MeasurementEnum.MetricUnits)(defaultInput)).toEqual(
      defaultOutputMetric
    );
  });

  it('Should return null when nil parameters are provided', () => {
    requiredTillageMetricKeys.forEach(key => {
      expect(
        makeTillageMetrics(MeasurementEnum.MetricUnits)({
          ...defaultInput,
          [key]: null,
        })
      ).toEqual(null);
      expect(
        makeTillageMetrics(MeasurementEnum.MetricUnits)({
          ...defaultInput,
          [key]: undefined,
        })
      ).toEqual(null);
      expect(
        makeTillageMetrics(MeasurementEnum.MetricUnits)({
          ...omit(defaultInput, key),
        })
      ).toEqual(null);
    });
    expect(makeTillageMetrics(MeasurementEnum.MetricUnits)({})).toEqual(null);
  });

  it('Should return null when non finite parameters are provided', () => {
    requiredTillageMetricKeys.forEach(key => {
      expect(
        makeTillageMetrics(MeasurementEnum.MetricUnits)({
          ...defaultInput,
          [key]: NaN,
        })
      ).toEqual(null);
      expect(
        makeTillageMetrics(MeasurementEnum.MetricUnits)({
          ...defaultInput,
          [key]: Infinity,
        })
      ).toEqual(null);
    });
  });

  it('Should return null when 0 is provided for till_conv_area_m2, till_redu_area_m2, and till_notill_area_m2', () => {
    expect(
      makeTillageMetrics(MeasurementEnum.MetricUnits)({
        ...defaultInput,
        till_conv_area_m2: 0,
        till_redu_area_m2: 0,
        till_notill_area_m2: 0,
      })
    ).toEqual(null);
  });

  it('Should correctly return 0% adoption if given 0 as conservation tillage values', () => {
    const expectedOutputZeroValues: TillageMetrics = {
      ...defaultOutputMetric,
      adoption: {
        formattedValue: '0%',
        unit: 'unit-interval',
        value: 0,
      },
      conservationTillageArea: {
        formattedValue: '0',
        unit: 'ha',
        value: 0,
      },
      noTillageArea: {
        formattedValue: '0',
        unit: 'ha',
        value: 0,
      },
      reducedTillageArea: {
        formattedValue: '0',
        unit: 'ha',
        value: 0,
      },
      totalTrackedArea: {
        formattedValue: '1K',
        unit: 'ha',
        value: 1000,
      },
    };

    expect(
      makeTillageMetrics(MeasurementEnum.MetricUnits)({
        ...defaultInput,
        till_redu_area_m2: 0,
        till_notill_area_m2: 0,
      })
    ).toEqual(expectedOutputZeroValues);
  });

  it('Should correctly return 100% adoption when 0 is provided as conventional tillage values', () => {
    const expectedOutput100PercentValue: TillageMetrics = {
      adoption: {
        formattedValue: '100%',
        unit: 'unit-interval',
        value: 1,
      },
      conservationTillageArea: {
        formattedValue: '3K',
        unit: 'ha',
        value: 3001,
      },
      conventionalTillageArea: {
        formattedValue: '0',
        unit: 'ha',
        value: 0,
      },
      noTillageArea: {
        formattedValue: '3K',
        unit: 'ha',
        value: 3000,
      },
      reducedTillageArea: {
        formattedValue: '1',
        unit: 'ha',
        value: 1,
      },
      totalTrackedArea: {
        formattedValue: '3K',
        unit: 'ha',
        value: 3001,
      },
      unknownArea: {
        formattedValue: '500',
        unit: 'ha',
        value: 499.67989800000004,
      },
    };
    expect(
      makeTillageMetrics(MeasurementEnum.MetricUnits)({
        till_conv_area_m2: 0,
        till_redu_area_m2: 10_000,
        till_notill_area_m2: 30_000_000,
        till_unknown_area_m2: 4996798.98,
      })
    ).toEqual(expectedOutput100PercentValue);
  });

  it('should not require unknown area to be defined', () => {
    expect(
      makeTillageMetrics(MeasurementEnum.MetricUnits)({
        ...defaultInput,
        till_unknown_area_m2: undefined,
      })
    ).not.toBeNull();
    expect(
      makeTillageMetrics(MeasurementEnum.MetricUnits)({
        ...defaultInput,
        //@ts-expect-error checking all nil vals
        till_unknown_area_m2: null,
      })
    ).not.toBeNull();
    expect(
      makeTillageMetrics(MeasurementEnum.MetricUnits)({
        ...omit(defaultInput, 'till_unknown_area_m2'),
      })
    ).not.toBeNull();
  });

  it('Should return TillageMetrics in hectares units given KPI response data and a Metric units system', () => {
    expect(makeTillageMetrics(MeasurementEnum.MetricUnits)(KPITillageMock.metric)).toEqual(
      KPITillageTopLevelOutputMetricUnitsMock
    );
    expect(makeTillageMetrics(MeasurementEnum.MetricUnits)(KPITillageSubregionInputMock)).toEqual(
      KPITillageSubregionOutputMetricUnitsMock
    );
  });
});

// TODO: SI-3064 clean up duplicate tests depending on expected value roll out
describe('makeTillageExpectedMetrics', () => {
  const requiredTillageMetricKeys: Array<keyof TillageMetricData> = [
    'till_conv_expected_area_m2',
    'till_conv_area_m2_stddev',
    'till_conserv_expected_area_m2',
    'till_conserv_area_m2_stddev',
    'till_redu_expected_area_m2',
    'till_redu_area_m2_stddev',
    'till_notill_expected_area_m2',
    'till_notill_area_m2_stddev',
    'till_conserv_adoption_rate',
    'till_conserv_adoption_rate_stddev',
  ];

  const defaultOutputImperial: TillageMetrics = {
    adoption: {
      formattedValue: '82.5%',
      unit: 'unit-interval',
      value: 0.82539682539,
    },
    adoptionStdDev: {
      formattedValue: '1%',
      unit: 'unit-interval',
      value: 0.0104166666666666,
    },
    conservationTillageArea: {
      formattedValue: '13K',
      unit: 'ac',
      value: 12849.467401285583,
    },
    conservationTillageAreaStdDev: {
      formattedValue: '23',
      unit: 'ac',
      value: 23.47498852157943,
    },
    conventionalTillageArea: {
      formattedValue: '2.7K',
      unit: 'ac',
      value: 2718.156565656566,
    },
    conventionalTillageAreaStdDev: {
      formattedValue: '31',
      unit: 'ac',
      value: 30.888142791551882,
    },
    noTillageArea: {
      formattedValue: '7.7K',
      unit: 'ac',
      value: 7660.259412304867,
    },
    noTillageAreaStdDev: {
      formattedValue: '93',
      unit: 'ac',
      value: 92.66442837465564,
    },
    reducedTillageArea: {
      formattedValue: '5.2K',
      unit: 'ac',
      value: 5189.207988980716,
    },
    reducedTillageAreaStdDev: {
      formattedValue: '62',
      unit: 'ac',
      value: 61.776285583103764,
    },
    totalTrackedArea: {
      formattedValue: '16K',
      unit: 'ac',
      value: 15567.623966942148,
    },
    unknownArea: {
      formattedValue: '1.2K',
      unit: 'ac',
      value: 1235.5257116620753,
    },
  };

  const defaultOutputMetric: TillageMetrics = {
    adoption: {
      formattedValue: '82.5%',
      unit: 'unit-interval',
      value: 0.82539682539,
    },
    adoptionStdDev: {
      formattedValue: '1%',
      unit: 'unit-interval',
      value: 0.0104166666666666,
    },
    conservationTillageArea: {
      formattedValue: '5.2K',
      unit: 'ha',
      value: 5200,
    },
    conservationTillageAreaStdDev: {
      formattedValue: '9.5',
      unit: 'ha',
      value: 9.5,
    },
    conventionalTillageArea: {
      formattedValue: '1.1K',
      unit: 'ha',
      value: 1100,
    },
    conventionalTillageAreaStdDev: {
      formattedValue: '13',
      unit: 'ha',
      value: 12.5,
    },
    noTillageArea: {
      formattedValue: '3.1K',
      unit: 'ha',
      value: 3100,
    },
    noTillageAreaStdDev: {
      formattedValue: '38',
      unit: 'ha',
      value: 37.5,
    },
    reducedTillageArea: {
      formattedValue: '2.1K',
      unit: 'ha',
      value: 2100,
    },
    reducedTillageAreaStdDev: {
      formattedValue: '25',
      unit: 'ha',
      value: 25,
    },
    totalTrackedArea: {
      formattedValue: '6.3K',
      unit: 'ha',
      value: 6300,
    },
    unknownArea: {
      formattedValue: '500',
      unit: 'ha',
      value: 500,
    },
  };

  it('Should return a TillageMetrics object with converted area values (m2 to ac), formattedValue string values, and units for stdDevs, adoption, conservation tillage field area and total field area given Imperial units system', () => {
    expect(makeTillageExpectedMetrics(MeasurementEnum.ImperialUnits)(defaultInput)).toEqual(
      defaultOutputImperial
    );
  });

  it('Should return a TillageMetrics object with converted area values (m2 to ha), formattedValue string values, and units for stdDevs, adoption, conservation tillage field area and total field area given Metric units system', () => {
    expect(makeTillageExpectedMetrics(MeasurementEnum.MetricUnits)(defaultInput)).toEqual(
      defaultOutputMetric
    );
  });

  it('Should return null when nil parameters are provided', () => {
    requiredTillageMetricKeys.forEach(key => {
      expect(
        makeTillageExpectedMetrics(MeasurementEnum.MetricUnits)({
          ...defaultInput,
          [key]: null,
        })
      ).toEqual(null);
      expect(
        makeTillageExpectedMetrics(MeasurementEnum.MetricUnits)({
          ...defaultInput,
          [key]: undefined,
        })
      ).toEqual(null);
      expect(
        makeTillageExpectedMetrics(MeasurementEnum.MetricUnits)({
          ...omit(defaultInput, key),
        })
      ).toEqual(null);
    });
    expect(makeTillageExpectedMetrics(MeasurementEnum.MetricUnits)({})).toEqual(null);
  });

  it('Should return null when non finite parameters are provided', () => {
    requiredTillageMetricKeys.forEach(key => {
      expect(
        makeTillageExpectedMetrics(MeasurementEnum.MetricUnits)({
          ...defaultInput,
          [key]: NaN,
        })
      ).toEqual(null);
      expect(
        makeTillageExpectedMetrics(MeasurementEnum.MetricUnits)({
          ...defaultInput,
          [key]: Infinity,
        })
      ).toEqual(null);
    });
  });

  it('Should return null when 0 is provided for till_conv_area_m2, till_redu_area_m2, and till_notill_area_m2', () => {
    expect(
      makeTillageExpectedMetrics(MeasurementEnum.MetricUnits)({
        ...defaultInput,
        till_conv_expected_area_m2: 0,
        till_redu_expected_area_m2: 0,
        till_notill_expected_area_m2: 0,
      })
    ).toEqual(null);
  });

  it('should not require unknown area to be defined', () => {
    expect(
      makeTillageExpectedMetrics(MeasurementEnum.MetricUnits)({
        ...defaultInput,
        till_unknown_area_m2: undefined,
      })
    ).not.toBeNull();
    expect(
      makeTillageExpectedMetrics(MeasurementEnum.MetricUnits)({
        ...defaultInput,
        //@ts-expect-error checking all nil vals
        till_unknown_area_m2: null,
      })
    ).not.toBeNull();
    expect(
      makeTillageExpectedMetrics(MeasurementEnum.MetricUnits)({
        ...omit(defaultInput, 'till_unknown_area_m2'),
      })
    ).not.toBeNull();
  });

  // TODO: SI-3064 udpate depending on expected value roll out and KPITillageMock updates
  it.todo(
    'Should return a TillageMetric in acres units given KPI response data and an Imperial units system'
  );

  it.todo(
    'Should return a TillageMetric in hectares units given KPI response data and a Metric units system'
  );
});
