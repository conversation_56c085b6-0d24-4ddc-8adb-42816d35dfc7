import {MeasurementEnum} from '_common/utils/measurement-unit-options';

import {KPIG2cDlucBoundarySummaryKpiMock} from 'containers/si/__mocks__/KPIG2CDlucMock';
import {
  makeG2cDlucMetrics,
  private__g2cDlucAreaUnit,
  type G2cDlucMetrics,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.grassland_to_cropland_dluc.transformation';

const topLevelInput = KPIG2cDlucBoundarySummaryKpiMock.metric;

const topLevelOutputImperialUnits: G2cDlucMetrics = {
  convertedArea: {
    formattedValue: '13.8M',
    unit: 'ac',
    value: 13767833.594167491,
  },
};

const topLevelOutputMetricUnits: G2cDlucMetrics = {
  convertedArea: {
    formattedValue: '5.6M',
    unit: 'ha',
    value: 5571649.97223995,
  },
};

const subregionInput = KPIG2cDlucBoundarySummaryKpiMock.boundary_summary?.[872];

const subregionOutputImperialUnits: G2cDlucMetrics = {
  convertedArea: {
    formattedValue: '2.7M',
    unit: 'ac',
    value: 2687804.6129939854,
  },
};

const subregionOutputMetricUnits: G2cDlucMetrics = {
  convertedArea: {
    formattedValue: '1.1M',
    unit: 'ha',
    value: 1087716.9886566952,
  },
};

const zeroValueOutputMetricUnits: G2cDlucMetrics = {
  convertedArea: {
    formattedValue: '0',
    unit: 'ha',
    value: 0,
  },
};

describe('g2cDlucAreaUnit', () => {
  it('should return the correct grassland to cropland area unit given Imperial or Metrics system', () => {
    expect(private__g2cDlucAreaUnit(MeasurementEnum.ImperialUnits)).toEqual({
      unit: 'ac',
      unitName: {
        singular: 'acre',
        plural: 'acres',
        abbr: 'ac',
      },
    });
    expect(private__g2cDlucAreaUnit(MeasurementEnum.MetricUnits)).toEqual({
      unit: 'ha',
      unitName: {
        singular: 'hectare',
        plural: 'hectares',
        abbr: 'ha',
      },
    });
  });
});

describe('makeG2cDlucMetrics', () => {
  it('Should return a G2cDlucMetric in ac given Imperial units system', () => {
    const expected: G2cDlucMetrics = {
      convertedArea: {
        formattedValue: '2.5K',
        unit: 'ac',
        value: 2471.0514233241506,
      },
    };
    expect(
      makeG2cDlucMetrics(MeasurementEnum.ImperialUnits)({
        g2c_dluc_conversion_area_m2: 10000000,
      })
    ).toEqual(expected);
  });

  it('Should return a G2cDlucMetric in ha given Metric units system', () => {
    const expected: G2cDlucMetrics = {
      convertedArea: {
        formattedValue: '1K',
        unit: 'ha',
        value: 1000,
      },
    };

    expect(
      makeG2cDlucMetrics(MeasurementEnum.MetricUnits)({
        g2c_dluc_conversion_area_m2: 10000000,
      })
    ).toEqual(expected);
  });

  it('Should return null when null parameters are provided', () => {
    expect(
      makeG2cDlucMetrics(MeasurementEnum.MetricUnits)({
        g2c_dluc_conversion_area_m2: undefined,
      })
    ).toEqual(null);
    expect(
      makeG2cDlucMetrics(MeasurementEnum.MetricUnits)({
        //@ts-expect-error checking all nil values
        g2c_dluc_conversion_area_m2: null,
      })
    ).toEqual(null);
    expect(makeG2cDlucMetrics(MeasurementEnum.MetricUnits)({})).toEqual(null);
  });

  it('Should return null when non finite parameters are provided', () => {
    expect(
      makeG2cDlucMetrics(MeasurementEnum.MetricUnits)({
        g2c_dluc_conversion_area_m2: NaN,
      })
    ).toEqual(null);

    expect(makeG2cDlucMetrics(MeasurementEnum.MetricUnits)(undefined)).toEqual(null);
    expect(
      makeG2cDlucMetrics(MeasurementEnum.MetricUnits)({
        g2c_dluc_conversion_area_m2: Infinity,
      })
    ).toEqual(null);
  });

  it('Should return 0 when 0 provided', () => {
    expect(
      makeG2cDlucMetrics(MeasurementEnum.MetricUnits)({
        g2c_dluc_conversion_area_m2: 0,
      })
    ).toEqual(zeroValueOutputMetricUnits);
  });

  it('Should return a G2cDlucMetric in acres units given KPI response data and an Imperial units system', () => {
    expect(makeG2cDlucMetrics(MeasurementEnum.ImperialUnits)(topLevelInput)).toEqual(
      topLevelOutputImperialUnits
    );
    expect(
      makeG2cDlucMetrics(MeasurementEnum.ImperialUnits)({
        ...subregionInput,
      })
    ).toEqual(subregionOutputImperialUnits);
  });

  it('Should return a G2cDlucMetric in hectares units given KPI response data and a Metric units system', () => {
    expect(makeG2cDlucMetrics(MeasurementEnum.MetricUnits)(topLevelInput)).toEqual(
      topLevelOutputMetricUnits
    );
    expect(
      makeG2cDlucMetrics(MeasurementEnum.MetricUnits)({
        ...subregionInput,
      })
    ).toEqual(subregionOutputMetricUnits);
  });
});
