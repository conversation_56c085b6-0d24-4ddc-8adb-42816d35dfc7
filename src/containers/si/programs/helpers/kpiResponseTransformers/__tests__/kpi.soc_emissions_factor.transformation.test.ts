import {KPISocEFMock, KPISocEFSubsectionSummaryMock} from 'containers/si/__mocks__/KPISocEFMock';
import {CO2E} from 'containers/si/constants';
import {
  makeSocEFMetrics,
  private__socEFUnit,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.soc_emissions_factor.transformation';

const topLevelInput = KPISocEFMock.metric;

const subregionInput = KPISocEFSubsectionSummaryMock.boundary_summary[874]!;

describe('private__socEFUnit', () => {
  it('should return the correct soc ef unit', () => {
    expect(private__socEFUnit()).toEqual({
      unit: 'kg/kg',
      unitName: {
        singular: `kilogram ${CO2E} / kilogram yield`,
        plural: `kilograms ${CO2E} / kilogram yield`,
        abbr: `kg ${CO2E} / kg yield`,
      },
    });
  });
});

describe('makeSocEFMetrics', () => {
  it('Should return SocEFMetrics with appropriate units', () => {
    expect(
      makeSocEFMetrics({
        soc_emissions_factor: 0.1375152183104355,
        soc_emissions_factor_stderr: 0.002781087793804042,
      })
    ).toEqual({
      socEmissionsPerYield: {
        formattedValue: '0.138',
        unit: 'kg/kg',
        value: 0.1375152183104355,
      },
      socEFStdErr: {
        formattedValue: '0.00278',
        unit: 'kg/kg',
        value: 0.002781087793804042,
      },
    });
  });

  it('Should return SocEFMetrics in appropriate units given 0', () => {
    expect(
      makeSocEFMetrics({
        soc_emissions_factor: 0,
      })
    ).toEqual({
      socEmissionsPerYield: {
        formattedValue: '0',
        unit: 'kg/kg',
        value: 0,
      },
    });
  });

  it('Should return no socEFStdErr when 0 is provided', () => {
    expect(
      makeSocEFMetrics({
        soc_emissions_factor: 0.1375152183104355,
        soc_emissions_factor_stderr: 0,
      })
    ).toEqual({
      socEmissionsPerYield: {
        formattedValue: '0.138',
        unit: 'kg/kg',
        value: 0.1375152183104355,
      },
    });
  });

  it('Should return null when null parameters are provided', () => {
    expect(
      makeSocEFMetrics({
        //@ts-expect-error covering all nil cases
        soc_emissions_factor: null,
      })
    ).toEqual(null);
    expect(
      makeSocEFMetrics({
        soc_emissions_factor: undefined,
      })
    ).toEqual(null);
    expect(makeSocEFMetrics({})).toEqual(null);
  });

  it('Should return null when non finite parameters are provided', () => {
    expect(
      makeSocEFMetrics({
        soc_emissions_factor: Infinity,
      })
    ).toEqual(null);
  });

  it('Should return undefined for socEFStdErr when non finite parameter is provided', () => {
    expect(
      makeSocEFMetrics({
        soc_emissions_factor: 0.1375152183104355,
        soc_emissions_factor_stderr: Infinity,
      })
    ).toEqual({
      socEmissionsPerYield: {
        formattedValue: '0.138',
        unit: 'kg/kg',
        value: 0.1375152183104355,
      },
    });

    expect(
      makeSocEFMetrics({
        soc_emissions_factor: 0.1375152183104355,
        soc_emissions_factor_stderr: NaN,
      })
    ).toEqual({
      socEmissionsPerYield: {
        formattedValue: '0.138',
        unit: 'kg/kg',
        value: 0.1375152183104355,
      },
    });
  });

  it('Should return undefined for socEFStdErr when null or undefined parameter is provided', () => {
    expect(
      makeSocEFMetrics({
        soc_emissions_factor: 0.1375152183104355,
        //@ts-expect-error covering all nil cases
        soc_emissions_factor_stderr: null,
      })
    ).toEqual({
      socEmissionsPerYield: {
        formattedValue: '0.138',
        unit: 'kg/kg',
        value: 0.1375152183104355,
      },
    });

    expect(
      makeSocEFMetrics({
        soc_emissions_factor: 0.1375152183104355,
        soc_emissions_factor_stderr: undefined,
      })
    ).toEqual({
      socEmissionsPerYield: {
        formattedValue: '0.138',
        unit: 'kg/kg',
        value: 0.1375152183104355,
      },
    });
  });

  it('Should return SocEFMetrics with appropriate units', () => {
    expect(makeSocEFMetrics(topLevelInput)).toEqual({
      socEmissionsPerYield: {
        formattedValue: '0.222',
        unit: 'kg/kg',
        value: 0.22248702350432337,
      },
      socEFStdErr: {
        formattedValue: '0.00278',
        unit: 'kg/kg',
        value: 0.002781087793804042,
      },
    });

    expect(makeSocEFMetrics(subregionInput)).toEqual({
      socEmissionsPerYield: {
        formattedValue: '0.254',
        unit: 'kg/kg',
        value: 0.25420684736109034,
      },
      socEFStdErr: {
        formattedValue: '0.00318',
        unit: 'kg/kg',
        value: 0.003177610592013629,
      },
    });
  });
});
