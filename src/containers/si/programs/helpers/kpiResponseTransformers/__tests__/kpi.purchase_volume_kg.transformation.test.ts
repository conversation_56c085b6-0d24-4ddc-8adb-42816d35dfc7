import {KPIPurchaseVolumeKgSummarizeByCropTypeMock} from 'containers/si/__mocks__/KPIPurchaseVolumeKgMetricsMock';
import {
  makePurchaseVolumeMetrics,
  private_volumePurchasedUnit,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.purchase_volume_kg.transformation';

describe('private_volumePurchasedUnit', () => {
  it('should return the correct cover crop adoption unit independent of Imperial or Metrics system', () => {
    expect(private_volumePurchasedUnit()).toEqual({
      unit: 'mt',
      unitName: {
        abbr: 'mt purchased',
        plural: 'metric tonnes purchased',
        singular: 'metric tonne purchased',
      },
    });
  });
});

describe('makePurchaseVolumeMetrics', () => {
  it('Should reture PurchaseVolumeMetrics for a crop_type_summary with appropriate units', () => {
    expect(
      makePurchaseVolumeMetrics({
        purchase_volume_kg: 6500.0,
      })
    ).toEqual({volumePurchased: {formattedValue: '6.5', unit: 'mt', value: 6.5}});
    expect(
      makePurchaseVolumeMetrics(KPIPurchaseVolumeKgSummarizeByCropTypeMock.crop_type_summary[1]!)
    ).toEqual({volumePurchased: {formattedValue: '8', unit: 'mt', value: 8}});
    expect(
      makePurchaseVolumeMetrics(KPIPurchaseVolumeKgSummarizeByCropTypeMock.crop_type_summary[5]!)
    ).toEqual({volumePurchased: {formattedValue: '7', unit: 'mt', value: 7}});
  });
  it('Should return null when null parameters are provided', () => {
    expect(
      makePurchaseVolumeMetrics({
        purchase_volume_kg: undefined,
      })
    ).toEqual(null);
    expect(makePurchaseVolumeMetrics({})).toEqual(null);
  });

  it('Should return null when non finite parameters are provided', () => {
    expect(
      makePurchaseVolumeMetrics({
        purchase_volume_kg: Infinity,
      })
    ).toEqual(null);
  });
});
