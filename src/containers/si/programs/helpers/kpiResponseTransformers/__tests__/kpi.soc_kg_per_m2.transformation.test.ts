import pick from 'lodash/pick';

import {MeasurementEnum} from '_common/utils/measurement-unit-options';

import {KPISOCMock, KPISOCSummarizeBySubsectionMock} from 'containers/si/__mocks__/KPISOCMock';
import type {SocKgPerM2MetricData} from 'containers/si/api/apiTypes';
import {CO2E} from 'containers/si/constants';
import {
  makeSocMetrics,
  private__socUnit,
  type SocMetrics,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.soc_kg_per_m2.transformation';

const topLevelInput = KPISOCMock.metric;

const subregionInput = KPISOCSummarizeBySubsectionMock.boundary_summary[872]!;

describe('private__socUnit', () => {
  it('should return the correct soc unit given Imperial or Metrics system', () => {
    expect(private__socUnit(MeasurementEnum.ImperialUnits)).toEqual({
      unit: 'mt/ac',
      unitName: {
        singular: `metric tonne ${CO2E} / acre`,
        plural: `metric tonnes ${CO2E} / acre`,
        abbr: `mt ${CO2E} / ac`,
      },
    });
    expect(private__socUnit(MeasurementEnum.MetricUnits)).toEqual({
      unit: 'mt/ha',
      unitName: {
        singular: `metric tonne ${CO2E} / hectare`,
        plural: `metric tonnes ${CO2E} / hectare`,
        abbr: `mt ${CO2E} / ha`,
      },
    });
  });
});

describe('makeSOCMetrics', () => {
  const input: SocKgPerM2MetricData = {
    soc_kg_per_m2: 0.1,
    soc_kg_per_m2_stderr: 0.01,
  };
  const expectedImperial: SocMetrics = {
    dSocMassPerArea: {
      formattedValue: '0.405',
      unit: 'mt/ac',
      value: 0.40468603387248114,
    },
    dSocStdErr: {
      formattedValue: '0.0405',
      unit: 'mt/ac',
      value: 0.040468603387248114,
    },
  };
  const expectedMetric: SocMetrics = {
    dSocMassPerArea: {
      formattedValue: '1',
      unit: 'mt/ha',
      value: 1,
    },
    dSocStdErr: {
      formattedValue: '0.1',
      unit: 'mt/ha',
      value: 0.1,
    },
  };
  it('Should return SOCMetrics with stderr with appropriate units given Imperial or Metric units system', () => {
    expect(makeSocMetrics(MeasurementEnum.ImperialUnits)(input)).toEqual(expectedImperial);
    expect(
      makeSocMetrics(MeasurementEnum.MetricUnits)({
        soc_kg_per_m2: 0.1,
        soc_kg_per_m2_stderr: 0.01,
      })
    ).toEqual(expectedMetric);
  });

  it('Should return SocMetrics without stderr if invalid/no stderr is provided', () => {
    const noStdErrInput: SocKgPerM2MetricData = pick(input, ['soc_kg_per_m2']);
    const noStdErrExpected: SocMetrics = pick(expectedMetric, ['dSocMassPerArea']);
    expect(makeSocMetrics(MeasurementEnum.MetricUnits)(noStdErrInput)).toEqual(noStdErrExpected);
    expect(
      //@ts-expect-error covering all nil cases
      makeSocMetrics(MeasurementEnum.MetricUnits)({...noStdErrInput, soc_kg_per_m2_stderr: null})
    ).toEqual(noStdErrExpected);
    expect(
      makeSocMetrics(MeasurementEnum.MetricUnits)({...noStdErrInput, soc_kg_per_m2_stderr: NaN})
    ).toEqual(noStdErrExpected);
    expect(
      makeSocMetrics(MeasurementEnum.MetricUnits)({...noStdErrInput, soc_kg_per_m2_stderr: 0})
    ).toEqual(noStdErrExpected);
  });

  it('Should return SOCMetrics in hectares given 0 and a Metric units system', () => {
    expect(
      makeSocMetrics(MeasurementEnum.MetricUnits)({
        soc_kg_per_m2: 0,
      })
    ).toEqual({
      dSocMassPerArea: {
        formattedValue: '0',
        unit: 'mt/ha',
        value: 0,
      },
    });
  });

  it('Should return null when null parameters are provided', () => {
    expect(
      makeSocMetrics(MeasurementEnum.MetricUnits)({
        //@ts-expect-error checking all nil values
        soc_kg_per_m2: null,
      })
    ).toEqual(null);
    expect(
      makeSocMetrics(MeasurementEnum.MetricUnits)({
        soc_kg_per_m2: undefined,
      })
    ).toEqual(null);
    expect(makeSocMetrics(MeasurementEnum.MetricUnits)({})).toEqual(null);
  });

  it('Should return null when non finite parameters are provided', () => {
    expect(
      makeSocMetrics(MeasurementEnum.MetricUnits)({
        soc_kg_per_m2: Infinity,
      })
    ).toEqual(null);
  });
  it('Should return a SOCMetrics with appropriate units given Imperial or Metric units system', () => {
    const expectedTopLevelImperial: SocMetrics = {
      dSocMassPerArea: {
        formattedValue: '0.287',
        unit: 'mt/ac',
        value: 0.28735158152529483,
      },
      dSocStdErr: {
        formattedValue: '0.00327',
        unit: 'mt/ac',
        value: 0.003265358876726837,
      },
    };
    expect(makeSocMetrics(MeasurementEnum.ImperialUnits)(topLevelInput)).toEqual(
      expectedTopLevelImperial
    );
    const expectedTopLevelMetric: SocMetrics = {
      dSocMassPerArea: {
        formattedValue: '0.71',
        unit: 'mt/ha',
        value: 0.7100605345225255,
      },
      dSocStdErr: {formattedValue: '0.00807', unit: 'mt/ha', value: 0.0080688697},
    };
    expect(makeSocMetrics(MeasurementEnum.MetricUnits)(topLevelInput)).toEqual(
      expectedTopLevelMetric
    );

    const expectedSubregionImperial: SocMetrics = {
      dSocMassPerArea: {
        formattedValue: '0.347',
        unit: 'mt/ac',
        value: 0.34660668256590593,
      },
      dSocStdErr: {
        formattedValue: '0.00394',
        unit: 'mt/ac',
        value: 0.00393871228584435,
      },
    };
    expect(makeSocMetrics(MeasurementEnum.ImperialUnits)(subregionInput)).toEqual(
      expectedSubregionImperial
    );

    const expectedSubregionMetric: SocMetrics = {
      dSocMassPerArea: {
        formattedValue: '0.856',
        unit: 'mt/ha',
        value: 0.8564829362881439,
      },
      dSocStdErr: {
        formattedValue: '0.00973',
        unit: 'mt/ha',
        value: 0.0097327606,
      },
    };
    expect(makeSocMetrics(MeasurementEnum.MetricUnits)(subregionInput)).toEqual(
      expectedSubregionMetric
    );
  });
});
