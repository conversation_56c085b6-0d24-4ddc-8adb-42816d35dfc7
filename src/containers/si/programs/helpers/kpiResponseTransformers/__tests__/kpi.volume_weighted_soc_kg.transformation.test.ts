import {KPIVolumeWeightedSocKgSummarizeByCropTypeMock} from 'containers/si/__mocks__/KPIVolumeWeightedSocMetricsMock';
import {makeVolumeWeightedSocMetrics} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.volume_weighted_soc_kg.transformation';

describe('makeVolumeWeightedSocMetrics', () => {
  it('Should return VolumeWeightedSocMetrics for a crop_type_summary with appropriate units', () => {
    expect(
      makeVolumeWeightedSocMetrics(
        KPIVolumeWeightedSocKgSummarizeByCropTypeMock.crop_type_summary[1]!
      )
    ).toEqual({volumeWeightedSocMass: {formattedValue: '0.01', unit: 'mt', value: 0.01}});
    expect(
      makeVolumeWeightedSocMetrics(
        KPIVolumeWeightedSocKgSummarizeByCropTypeMock.crop_type_summary[2]!
      )
    ).toEqual({volumeWeightedSocMass: {formattedValue: '0.02', unit: 'mt', value: 0.02}});
    expect(
      makeVolumeWeightedSocMetrics(
        KPIVolumeWeightedSocKgSummarizeByCropTypeMock.crop_type_summary[3]!
      )
    ).toEqual({volumeWeightedSocMass: {formattedValue: '0.03', unit: 'mt', value: 0.03}});
  });
  it('Should return null when null parameters are provided', () => {
    expect(
      makeVolumeWeightedSocMetrics({
        //@ts-expect-error covering all nil cases
        volume_weighted_soc_kg: null,
      })
    ).toEqual(null);
    expect(
      makeVolumeWeightedSocMetrics({
        volume_weighted_soc_kg: undefined,
      })
    ).toEqual(null);
    expect(makeVolumeWeightedSocMetrics({})).toEqual(null);
  });

  it('Should return null when non finite parameters are provided', () => {
    expect(
      makeVolumeWeightedSocMetrics({
        volume_weighted_soc_kg: Infinity,
      })
    ).toEqual(null);
  });
});
