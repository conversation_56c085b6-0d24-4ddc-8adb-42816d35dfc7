import {
  KPILivingRootConfidenceMock,
  KPILivingRootConfidenceSubsectionSummaryMock,
} from 'containers/si/__mocks__/KPILivingRootConfidenceMock';
import {
  makeLivingRootConfidenceMetrics,
  type LivingRootConfidenceMetrics,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.living_root_confidence.transformation';

const topLevelInput = KPILivingRootConfidenceMock.metric;

const topLevelOutput: LivingRootConfidenceMetrics = {
  Jan: {
    value: 0.8427756468625378,
    unit: 'unit-interval',
    formattedValue: '84.3%',
  },
  Feb: {
    value: 0.8469657902224662,
    unit: 'unit-interval',
    formattedValue: '84.7%',
  },
  Mar: {
    value: 0.9649622263696778,
    unit: 'unit-interval',
    formattedValue: '96.5%',
  },
  Apr: {
    value: 0.9163957471517936,
    unit: 'unit-interval',
    formattedValue: '91.6%',
  },
  May: {
    value: 0.885849767909106,
    unit: 'unit-interval',
    formattedValue: '88.6%',
  },
  Jun: {
    value: 0.9592630240708853,
    unit: 'unit-interval',
    formattedValue: '95.9%',
  },
  Jul: {
    value: 0.9785083644502065,
    unit: 'unit-interval',
    formattedValue: '97.9%',
  },
  Aug: {
    value: 0.9888983359610314,
    unit: 'unit-interval',
    formattedValue: '98.9%',
  },
  Sep: {
    value: 0.9889309870336647,
    unit: 'unit-interval',
    formattedValue: '98.9%',
  },
  Oct: {
    value: 0.9648912806872997,
    unit: 'unit-interval',
    formattedValue: '96.5%',
  },
  Nov: {
    value: 0.8937492711412445,
    unit: 'unit-interval',
    formattedValue: '89.4%',
  },
  Dec: {
    value: 0.8807533718097404,
    unit: 'unit-interval',
    formattedValue: '88.1%',
  },
};

const subregionInput = KPILivingRootConfidenceSubsectionSummaryMock.boundary_summary[872]!;

const subregionOutput: LivingRootConfidenceMetrics = {
  Jan: {
    value: 0.830552103306376,
    unit: 'unit-interval',
    formattedValue: '83.1%',
  },
  Feb: {
    value: 0.9019821317296861,
    unit: 'unit-interval',
    formattedValue: '90.2%',
  },
  Mar: {
    value: 0.9675330083246593,
    unit: 'unit-interval',
    formattedValue: '96.8%',
  },
  Apr: {
    value: 0.9011119674568202,
    unit: 'unit-interval',
    formattedValue: '90.1%',
  },
  May: {
    value: 0.8753028273181961,
    unit: 'unit-interval',
    formattedValue: '87.5%',
  },
  Jun: {
    value: 0.914487803653173,
    unit: 'unit-interval',
    formattedValue: '91.4%',
  },
  Jul: {
    value: 0.9522028510755093,
    unit: 'unit-interval',
    formattedValue: '95.2%',
  },
  Aug: {
    value: 0.9752872601900362,
    unit: 'unit-interval',
    formattedValue: '97.5%',
  },
  Sep: {
    value: 0.9966311447131386,
    unit: 'unit-interval',
    formattedValue: '99.7%',
  },
  Oct: {
    value: 0.900302612712768,
    unit: 'unit-interval',
    formattedValue: '90%',
  },
  Nov: {
    value: 0.8904931801950793,
    unit: 'unit-interval',
    formattedValue: '89%',
  },
  Dec: {
    value: 0.8724331270720107,
    unit: 'unit-interval',
    formattedValue: '87.2%',
  },
};

describe('makeLivingRootConfidenceMetrics', () => {
  it('Should return null when only null parameters are provided', () => {
    expect(
      makeLivingRootConfidenceMetrics({
        living_root_confidence_1: undefined,
        living_root_confidence_2: undefined,
        living_root_confidence_3: undefined,
        living_root_confidence_4: undefined,
        living_root_confidence_5: undefined,
        living_root_confidence_6: undefined,
        living_root_confidence_7: undefined,
        living_root_confidence_8: undefined,
        living_root_confidence_9: undefined,
        living_root_confidence_10: undefined,
        living_root_confidence_11: undefined,
        living_root_confidence_12: undefined,
      })
    ).toEqual(null);
    expect(
      makeLivingRootConfidenceMetrics({
        //@ts-expect-error checking all nil values
        living_root_confidence_1: null,
        //@ts-expect-error checking all nil values
        living_root_confidence_2: null,
        //@ts-expect-error checking all nil values
        living_root_confidence_3: null,
        //@ts-expect-error checking all nil values
        living_root_confidence_4: null,
        //@ts-expect-error checking all nil values
        living_root_confidence_5: null,
        //@ts-expect-error checking all nil values
        living_root_confidence_6: null,
        //@ts-expect-error checking all nil values
        living_root_confidence_7: null,
        //@ts-expect-error checking all nil values
        living_root_confidence_8: null,
        //@ts-expect-error checking all nil values
        living_root_confidence_9: null,
        //@ts-expect-error checking all nil values
        living_root_confidence_10: null,
        //@ts-expect-error checking all nil values
        living_root_confidence_11: null,
        //@ts-expect-error checking all nil values
        living_root_confidence_12: null,
      })
    ).toEqual(null);
    expect(makeLivingRootConfidenceMetrics({})).toEqual(null);
  });

  it('Should not return null on partial nil', () => {
    expect(
      makeLivingRootConfidenceMetrics({
        ...topLevelInput,
        living_root_confidence_11: undefined,
      })
    ).not.toBeNull();

    expect(
      makeLivingRootConfidenceMetrics({
        ...topLevelInput,
        // @ts-expect-error checking all nil vals
        living_root_confidence_2: null,
      })
    ).not.toBeNull();

    expect(
      makeLivingRootConfidenceMetrics({
        living_root_confidence_1: 2.328596834757658,
        living_root_confidence_2: 2.227703564615117,
        living_root_confidence_3: 2.5536043748757438,
        living_root_confidence_4: 2.7539995780119946,
      })
    ).not.toBeNull();
  });

  it('Should return null when non finite parameters are provided', () => {
    expect(
      makeLivingRootConfidenceMetrics({
        ...topLevelInput,
        living_root_confidence_1: NaN,
      })
    ).toEqual(null);

    expect(
      makeLivingRootConfidenceMetrics({
        ...topLevelInput,
        living_root_confidence_12: Infinity,
      })
    ).toEqual(null);
  });

  it('Should return 0 when 0 provided', () => {
    expect(
      makeLivingRootConfidenceMetrics({
        ...topLevelInput,
        living_root_confidence_3: 0,
      })
    ).toEqual({
      ...topLevelOutput,
      Mar: {
        unit: 'unit-interval',
        value: 0,
        formattedValue: '0%',
      },
    });
  });

  it('Should return parital data when one value is undefined', () => {
    expect(
      makeLivingRootConfidenceMetrics({
        ...topLevelInput,
        living_root_confidence_10: undefined,
      })
    ).toEqual({
      ...topLevelOutput,
      Oct: null,
    });
  });

  it('Should return a LivingRootConfidenceMetrics output given KPI response data', () => {
    expect(makeLivingRootConfidenceMetrics(topLevelInput)).toEqual(topLevelOutput);
    expect(makeLivingRootConfidenceMetrics(subregionInput)).toEqual(subregionOutput);
  });
});
