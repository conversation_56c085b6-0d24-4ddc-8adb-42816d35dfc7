import {MeasurementEnum} from '_common/utils/measurement-unit-options';

import {
  KPIYieldPerAreaMock,
  KPIYieldPerAreaSubsectionSummaryMock,
} from 'containers/si/__mocks__/KPIYieldPerAreaMock';
import {
  makeYieldPerAreaMetrics,
  private__yieldPerAreaUnit,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.yield_per_area.transformation';

const topLevelInput = KPIYieldPerAreaMock.metric;

const subregionInput = KPIYieldPerAreaSubsectionSummaryMock.boundary_summary[872]!;

describe('private__yieldPerAreaUnit', () => {
  it('should return the correct yield per area unit given Imperial or Metrics system', () => {
    expect(private__yieldPerAreaUnit(MeasurementEnum.ImperialUnits)).toEqual({
      unit: 'kg/ac',
      unitName: {
        singular: `kilogram yield / acre`,
        plural: `kilograms yield / acre`,
        abbr: `kg yield / ac`,
      },
    });
    expect(private__yieldPerAreaUnit(MeasurementEnum.MetricUnits)).toEqual({
      unit: 'kg/ha',
      unitName: {
        singular: `kilogram yield / hectare`,
        plural: `kilograms yield / hectare`,
        abbr: `kg yield / ha`,
      },
    });
  });
});

describe('makeYieldPerAreaMetrics', () => {
  it('Should return YieldPerAreaMetrics with appropriate units given Imperial or Metric units system', () => {
    expect(
      makeYieldPerAreaMetrics(MeasurementEnum.ImperialUnits)({
        yield_kg_per_m2: 1000,
      })
    ).toEqual({
      yieldPerArea: {
        formattedValue: '4M',
        unit: 'kg/ac',
        value: 4_046_860.3387248116,
      },
    });
    expect(
      makeYieldPerAreaMetrics(MeasurementEnum.MetricUnits)({
        yield_kg_per_m2: 1000,
      })
    ).toEqual({
      yieldPerArea: {
        formattedValue: '10M',
        unit: 'kg/ha',
        value: 10_000_000,
      },
    });
  });

  it('Should return YieldPerAreaMetrics in hectares given 0 and a Metric units system', () => {
    expect(
      makeYieldPerAreaMetrics(MeasurementEnum.MetricUnits)({
        yield_kg_per_m2: 0,
      })
    ).toEqual({
      yieldPerArea: {
        formattedValue: '0',
        unit: 'kg/ha',
        value: 0,
      },
    });
  });

  it('Should return null when null parameters are provided', () => {
    expect(
      makeYieldPerAreaMetrics(MeasurementEnum.MetricUnits)({
        //@ts-expect-error checking all nil values
        yield_kg_per_m2: null,
      })
    ).toEqual(null);
    expect(
      makeYieldPerAreaMetrics(MeasurementEnum.MetricUnits)({
        yield_kg_per_m2: undefined,
      })
    ).toEqual(null);
    expect(makeYieldPerAreaMetrics(MeasurementEnum.MetricUnits)({})).toEqual(null);
  });

  it('Should return null when non finite parameters are provided', () => {
    expect(
      makeYieldPerAreaMetrics(MeasurementEnum.MetricUnits)({
        yield_kg_per_m2: Infinity,
      })
    ).toEqual(null);
  });

  it('Should return a YieldPerAreaMetrics with appropriate units given Imperial or Metric units system', () => {
    expect(
      makeYieldPerAreaMetrics(MeasurementEnum.ImperialUnits)({
        ...topLevelInput,
      })
    ).toEqual({
      yieldPerArea: {
        formattedValue: '4.2K',
        unit: 'kg/ac',
        value: 4164.770057972489,
      },
    });
    expect(makeYieldPerAreaMetrics(MeasurementEnum.MetricUnits)(topLevelInput)).toEqual({
      yieldPerArea: {
        formattedValue: '10.3K',
        unit: 'kg/ha',
        value: 10291.360979570725,
      },
    });
    expect(makeYieldPerAreaMetrics(MeasurementEnum.ImperialUnits)(subregionInput)).toEqual({
      yieldPerArea: {
        formattedValue: '3.4K',
        unit: 'kg/ac',
        value: 3354.9625946168553,
      },
    });
    expect(makeYieldPerAreaMetrics(MeasurementEnum.MetricUnits)(subregionInput)).toEqual({
      yieldPerArea: {
        formattedValue: '8.3K',
        unit: 'kg/ha',
        value: 8290.285094627267,
      },
    });
  });
});
