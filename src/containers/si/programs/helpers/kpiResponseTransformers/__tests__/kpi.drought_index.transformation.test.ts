import type {DroughtIndexKPIMetricData} from 'containers/si/api/apiTypes';
import {QUANTIFICATION_LEVEL_TO_LABEL_MAP} from 'containers/si/programs/helpers/kpi.quantification_level.helpers';
import {
  makeDroughtMetrics,
  private__droughtFormatter,
  private__droughtUnit,
  private__isValidBEDroughtIndexString,
  private__isValidFEDroughtIndexString,
  type DroughtMetrics,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.drought_index.transformation';

describe('private__isValidBEDroughtIndexString', () => {
  it('should correctly return if a be drought index string is valid', () => {
    expect(private__isValidBEDroughtIndexString(-2)).toEqual(false);
    expect(private__isValidBEDroughtIndexString(-1)).toEqual(false);
    expect(private__isValidBEDroughtIndexString('-1.1')).toEqual(false); // should not encounter this as BE should return ints
    expect(private__isValidBEDroughtIndexString('-1.0')).toEqual(false); // should not encounter this as we cast using String in the transform
    expect(private__isValidBEDroughtIndexString('-1')).toEqual(true);
    expect(private__isValidBEDroughtIndexString('0')).toEqual(true);
    expect(private__isValidBEDroughtIndexString('1')).toEqual(true);
    expect(private__isValidBEDroughtIndexString('1.0')).toEqual(false); // should not encounter this as we cast using String in the transform
    expect(private__isValidBEDroughtIndexString('1.1')).toEqual(false); // should not encounter this as BE should return ints
    expect(private__isValidBEDroughtIndexString('2')).toEqual(true);
    expect(private__isValidBEDroughtIndexString('3')).toEqual(true);
    expect(private__isValidBEDroughtIndexString('4')).toEqual(true);
    expect(private__isValidBEDroughtIndexString('5')).toEqual(false);
  });
});

describe('private__isValidFEDroughtIndexString', () => {
  it('should correctly return if a fe drought index string is valid', () => {
    expect(private__isValidFEDroughtIndexString(-2)).toEqual(false);
    expect(private__isValidFEDroughtIndexString(-1)).toEqual(false);
    expect(private__isValidFEDroughtIndexString(0)).toEqual(false);
    expect(private__isValidFEDroughtIndexString(1)).toEqual(false);
    expect(private__isValidFEDroughtIndexString('0')).toEqual(false);
    expect(private__isValidFEDroughtIndexString('1.0')).toEqual(false); // should not encounter this as we use a BE to FE value map
    expect(private__isValidFEDroughtIndexString('1')).toEqual(true);
    expect(private__isValidFEDroughtIndexString('2')).toEqual(true);
    expect(private__isValidFEDroughtIndexString('3')).toEqual(true);
    expect(private__isValidFEDroughtIndexString('4')).toEqual(true);
    expect(private__isValidFEDroughtIndexString('5')).toEqual(true);
    expect(private__isValidFEDroughtIndexString('6')).toEqual(true);
    expect(private__isValidFEDroughtIndexString('7')).toEqual(false);
    expect(private__isValidFEDroughtIndexString(7)).toEqual(false);
  });
});

describe('private__droughtUnit', () => {
  it('should correctly return the drought index unit', () => {
    expect(private__droughtUnit()).toEqual({
      unit: 'drought-index' as const,
      unitName: {
        singular: 'classification',
        plural: 'classification',
        abbr: 'classification',
      },
    });
  });
});

describe('private__droughtFormatter', () => {
  it('should correctly return a formatted drought index', () => {
    expect(private__droughtFormatter(-1)).toEqual(''); // should not encounter this (unless we do math on the fe) as we return null for invalid fe drought values
    expect(private__droughtFormatter(0)).toEqual(''); // should not encounter this (unless we do math on the fe) as we return null for invalid fe drought values
    expect(private__droughtFormatter(1)).toEqual('Normal / wet');
    expect(private__droughtFormatter(2)).toEqual('D0');
    expect(private__droughtFormatter(3)).toEqual('D1');
    expect(private__droughtFormatter(4)).toEqual('D2');
    expect(private__droughtFormatter(5)).toEqual('D3');
    expect(private__droughtFormatter(6)).toEqual('D4');
    expect(private__droughtFormatter(7)).toEqual(''); // should not encounter this (unless we do math on the fe) as we return null for invalid fe drought values
  });
});

describe('makeDroughtMetrics', () => {
  const defaultInput1: DroughtIndexKPIMetricData = {
    drought_index: 1,
    quantification_level: 3,
  };
  const defaultInput2: DroughtIndexKPIMetricData = {
    drought_index: 1.0,
    quantification_level: 3.0,
  };
  const defaultInput3: DroughtIndexKPIMetricData = {
    drought_index: -1,
    quantification_level: 2,
  };
  const defaultInput4: DroughtIndexKPIMetricData = {
    drought_index: 0,
    quantification_level: 2,
  };

  it('Should return DroughtMetrics independent of Imperial or Metric units system', () => {
    const output1: DroughtMetrics = {
      droughtIndex: {
        value: 3,
        formattedValue: 'D1',
        unit: 'drought-index',
      },
      tier: {
        value: 3,
        formattedValue: QUANTIFICATION_LEVEL_TO_LABEL_MAP[3],
        unit: 'metric-tier',
      },
    };

    const output2: DroughtMetrics = {
      droughtIndex: {
        value: 3.0,
        formattedValue: 'D1',
        unit: 'drought-index',
      },
      tier: {
        value: 3.0,
        formattedValue: QUANTIFICATION_LEVEL_TO_LABEL_MAP[3],
        unit: 'metric-tier',
      },
    };

    const output3: DroughtMetrics = {
      droughtIndex: {
        value: 1,
        formattedValue: 'Normal / wet',
        unit: 'drought-index',
      },
      tier: {
        value: 2,
        formattedValue: QUANTIFICATION_LEVEL_TO_LABEL_MAP[2],
        unit: 'metric-tier',
      },
    };

    const output4: DroughtMetrics = {
      droughtIndex: {
        value: 2,
        formattedValue: 'D0',
        unit: 'drought-index',
      },
      tier: {
        value: 2,
        formattedValue: QUANTIFICATION_LEVEL_TO_LABEL_MAP[2],
        unit: 'metric-tier',
      },
    };

    expect(makeDroughtMetrics(defaultInput1)).toEqual(output1);
    expect(makeDroughtMetrics(defaultInput2)).toEqual(output2);
    expect(makeDroughtMetrics(defaultInput3)).toEqual(output3);
    expect(makeDroughtMetrics(defaultInput4)).toEqual(output4);
  });

  it('Should return null when null parameters are provided', () => {
    expect(
      makeDroughtMetrics({
        ...defaultInput1,
        //@ts-expect-error checking all nil values
        drought_index: null,
      })
    ).toEqual(null);
    expect(
      makeDroughtMetrics({
        ...defaultInput1,
        drought_index: undefined,
      })
    ).toEqual(null);
    expect(
      makeDroughtMetrics({
        ...defaultInput1,
        //@ts-expect-error checking all nil values
        quantification_level: null,
      })
    ).toEqual(null);
    expect(
      makeDroughtMetrics({
        ...defaultInput1,
        quantification_level: undefined,
      })
    ).toEqual(null);
    expect(makeDroughtMetrics({})).toEqual(null);
  });

  it('Should return null when non finite parameters are provided', () => {
    expect(
      makeDroughtMetrics({
        ...defaultInput1,
        drought_index: Infinity,
      })
    ).toEqual(null);
    expect(
      makeDroughtMetrics({
        ...defaultInput1,
        quantification_level: Infinity,
      })
    ).toEqual(null);
  });

  it('Should return null when an invalid drought_score or quantification_level is provided', () => {
    expect(
      makeDroughtMetrics({
        ...defaultInput1,
        drought_index: 5,
      })
    ).toEqual(null);
    expect(
      makeDroughtMetrics({
        ...defaultInput1,
        quantification_level: 1,
      })
    ).toEqual(null);
  });
});
