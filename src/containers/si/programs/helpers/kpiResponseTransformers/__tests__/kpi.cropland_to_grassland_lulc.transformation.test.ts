import {MeasurementEnum} from '_common/utils/measurement-unit-options';

import {KPIC2gLulcMock, KPIC2gLulcSubsectionMock} from 'containers/si/__mocks__/KPIC2gLulcMock';
import {
  makeC2gLulcMetrics,
  private__c2gLulcAreaUnit,
  private__c2gLulcRestorationUnit,
  type C2gLulcMetrics,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.cropland_to_grassland_lulc.transformation';

const topLevelInput = KPIC2gLulcMock.metric;

const topLevelOutputImperialUnits: C2gLulcMetrics = {
  restoration: {
    formattedValue: '0.1%',
    unit: 'unit-interval',
    value: 0.001123141648218425,
  },
  restoredArea: {
    formattedValue: '1.2M',
    unit: 'ac',
    value: 1204146.595663223,
  },
  totalTrackedArea: {
    formattedValue: '1.1B',
    unit: 'ac',
    value: 1072123536.3084359,
  },
};

const topLevelOutputMetricUnits: C2gLulcMetrics = {
  restoration: {
    formattedValue: '0.1%',
    unit: 'unit-interval',
    value: 0.001123141648218425,
  },
  restoredArea: {
    formattedValue: '487.3K',
    unit: 'ha',
    value: 487301.31,
  },
  totalTrackedArea: {
    formattedValue: '433.9M',
    unit: 'ha',
    value: 433873421.73,
  },
};

const subregionInput = KPIC2gLulcSubsectionMock.boundary_summary[872]!;

const subregionOutputImperialUnits: C2gLulcMetrics = {
  restoration: {
    formattedValue: '0.06%',
    unit: 'unit-interval',
    value: 0.0006337013702437038,
  },
  restoredArea: {
    formattedValue: '196.9K',
    unit: 'ac',
    value: 196887.96580785126,
  },
  totalTrackedArea: {
    formattedValue: '310.7M',
    unit: 'ac',
    value: 310695187.12281406,
  },
};

const subregionOutputMetricUnits: C2gLulcMetrics = {
  restoration: {
    formattedValue: '0.06%',
    unit: 'unit-interval',
    value: 0.0006337013702437038,
  },
  restoredArea: {
    formattedValue: '79.7K',
    unit: 'ha',
    value: 79677.81000000001,
  },
  totalTrackedArea: {
    formattedValue: '125.7M',
    unit: 'ha',
    value: 125734003.02000003,
  },
};

const zeroValueOutputMetricUnits: C2gLulcMetrics = {
  restoration: {
    formattedValue: '0%',
    unit: 'unit-interval',
    value: 0,
  },
  restoredArea: {
    formattedValue: '0',
    unit: 'ha',
    value: 0,
  },
  totalTrackedArea: {
    formattedValue: '1',
    unit: 'ha',
    value: 1,
  },
};

describe('private__c2gLulcRestorationUnit', () => {
  it('should return the correct grassland to cropland restoration unit independent of Imperial or Metrics system', () => {
    expect(private__c2gLulcRestorationUnit()).toEqual({
      unit: 'unit-interval',
      unitName: {
        singular: '% of land area restored',
        plural: '% of land area restored',
        abbr: '',
      },
    });
  });
});

describe('private__c2gLulcAreaUnit', () => {
  it('should return the correct grassland to cropland area unit given Imperial or Metrics system', () => {
    expect(private__c2gLulcAreaUnit(MeasurementEnum.ImperialUnits)).toEqual({
      unit: 'ac',
      unitName: {
        singular: 'acre',
        plural: 'acres',
        abbr: 'ac',
      },
    });
    expect(private__c2gLulcAreaUnit(MeasurementEnum.MetricUnits)).toEqual({
      unit: 'ha',
      unitName: {
        singular: 'hectare',
        plural: 'hectares',
        abbr: 'ha',
      },
    });
  });
});

describe('makeC2gLulcMetrics', () => {
  it('Should return a C2gLulcMetric in ac given Imperial units system', () => {
    const expected: C2gLulcMetrics = {
      restoration: {
        formattedValue: '50%',
        unit: 'unit-interval',
        value: 0.5,
      },
      restoredArea: {
        formattedValue: '2.5K',
        unit: 'ac',
        value: 2471.0514233241506,
      },
      totalTrackedArea: {
        formattedValue: '4.9K',
        unit: 'ac',
        value: 4942.102846648301,
      },
    };
    expect(
      makeC2gLulcMetrics(MeasurementEnum.ImperialUnits)({
        c2g_lulc_conversion_area_m2: 10000000,
        c2g_lulc_total_tracked_area_m2: 20000000,
      })
    ).toEqual(expected);
  });

  it('Should return a C2gLulcMetric in ha given Metric units system', () => {
    const expected: C2gLulcMetrics = {
      restoration: {
        formattedValue: '50%',
        unit: 'unit-interval',
        value: 0.5,
      },
      restoredArea: {
        formattedValue: '1K',
        unit: 'ha',
        value: 1000,
      },
      totalTrackedArea: {
        formattedValue: '2K',
        unit: 'ha',
        value: 2000,
      },
    };

    expect(
      makeC2gLulcMetrics(MeasurementEnum.MetricUnits)({
        c2g_lulc_conversion_area_m2: 10000000,
        c2g_lulc_total_tracked_area_m2: 20000000,
      })
    ).toEqual(expected);
  });

  it('Should return null when nil parameters are provided', () => {
    expect(
      makeC2gLulcMetrics(MeasurementEnum.MetricUnits)({
        //@ts-expect-error checking all nil values
        c2g_lulc_conversion_area_m2: null,
        c2g_lulc_total_tracked_area_m2: 1,
      })
    ).toEqual(null);
    expect(
      makeC2gLulcMetrics(MeasurementEnum.MetricUnits)({
        c2g_lulc_conversion_area_m2: undefined,
        c2g_lulc_total_tracked_area_m2: 1,
      })
    ).toEqual(null);
    expect(
      makeC2gLulcMetrics(MeasurementEnum.MetricUnits)({
        c2g_lulc_total_tracked_area_m2: 1,
      })
    ).toEqual(null);
    expect(
      makeC2gLulcMetrics(MeasurementEnum.MetricUnits)({
        c2g_lulc_conversion_area_m2: 1,
        // @ts-expect-error checking all nil values
        c2g_lulc_total_tracked_area_m2: null,
      })
    ).toEqual(null);
    expect(
      makeC2gLulcMetrics(MeasurementEnum.MetricUnits)({
        c2g_lulc_conversion_area_m2: 1,
        c2g_lulc_total_tracked_area_m2: undefined,
      })
    ).toEqual(null);
    expect(
      makeC2gLulcMetrics(MeasurementEnum.MetricUnits)({
        c2g_lulc_conversion_area_m2: 1,
      })
    ).toEqual(null);
    expect(makeC2gLulcMetrics(MeasurementEnum.MetricUnits)({})).toEqual(null);
  });

  it('Should return null when non finite parameters are provided', () => {
    expect(
      makeC2gLulcMetrics(MeasurementEnum.MetricUnits)({
        c2g_lulc_conversion_area_m2: NaN,
        c2g_lulc_total_tracked_area_m2: 1,
      })
    ).toEqual(null);
    expect(
      makeC2gLulcMetrics(MeasurementEnum.MetricUnits)({
        c2g_lulc_conversion_area_m2: 1,
        c2g_lulc_total_tracked_area_m2: Infinity,
      })
    ).toEqual(null);
  });

  it('Should return null when empty parameters are provided for either c2g_lulc_conversion_area_m2 or c2g_lulc_total_tracked_area_m2', () => {
    expect(
      makeC2gLulcMetrics(MeasurementEnum.MetricUnits)({
        c2g_lulc_conversion_area_m2: 0,
      })
    ).toEqual(null);
    expect(
      makeC2gLulcMetrics(MeasurementEnum.MetricUnits)({
        c2g_lulc_total_tracked_area_m2: 0,
      })
    ).toEqual(null);
  });

  it('Should return null when 0 is provided for both c2g_lulc_conversion_area_m2 and c2g_lulc_total_tracked_area_m2', () => {
    expect(
      makeC2gLulcMetrics(MeasurementEnum.MetricUnits)({
        c2g_lulc_conversion_area_m2: 0,
        c2g_lulc_total_tracked_area_m2: 0,
      })
    ).toEqual(null);
  });

  it('Should return a C2gLulcMetric in ha when 0 is provided as c2g_lulc_conversion_area_m2 and a Metric units system', () => {
    expect(
      makeC2gLulcMetrics(MeasurementEnum.MetricUnits)({
        c2g_lulc_conversion_area_m2: 0,
        c2g_lulc_total_tracked_area_m2: 10000,
      })
    ).toEqual(zeroValueOutputMetricUnits);
  });

  it('Should correctly return 0% adoption when c2g_lulc_conversion_area_m2 is 0', () => {
    expect(
      makeC2gLulcMetrics(MeasurementEnum.MetricUnits)({
        c2g_lulc_conversion_area_m2: 0,
        c2g_lulc_total_tracked_area_m2: 10000,
      })
    ).toEqual(zeroValueOutputMetricUnits);
  });

  it('Should correctly return 100% adoption when c2g_lulc_conversion_area_m2 and c2g_lulc_total_tracked_area_m2 values match', () => {
    const expected: C2gLulcMetrics = {
      restoration: {
        formattedValue: '100%',
        unit: 'unit-interval',
        value: 1,
      },
      restoredArea: {
        formattedValue: '1',
        unit: 'ha',
        value: 1,
      },
      totalTrackedArea: {
        formattedValue: '1',
        unit: 'ha',
        value: 1,
      },
    };

    expect(
      makeC2gLulcMetrics(MeasurementEnum.MetricUnits)({
        c2g_lulc_conversion_area_m2: 10000,
        c2g_lulc_total_tracked_area_m2: 10000,
      })
    ).toEqual(expected);
  });
  it('Should return a C2gLulcMetric in acres units given KPI response data and an Imperial units system', () => {
    expect(makeC2gLulcMetrics(MeasurementEnum.ImperialUnits)(topLevelInput)).toEqual(
      topLevelOutputImperialUnits
    );
    expect(makeC2gLulcMetrics(MeasurementEnum.ImperialUnits)(subregionInput)).toEqual(
      subregionOutputImperialUnits
    );
  });
  it('Should return a C2gLulcMetric in hectares units given KPI response data and a Metric units system', () => {
    expect(makeC2gLulcMetrics(MeasurementEnum.MetricUnits)(topLevelInput)).toEqual(
      topLevelOutputMetricUnits
    );
    expect(makeC2gLulcMetrics(MeasurementEnum.MetricUnits)(subregionInput)).toEqual(
      subregionOutputMetricUnits
    );
  });
});
