import {MeasurementEnum} from '_common/utils/measurement-unit-options';

import {KPICropRotationNumberMock} from 'containers/si/__mocks__/KPICropRotationNumberMock';
import type {CropRotationNumberMetricData} from 'containers/si/api/apiTypes';
import {
  makeCropRotationNumberMetric,
  private__cropRotationNumberUnit,
  type CropRotationNumberMetric,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.crop_rotation_number.transformation';

const topLevelInput = KPICropRotationNumberMock.metric;

const topLevelOutputImperialUnits: CropRotationNumberMetric = {
  oneCropRotationArea: {formattedValue: '14M', unit: 'ac', value: 13768338.199467707},
  twoCropRotationArea: {formattedValue: '109M', unit: 'ac', value: 108633713.20453835},
  threeCropRotationArea: {formattedValue: '29M', unit: 'ac', value: 28876200.70702712},
  totalCropRotationDataArea: {formattedValue: '151M', unit: 'ac', value: 151278252.11103317},
};

const topLevelOutputMetricUnits: CropRotationNumberMetric = {
  oneCropRotationArea: {formattedValue: '5.6M', unit: 'ha', value: 5571854.178957566},
  twoCropRotationArea: {formattedValue: '44M', unit: 'ha', value: 43962546.541585214},
  threeCropRotationArea: {formattedValue: '12M', unit: 'ha', value: 11685795.137432542},
  totalCropRotationDataArea: {formattedValue: '61M', unit: 'ha', value: 61220195.85797532},
};

const baseInput: CropRotationNumberMetricData = {
  croprtn_1_m2: 1000,
  croprtn_2_m2: 2000,
  croprtn_3_m2: 3000,
};

const baseOutputMetric: CropRotationNumberMetric = {
  oneCropRotationArea: {formattedValue: '0.1', unit: 'ha', value: 0.1},
  twoCropRotationArea: {formattedValue: '0.2', unit: 'ha', value: 0.2},
  threeCropRotationArea: {formattedValue: '0.3', unit: 'ha', value: 0.3},
  totalCropRotationDataArea: {formattedValue: '0.6', unit: 'ha', value: 0.6},
};

const baseOutputImperial: CropRotationNumberMetric = {
  oneCropRotationArea: {formattedValue: '0.247', unit: 'ac', value: 0.24710514233241504},
  twoCropRotationArea: {formattedValue: '0.494', unit: 'ac', value: 0.4942102846648301},
  threeCropRotationArea: {formattedValue: '0.741', unit: 'ac', value: 0.7413154269972451},
  totalCropRotationDataArea: {formattedValue: '1.483', unit: 'ac', value: 1.4826308539944901},
};

describe('private__cropRotationNumberUnit', () => {
  it('should return the correct unit given Imperial or Metrics system', () => {
    expect(private__cropRotationNumberUnit(MeasurementEnum.ImperialUnits)).toEqual({
      unit: 'ac',
      unitName: {
        singular: 'acre',
        plural: 'acres',
        abbr: 'ac',
      },
    });
    expect(private__cropRotationNumberUnit(MeasurementEnum.MetricUnits)).toEqual({
      unit: 'ha',
      unitName: {
        singular: 'hectare',
        plural: 'hectares',
        abbr: 'ha',
      },
    });
  });
});

describe('makeCropRotationNumberMetric', () => {
  it('Should return CropRotationNumberMetric given Imperial or Metric units system', () => {
    expect(makeCropRotationNumberMetric(MeasurementEnum.ImperialUnits)(baseInput)).toEqual(
      baseOutputImperial
    );
    expect(makeCropRotationNumberMetric(MeasurementEnum.MetricUnits)(baseInput)).toEqual(
      baseOutputMetric
    );
  });

  it('Should return a CropRotationNumberMetric when a 0 parameter is provided', () => {
    expect(
      makeCropRotationNumberMetric(MeasurementEnum.MetricUnits)({...baseInput, croprtn_1_m2: 0})
    ).toEqual({
      ...baseOutputMetric,
      oneCropRotationArea: {
        value: 0,
        unit: 'ha',
        formattedValue: '0',
      },
      totalCropRotationDataArea: {
        value: 0.5,
        unit: 'ha',
        formattedValue: '0.5',
      },
    });
  });

  it('Should return null when nil parameters are provided', () => {
    expect(
      makeCropRotationNumberMetric(MeasurementEnum.MetricUnits)({
        ...baseInput,
        // @ts-expect-error being extra safe to handle all nil values
        croprtn_1_m2: null,
      })
    ).toEqual(null);
    expect(
      makeCropRotationNumberMetric(MeasurementEnum.MetricUnits)({
        ...baseInput,
        croprtn_2_m2: undefined,
      })
    ).toEqual(null);
    expect(makeCropRotationNumberMetric(MeasurementEnum.MetricUnits)({})).toEqual(null);
    expect(
      makeCropRotationNumberMetric(MeasurementEnum.MetricUnits)({
        croprtn_1_m2: 100,
        croprtn_2_m2: 200,
      })
    ).toEqual(null);
  });

  it('Should return null when non finite parameters are provided', () => {
    expect(
      makeCropRotationNumberMetric(MeasurementEnum.MetricUnits)({
        ...baseInput,
        croprtn_2_m2: Infinity,
      })
    ).toEqual(null);
    expect(
      makeCropRotationNumberMetric(MeasurementEnum.MetricUnits)({
        ...baseInput,
        croprtn_1_m2: NaN,
      })
    ).toEqual(null);
  });
  it('Should return a CropRotationNumberMetric with converted area values (m2 to ac), formattedValue string values, and units given Imperial units system', () => {
    expect(makeCropRotationNumberMetric(MeasurementEnum.ImperialUnits)(topLevelInput)).toEqual(
      topLevelOutputImperialUnits
    );
  });
  it('Should return a CropRotationNumberMetric with converted area values (m2 to ha), formattedValue string values, and units given Metric units system', () => {
    expect(makeCropRotationNumberMetric(MeasurementEnum.MetricUnits)(topLevelInput)).toEqual(
      topLevelOutputMetricUnits
    );
  });
});
