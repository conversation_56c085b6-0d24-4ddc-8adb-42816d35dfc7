import {CO2E} from 'containers/si/constants';
import {
  makeVolumeWeightedNetEFMetrics,
  private__netEFUnit,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.volume_weighted_net_emissions_factor.transformation';

describe('private__netEFUnit', () => {
  it('should return the correct ghg ef unit', () => {
    expect(private__netEFUnit()).toEqual({
      unit: 'kg/kg',
      unitName: {
        singular: `kilogram ${CO2E} / kilogram yield`,
        plural: `kilograms ${CO2E} / kilogram yield`,
        abbr: `kg ${CO2E} / kg yield`,
      },
    });
  });
});

describe('makeVolumeWeightedNetEFMetrics', () => {
  it('Should return VolumeWeightedNetEFMetrics with appropriate units', () => {
    expect(
      makeVolumeWeightedNetEFMetrics({
        volume_weighted_net_emissions_factor: 0.5408840070399256,
      })
    ).toEqual({
      volumeWeighteNetEmissionsPerYeild: {
        formattedValue: '0.541',
        unit: 'kg/kg',
        value: 0.5408840070399256,
      },
    });
  });

  it('Should return VolumeWeightedNetEFMetrics in appropriate units given 0', () => {
    expect(
      makeVolumeWeightedNetEFMetrics({
        volume_weighted_net_emissions_factor: 0,
      })
    ).toEqual({
      volumeWeighteNetEmissionsPerYeild: {
        formattedValue: '0',
        unit: 'kg/kg',
        value: 0,
      },
    });
  });

  it('Should return null when null parameters are provided', () => {
    expect(
      makeVolumeWeightedNetEFMetrics({
        //@ts-expect-error covering all nil cases
        volume_weighted_net_emissions_factor: null,
      })
    ).toEqual(null);
    expect(
      makeVolumeWeightedNetEFMetrics({
        volume_weighted_net_emissions_factor: undefined,
      })
    ).toEqual(null);
    expect(makeVolumeWeightedNetEFMetrics({})).toEqual(null);
  });

  it('Should return null when non finite parameters are provided', () => {
    expect(
      makeVolumeWeightedNetEFMetrics({
        volume_weighted_net_emissions_factor: Infinity,
      })
    ).toEqual(null);
  });
});
