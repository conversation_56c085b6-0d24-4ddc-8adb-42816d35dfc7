import {MeasurementEnum} from '_common/utils/measurement-unit-options';

import {KPIG2cLulcKpiMock, KPIG2cLulcSubsectionMock} from 'containers/si/__mocks__/KPIG2cLulcMock';
import {
  makeG2cLulcMetrics,
  private_g2cLulcAreaUnit,
  private_g2cLulcConversionUnit,
  type G2cLulcMetrics,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.grassland_to_cropland_lulc.transformation';

const topLevelInput = KPIG2cLulcKpiMock.metric;

const topLevelOutputImperialUnits: G2cLulcMetrics = {
  conversion: {
    formattedValue: '0.2%',
    unit: 'unit-interval',
    value: 0.002030278776901377,
  },
  convertedArea: {
    formattedValue: '2.2M',
    unit: 'ac',
    value: 2176709.6619834704,
  },
  totalTrackedArea: {
    formattedValue: '1.1B',
    unit: 'ac',
    value: 1072123536.3084362,
  },
};

const topLevelOutputMetricUnits: G2cLulcMetrics = {
  conversion: {
    formattedValue: '0.2%',
    unit: 'unit-interval',
    value: 0.002030278776901377,
  },
  convertedArea: {
    formattedValue: '880.9K',
    unit: 'ha',
    value: 880883.9999999998,
  },
  totalTrackedArea: {
    formattedValue: '433.9M',
    unit: 'ha',
    value: 433873421.73,
  },
};

const subregionInput = KPIG2cLulcSubsectionMock.boundary_summary?.[872];

const subregionOutputImperialUnits: G2cLulcMetrics = {
  conversion: {
    formattedValue: '0.09%',
    unit: 'unit-interval',
    value: 0.0008602396122136921,
  },
  convertedArea: {
    formattedValue: '267.3K',
    unit: 'ac',
    value: 267272.3072871901,
  },
  totalTrackedArea: {
    formattedValue: '310.7M',
    unit: 'ac',
    value: 310695187.12281406,
  },
};

const subregionOutputMetricUnits: G2cLulcMetrics = {
  conversion: {
    formattedValue: '0.09%',
    unit: 'unit-interval',
    value: 0.0008602396122136921,
  },
  convertedArea: {
    formattedValue: '108.2K',
    unit: 'ha',
    value: 108161.37,
  },
  totalTrackedArea: {
    formattedValue: '125.7M',
    unit: 'ha',
    value: 125734003.02,
  },
};

const zeroValueOutputMetricUnits: G2cLulcMetrics = {
  conversion: {
    formattedValue: '0%',
    unit: 'unit-interval',
    value: 0,
  },
  convertedArea: {
    formattedValue: '0',
    unit: 'ha',
    value: 0,
  },
  totalTrackedArea: {
    formattedValue: '1',
    unit: 'ha',
    value: 1,
  },
};

describe('g2cLulcConversionUnit', () => {
  it('should return the correct grassland to cropland conversion unit independent of Imperial or Metrics system', () => {
    expect(private_g2cLulcConversionUnit()).toEqual({
      unit: 'unit-interval',
      unitName: {
        singular: '% of land area converted',
        plural: '% of land area converted',
        abbr: '',
      },
    });
  });
});

describe('g2cLulcAreaUnit', () => {
  it('should return the correct grassland to cropland area unit given Imperial or Metrics system', () => {
    expect(private_g2cLulcAreaUnit(MeasurementEnum.ImperialUnits)).toEqual({
      unit: 'ac',
      unitName: {
        singular: 'acre',
        plural: 'acres',
        abbr: 'ac',
      },
    });
    expect(private_g2cLulcAreaUnit(MeasurementEnum.MetricUnits)).toEqual({
      unit: 'ha',
      unitName: {
        singular: 'hectare',
        plural: 'hectares',
        abbr: 'ha',
      },
    });
  });
});

describe('_makeG2cLulcMetrics', () => {
  it('Should return a G2cLulcMetric in ac given Imperial units system', () => {
    const expected: G2cLulcMetrics = {
      conversion: {
        formattedValue: '50%',
        unit: 'unit-interval',
        value: 0.5,
      },
      convertedArea: {
        formattedValue: '2.5K',
        unit: 'ac',
        value: 2471.0514233241506,
      },
      totalTrackedArea: {
        formattedValue: '4.9K',
        unit: 'ac',
        value: 4942.102846648301,
      },
    };
    expect(
      makeG2cLulcMetrics(MeasurementEnum.ImperialUnits)({
        g2c_lulc_conversion_area_m2: 10000000,
        g2c_lulc_total_tracked_area_m2: 20000000,
      })
    ).toEqual(expected);
  });

  it('Should return a G2cLulcMetric in ha given Metric units system', () => {
    const expected: G2cLulcMetrics = {
      conversion: {
        formattedValue: '50%',
        unit: 'unit-interval',
        value: 0.5,
      },
      convertedArea: {
        formattedValue: '1K',
        unit: 'ha',
        value: 1000,
      },
      totalTrackedArea: {
        formattedValue: '2K',
        unit: 'ha',
        value: 2000,
      },
    };

    expect(
      makeG2cLulcMetrics(MeasurementEnum.MetricUnits)({
        g2c_lulc_conversion_area_m2: 10000000,
        g2c_lulc_total_tracked_area_m2: 20000000,
      })
    ).toEqual(expected);
  });

  it('Should return null when null parameters are provided', () => {
    expect(
      makeG2cLulcMetrics(MeasurementEnum.MetricUnits)({
        g2c_lulc_conversion_area_m2: undefined,
        g2c_lulc_total_tracked_area_m2: 1,
      })
    ).toEqual(null);
    expect(
      makeG2cLulcMetrics(MeasurementEnum.MetricUnits)({
        g2c_lulc_conversion_area_m2: 1,
        g2c_lulc_total_tracked_area_m2: undefined,
      })
    ).toEqual(null);
  });

  it('Should return null when non finite parameters are provided', () => {
    expect(
      makeG2cLulcMetrics(MeasurementEnum.MetricUnits)({
        g2c_lulc_conversion_area_m2: NaN,
        g2c_lulc_total_tracked_area_m2: 1,
      })
    ).toEqual(null);

    expect(makeG2cLulcMetrics(MeasurementEnum.MetricUnits)(undefined)).toEqual(null);
    expect(
      makeG2cLulcMetrics(MeasurementEnum.MetricUnits)({
        g2c_lulc_conversion_area_m2: 1,
        g2c_lulc_total_tracked_area_m2: Infinity,
      })
    ).toEqual(null);
  });

  it('Should return null when empty parameters are provided for either g2c_lulc_conversion_area_m2 or g2c_lulc_total_tracked_area_m2', () => {
    expect(
      makeG2cLulcMetrics(MeasurementEnum.MetricUnits)({
        g2c_lulc_conversion_area_m2: 0,
      })
    ).toEqual(null);
    expect(
      makeG2cLulcMetrics(MeasurementEnum.MetricUnits)({
        g2c_lulc_total_tracked_area_m2: 2,
      })
    ).toEqual(null);
    expect(makeG2cLulcMetrics(MeasurementEnum.MetricUnits)({})).toEqual(null);
  });

  it('Should return null when 0 is provided for both g2c_lulc_conversion_area_m2 and g2c_lulc_total_tracked_area_m2', () => {
    expect(
      makeG2cLulcMetrics(MeasurementEnum.MetricUnits)({
        g2c_lulc_conversion_area_m2: 0,
        g2c_lulc_total_tracked_area_m2: 0,
      })
    ).toEqual(null);
  });

  it('Should return a G2cLulcMetric in ha when 0 is provided as g2c_lulc_conversion_area_m2 and a Metric units system', () => {
    expect(
      makeG2cLulcMetrics(MeasurementEnum.MetricUnits)({
        g2c_lulc_conversion_area_m2: 0,
        g2c_lulc_total_tracked_area_m2: 10000,
      })
    ).toEqual(zeroValueOutputMetricUnits);
  });

  it('Should correctly return 0% adoption when g2c_lulc_conversion_area_m2 is 0', () => {
    expect(
      makeG2cLulcMetrics(MeasurementEnum.MetricUnits)({
        g2c_lulc_conversion_area_m2: 0,
        g2c_lulc_total_tracked_area_m2: 10000,
      })
    ).toEqual(zeroValueOutputMetricUnits);
  });

  it('Should correctly return 100% adoption when g2c_lulc_conversion_area_m2 and g2c_lulc_total_tracked_area_m2 values match', () => {
    const expected: G2cLulcMetrics = {
      conversion: {
        formattedValue: '100%',
        unit: 'unit-interval',
        value: 1,
      },
      convertedArea: {
        formattedValue: '1',
        unit: 'ha',
        value: 1,
      },
      totalTrackedArea: {
        formattedValue: '1',
        unit: 'ha',
        value: 1,
      },
    };

    expect(
      makeG2cLulcMetrics(MeasurementEnum.MetricUnits)({
        g2c_lulc_conversion_area_m2: 10000,
        g2c_lulc_total_tracked_area_m2: 10000,
      })
    ).toEqual(expected);
  });
  it('Should return a G2cLulcMetric in acres units given KPI response data and an Imperial units system', () => {
    expect(makeG2cLulcMetrics(MeasurementEnum.ImperialUnits)(topLevelInput)).toEqual(
      topLevelOutputImperialUnits
    );
  });
  it('Should return a G2cLulcMetric in hectares units given KPI response data and a Metric units system', () => {
    expect(makeG2cLulcMetrics(MeasurementEnum.MetricUnits)(topLevelInput)).toEqual(
      topLevelOutputMetricUnits
    );
  });

  it('Should return a G2cLulcMetric in hectares units given g2c_lulc_conversion_area_m2 of 0 and a Metric units system', () => {
    const expected: G2cLulcMetrics = {
      ...zeroValueOutputMetricUnits,
      totalTrackedArea: {
        formattedValue: '433.9M',
        unit: 'ha',
        value: 433873421.73,
      },
    };

    expect(
      makeG2cLulcMetrics(MeasurementEnum.MetricUnits)({
        ...topLevelInput,
        g2c_lulc_conversion_area_m2: 0,
      })
    ).toEqual(expected);
  });

  it('Should return a G2cLulcMetric in acres units given KPI response data and an Imperial units system', () => {
    expect(
      makeG2cLulcMetrics(MeasurementEnum.ImperialUnits)({
        ...subregionInput,
      })
    ).toEqual(subregionOutputImperialUnits);
  });

  it('Should return a G2cLulcMetric in hectares units given KPI response data and a Metric units system', () => {
    expect(
      makeG2cLulcMetrics(MeasurementEnum.MetricUnits)({
        ...subregionInput,
      })
    ).toEqual(subregionOutputMetricUnits);
  });

  it('Should return a G2cLulcMetric in hectares units when 0 value is provided for one subregionKpiSubtypeResponse key and a Metric units system', () => {
    expect(
      makeG2cLulcMetrics(MeasurementEnum.MetricUnits)({
        g2c_lulc_conversion_area_m2: 0,
        g2c_lulc_total_tracked_area_m2: 10000,
      })
    ).toEqual(zeroValueOutputMetricUnits);
  });
});
