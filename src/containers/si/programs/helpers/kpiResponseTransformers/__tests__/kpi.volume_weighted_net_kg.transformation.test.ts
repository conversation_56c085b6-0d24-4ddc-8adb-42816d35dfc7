import {KPIVolumeWeightedNetKgSummarizeByCropTypeMock} from 'containers/si/__mocks__/KPIVolumeWeightedNetMetricsMock';
import {makeVolumeWeightedNetMetrics} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.volume_weighted_net_kg.transformation';

describe('makeVolumeWeightedNetMetrics', () => {
  it('Should return VolumeWeightedNetMetrics for a crop_type_summary with appropriate units', () => {
    expect(
      makeVolumeWeightedNetMetrics(
        KPIVolumeWeightedNetKgSummarizeByCropTypeMock.crop_type_summary[1]!
      )
    ).toEqual({volumeWeightedNetMass: {formattedValue: '0.01', unit: 'mt', value: 0.01}});
    expect(
      makeVolumeWeightedNetMetrics(
        KPIVolumeWeightedNetKgSummarizeByCropTypeMock.crop_type_summary[2]!
      )
    ).toEqual({volumeWeightedNetMass: {formattedValue: '0.02', unit: 'mt', value: 0.02}});
    expect(
      makeVolumeWeightedNetMetrics(
        KPIVolumeWeightedNetKgSummarizeByCropTypeMock.crop_type_summary[3]!
      )
    ).toEqual({volumeWeightedNetMass: {formattedValue: '0.03', unit: 'mt', value: 0.03}});
  });
  it('Should return null when null parameters are provided', () => {
    expect(
      makeVolumeWeightedNetMetrics({
        //@ts-expect-error covering all nil cases
        volume_weighted_net_kg: null,
      })
    ).toEqual(null);
    expect(
      makeVolumeWeightedNetMetrics({
        volume_weighted_net_kg: undefined,
      })
    ).toEqual(null);
    expect(makeVolumeWeightedNetMetrics({})).toEqual(null);
  });

  it('Should return null when non finite parameters are provided', () => {
    expect(
      makeVolumeWeightedNetMetrics({
        volume_weighted_net_kg: Infinity,
      })
    ).toEqual(null);
  });
});
