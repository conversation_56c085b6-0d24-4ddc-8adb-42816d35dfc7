import {MeasurementEnum} from '_common/utils/measurement-unit-options';
import {getTypedKeys} from '_common/utils/object';

import type {GreennessLevelMetricData} from 'containers/si/api/apiTypes';
import {
  makeGreennessLevelMetrics,
  private__greennessLevelAreaUnit,
  type GreennessLevelMetrics,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.greenness_level_area_m2.transformation';

describe('tillageAreaUnit', () => {
  it('should return the correct tillage area unit given Imperial or Metrics system', () => {
    expect(private__greennessLevelAreaUnit(MeasurementEnum.ImperialUnits)).toEqual({
      unit: 'ac',
      unitName: {
        singular: 'acre',
        plural: 'acres',
        abbr: 'ac',
      },
    });
    expect(private__greennessLevelAreaUnit(MeasurementEnum.MetricUnits)).toEqual({
      unit: 'ha',
      unitName: {
        singular: 'hectare',
        plural: 'hectares',
        abbr: 'ha',
      },
    });
  });
});

const responseKeyToMetricKeyMap: Record<
  keyof GreennessLevelMetricData,
  keyof GreennessLevelMetrics
> = {
  greenness_level_bare_area_m2: 'bareArea',
  greenness_level_low_area_m2: 'lowArea',
  greenness_level_moderate_area_m2: 'moderateArea',
  greenness_level_high_area_m2: 'highArea',
  greenness_level_unknown_area_m2: 'unknownArea',
  greenness_level_not_applicable_area_m2: 'notApplicableArea',
};

const defaultInput: GreennessLevelMetricData = {
  greenness_level_bare_area_m2: 11_000_000,
  greenness_level_low_area_m2: 21_000_000,
  greenness_level_moderate_area_m2: 31_000_000,
  greenness_level_high_area_m2: 52_000_000,
  greenness_level_unknown_area_m2: 5_000_000,
  greenness_level_not_applicable_area_m2: 10_000_000,
};

const defaultOutputImperial: GreennessLevelMetrics = {
  bareArea: {
    formattedValue: '2.7K',
    unit: 'ac',
    value: 2718.156565656566,
  },
  lowArea: {
    formattedValue: '5.2K',
    unit: 'ac',
    value: 5189.207988980716,
  },
  moderateArea: {
    formattedValue: '7.7K',
    unit: 'ac',
    value: 7660.259412304867,
  },
  highArea: {
    formattedValue: '13K',
    unit: 'ac',
    value: 12849.467401285583,
  },
  unknownArea: {
    formattedValue: '1.2K',
    unit: 'ac',
    value: 1235.5257116620753,
  },
  notApplicableArea: {
    formattedValue: '2.5K',
    unit: 'ac',
    value: 2471.0514233241506,
  },
};

const defaultOutputMetric: GreennessLevelMetrics = {
  bareArea: {
    formattedValue: '1.1K',
    unit: 'ha',
    value: 1100,
  },
  lowArea: {
    formattedValue: '2.1K',
    unit: 'ha',
    value: 2100,
  },
  moderateArea: {
    formattedValue: '3.1K',
    unit: 'ha',
    value: 3100,
  },
  highArea: {
    formattedValue: '5.2K',
    unit: 'ha',
    value: 5200,
  },
  unknownArea: {
    formattedValue: '500',
    unit: 'ha',
    value: 500,
  },
  notApplicableArea: {
    formattedValue: '1K',
    unit: 'ha',
    value: 1000,
  },
};

describe('makeGreennessLevelMetrics', () => {
  it('Should return a GreenLevelMetrics object with converted area values (m2 to ac), given Imperial units system', () => {
    expect(makeGreennessLevelMetrics(MeasurementEnum.ImperialUnits)(defaultInput)).toEqual(
      defaultOutputImperial
    );
  });

  it('Should return a GreenLevelMetrics object with converted area values (m2 to ha), given Metric units system', () => {
    expect(makeGreennessLevelMetrics(MeasurementEnum.MetricUnits)(defaultInput)).toEqual(
      defaultOutputMetric
    );
  });

  it('Should return null when no parameters or only 0 are provided', () => {
    const nullInput: GreennessLevelMetricData = {
      //@ts-expect-error checking all nil vals
      greenness_level_bare_area_m2: null,
      //@ts-expect-error checking all nil vals
      greenness_level_low_area_m2: null,
      //@ts-expect-error checking all nil vals
      greenness_level_moderate_area_m2: null,
      //@ts-expect-error checking all nil vals
      greenness_level_high_area_m2: null,
      //@ts-expect-error checking all nil vals
      greenness_level_unknown_area_m2: null,
      //@ts-expect-error checking all nil vals
      greenness_level_not_applicable_area_m2: null,
    };

    const undefinedInput: GreennessLevelMetricData = {
      greenness_level_bare_area_m2: undefined,
      greenness_level_low_area_m2: undefined,
      greenness_level_moderate_area_m2: undefined,
      greenness_level_high_area_m2: undefined,
      greenness_level_unknown_area_m2: undefined,
      greenness_level_not_applicable_area_m2: undefined,
    };

    const zeroInput: GreennessLevelMetricData = {
      greenness_level_bare_area_m2: 0,
      greenness_level_low_area_m2: 0,
      greenness_level_moderate_area_m2: 0,
      greenness_level_high_area_m2: 0,
      greenness_level_unknown_area_m2: 0,
      greenness_level_not_applicable_area_m2: 0,
    };

    expect(makeGreennessLevelMetrics(MeasurementEnum.MetricUnits)(nullInput)).toEqual(null);
    expect(makeGreennessLevelMetrics(MeasurementEnum.MetricUnits)(undefinedInput)).toEqual(null);
    expect(makeGreennessLevelMetrics(MeasurementEnum.MetricUnits)(zeroInput)).toEqual(null);
    expect(makeGreennessLevelMetrics(MeasurementEnum.MetricUnits)({})).toEqual(null);
  });

  it('Should return null when non finite number parameters are provided', () => {
    getTypedKeys(defaultInput).forEach(key => {
      expect(
        makeGreennessLevelMetrics(MeasurementEnum.MetricUnits)({
          ...defaultInput,
          [key]: NaN,
        })
      ).toEqual(null);
      expect(
        makeGreennessLevelMetrics(MeasurementEnum.MetricUnits)({
          ...defaultInput,
          [key]: Infinity,
        })
      ).toEqual(null);
    });
  });

  it('Should default nil values to 0', () => {
    getTypedKeys(defaultInput).forEach(key => {
      expect(
        makeGreennessLevelMetrics(MeasurementEnum.MetricUnits)({
          ...defaultInput,
          [key]: null,
        })
      ).toEqual({
        ...defaultOutputMetric,
        [responseKeyToMetricKeyMap[key]]: {
          value: 0,
          unit: 'ha',
          formattedValue: '0',
        },
      });
    });
  });

  // TODO: update when BE provided mock greenness values are present
  it.todo(
    'Should return a GreennessLevelMetrics in acres units given KPI response data and an Imperial units system'
  );

  it.todo(
    'Should return a GreennessLevelMetrics in hectares units given KPI response data and a Metric units system'
  );
});
