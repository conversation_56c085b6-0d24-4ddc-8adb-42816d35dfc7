import omit from 'lodash/omit';

import {MeasurementEnum} from '_common/utils/measurement-unit-options';

import type {CoverCropMetricData} from 'containers/si/api/apiTypes';
import {
  COVER_CROP_SUBREGION_EXPECTED_OUTPUT_IMPERIAL_UNITS,
  COVER_CROP_SUBREGION_EXPECTED_OUTPUT_METRIC_UNITS,
  COVER_CROP_SUBREGION_INPUT,
  COVER_CROP_TOP_LEVEL_EXPECTED_OUTPUT_IMPERIAL_UNITS,
  COVER_CROP_TOP_LEVEL_EXPECTED_OUTPUT_METRIC_UNITS,
  COVER_CROP_TOP_LEVEL_INPUT,
  COVER_CROP_ZERO_VALUE_EXPECTED_OUTPUT_METRIC_UNITS,
  COVERCROP_ADOPTION_UNIT_DETAIL_MOCK,
  COVERCROP_IMPERIALUNITS_AREA_UNITDETAIL_MOCK,
  COVERCROP_METRICUNITS_AREA_UNITDETAIL_MOCK,
} from 'containers/si/programs/helpers/__mocks__/covercropMock';
import {
  makeCoverCropExpectedMetrics,
  makeCoverCropExpectedWithNaExclusiveMetrics,
  makeCoverCropMetrics,
  private__covercropAdoptionUnit,
  private__covercropAreaUnit,
  type CoverCropMetrics,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.cover_crop.transformation';

describe('private__covercropAdoptionUnit', () => {
  it('should return the correct cover crop adoption unit independent of Imperial or Metrics system', () => {
    expect(private__covercropAdoptionUnit()).toEqual(COVERCROP_ADOPTION_UNIT_DETAIL_MOCK);
  });
});

describe('private__covercropAreaUnit', () => {
  it('should return the correct cover crop area unit given Imperial or Metrics system', () => {
    expect(private__covercropAreaUnit(MeasurementEnum.ImperialUnits)).toEqual(
      COVERCROP_IMPERIALUNITS_AREA_UNITDETAIL_MOCK
    );
    expect(private__covercropAreaUnit(MeasurementEnum.MetricUnits)).toEqual(
      COVERCROP_METRICUNITS_AREA_UNITDETAIL_MOCK
    );
  });
});

const defaultInput: CoverCropMetricData = {
  cover_cc_adoption_rate_na_inclusive: 0.25,
  cover_cc_adoption_rate_na_inclusive_stddev: 0.005555555,
  cover_cc_adoption_rate: 0.3333333333333333,
  cover_cc_adoption_rate_stddev: 0.004166666666666667,
  cover_cc_area_m2: 10_000_000,
  cover_cc_expected_area_m2: 10_000_000,
  cover_cc_area_m2_stddev: 41_000,
  cover_nocc_area_m2: 20_000_000,
  cover_nocc_expected_area_m2: 20_000_000,
  cover_unknowncc_area_m2: 30_000_000,
  cover_not_applicable_cc_area_m2: 1_000_000,
};

describe('makeCoverCropMetrics', () => {
  const requiredCoverCropMetricKeys: Array<keyof CoverCropMetricData> = [];
  const notRequiredCoverCropMetricKeys = [
    'cover_cc_area_m2',
    'cover_nocc_area_m2',
    'cover_unknowncc_area_m2',
    'cover_not_applicable_cc_area_m2',
  ];

  const defaultOutputImperial: CoverCropMetrics = {
    adoption: {
      formattedValue: '33.3%',
      unit: 'unit-interval',
      value: 0.3333333333333333,
    },
    covercroppedArea: {
      formattedValue: '2.5K',
      unit: 'ac',
      value: 2471.0514233241506,
    },
    totalTrackedArea: {
      formattedValue: '7.4K',
      unit: 'ac',
      value: 7413.154269972451,
    },
    unknownArea: {
      formattedValue: '7.4K',
      unit: 'ac',
      value: 7413.154269972451,
    },
    notApplicableArea: {
      formattedValue: '247',
      unit: 'ac',
      value: 247.10514233241506,
    },
  };

  const defaultOutputMetric: CoverCropMetrics = {
    adoption: {
      formattedValue: '33.3%',
      unit: 'unit-interval',
      value: 0.3333333333333333,
    },
    covercroppedArea: {
      formattedValue: '1K',
      unit: 'ha',
      value: 1000,
    },
    totalTrackedArea: {
      formattedValue: '3K',
      unit: 'ha',
      value: 3000,
    },
    unknownArea: {
      formattedValue: '3K',
      unit: 'ha',
      value: 3000,
    },
    notApplicableArea: {
      formattedValue: '100',
      unit: 'ha',
      value: 100,
    },
  };

  it('Should return a CoverCropMetric in ac given Imperial units system', () => {
    expect(makeCoverCropMetrics(MeasurementEnum.ImperialUnits)(defaultInput)).toEqual(
      defaultOutputImperial
    );
  });

  it('Should return a CoverCropMetric in ha given Metric units system', () => {
    expect(makeCoverCropMetrics(MeasurementEnum.MetricUnits)(defaultInput)).toEqual(
      defaultOutputMetric
    );
  });

  it('Should return null when nil parameters are provided for cover crop or no cover crop areas', () => {
    requiredCoverCropMetricKeys.forEach(key => {
      expect(
        makeCoverCropMetrics(MeasurementEnum.MetricUnits)({
          ...defaultInput,
          [key]: null,
        })
      ).toEqual(null);
      expect(
        makeCoverCropMetrics(MeasurementEnum.MetricUnits)({
          ...defaultInput,
          [key]: undefined,
        })
      ).toEqual(null);
      expect(
        makeCoverCropMetrics(MeasurementEnum.MetricUnits)({
          ...omit(defaultInput, key),
        })
      ).toEqual(null);
    });
  });

  it('Should return null when non finite parameters are provided', () => {
    [...requiredCoverCropMetricKeys, ...notRequiredCoverCropMetricKeys].forEach(key => {
      expect(
        makeCoverCropMetrics(MeasurementEnum.MetricUnits)({
          ...defaultInput,
          [key]: NaN,
        })
      ).toEqual(null);
      expect(
        makeCoverCropMetrics(MeasurementEnum.MetricUnits)({
          ...defaultInput,
          [key]: Infinity,
        })
      ).toEqual(null);
    });
  });

  it('Should return null when 0 is provided for both cover_cc_area_m2, cover_nocc_area_m2', () => {
    expect(
      makeCoverCropMetrics(MeasurementEnum.MetricUnits)({
        ...defaultInput,
        cover_cc_area_m2: 0,
        cover_nocc_area_m2: 0,
      })
    ).toEqual(null);
  });

  it('Should correctly return 0% adoption when 0 is provided as cover_cc_area_m2 values', () => {
    expect(
      makeCoverCropMetrics(MeasurementEnum.MetricUnits)({
        cover_cc_area_m2: 0,
        cover_nocc_area_m2: 10_000,
        cover_unknowncc_area_m2: 0,
        cover_not_applicable_cc_area_m2: 0,
      })
    ).toEqual(COVER_CROP_ZERO_VALUE_EXPECTED_OUTPUT_METRIC_UNITS);
  });

  it('Should correctly return 100% adoption when 0 is provided as cover_nocc_area_m2 values', () => {
    const expected: CoverCropMetrics = {
      adoption: {
        formattedValue: '100%',
        unit: 'unit-interval',
        value: 1,
      },
      covercroppedArea: {
        formattedValue: '1',
        unit: 'ha',
        value: 1,
      },
      totalTrackedArea: {
        formattedValue: '1',
        unit: 'ha',
        value: 1,
      },
      unknownArea: {
        formattedValue: '0',
        unit: 'ha',
        value: 0,
      },
      notApplicableArea: {
        formattedValue: '0',
        unit: 'ha',
        value: 0,
      },
    };

    expect(
      makeCoverCropMetrics(MeasurementEnum.MetricUnits)({
        cover_cc_area_m2: 10_000,
        cover_nocc_area_m2: 0,
        cover_unknowncc_area_m2: 0,
        cover_not_applicable_cc_area_m2: 0,
      })
    ).toEqual(expected);
  });

  it('should not require either cover_unknowncc_area_m2 or cover_not_applicable_cc_area_m2', () => {
    notRequiredCoverCropMetricKeys.forEach(key => {
      expect(
        makeCoverCropMetrics(MeasurementEnum.MetricUnits)({
          ...defaultInput,
          [key]: undefined,
        })
      ).not.toBeNull();
      expect(
        makeCoverCropMetrics(MeasurementEnum.MetricUnits)({
          ...defaultInput,
          [key]: null,
        })
      ).not.toBeNull();
      expect(
        makeCoverCropMetrics(MeasurementEnum.MetricUnits)({
          ...omit(defaultInput, key),
        })
      ).not.toBeNull();
    });
  });

  it('Should return a CoverCropMetric in acres units given KPI response data and an Imperial units system', () => {
    expect(makeCoverCropMetrics(MeasurementEnum.ImperialUnits)(COVER_CROP_TOP_LEVEL_INPUT)).toEqual(
      COVER_CROP_TOP_LEVEL_EXPECTED_OUTPUT_IMPERIAL_UNITS
    );
    expect(makeCoverCropMetrics(MeasurementEnum.ImperialUnits)(COVER_CROP_SUBREGION_INPUT)).toEqual(
      COVER_CROP_SUBREGION_EXPECTED_OUTPUT_IMPERIAL_UNITS
    );
  });

  it('Should return a CoverCropMetric in hectares units given KPI response data and a Metric units system', () => {
    expect(makeCoverCropMetrics(MeasurementEnum.MetricUnits)(COVER_CROP_TOP_LEVEL_INPUT)).toEqual(
      COVER_CROP_TOP_LEVEL_EXPECTED_OUTPUT_METRIC_UNITS
    );
    expect(makeCoverCropMetrics(MeasurementEnum.MetricUnits)(COVER_CROP_SUBREGION_INPUT)).toEqual(
      COVER_CROP_SUBREGION_EXPECTED_OUTPUT_METRIC_UNITS
    );
  });
});

describe('makeCoverCropExpectedMetrics', () => {
  const requiredCoverCropMetricKeys: Array<keyof CoverCropMetricData> = [
    'cover_cc_adoption_rate',
    'cover_cc_adoption_rate_stddev',
  ];
  const notRequiredCoverCropMetricKeys = [
    'cover_cc_area_m2_stddev',
    'cover_cc_expected_area_m2',
    'cover_nocc_expected_area_m2',
    'cover_unknowncc_area_m2',
    'cover_not_applicable_cc_area_m2',
  ];

  const defaultOutputImperial: CoverCropMetrics = {
    adoption: {
      formattedValue: '33.3%',
      unit: 'unit-interval',
      value: 0.3333333333333333,
    },
    adoptionStdDev: {
      formattedValue: '0.4%',
      unit: 'unit-interval',
      value: 0.004166666666666667,
    },
    covercroppedArea: {
      formattedValue: '2.5K',
      unit: 'ac',
      value: 2471.0514233241506,
    },
    covercroppedAreaStdDev: {
      formattedValue: '10',
      unit: 'ac',
      value: 10.131310835629016,
    },
    totalTrackedArea: {
      formattedValue: '7.4K',
      unit: 'ac',
      value: 7413.154269972451,
    },
    unknownArea: {
      formattedValue: '7.4K',
      unit: 'ac',
      value: 7413.154269972451,
    },
    notApplicableArea: {
      formattedValue: '247',
      unit: 'ac',
      value: 247.10514233241506,
    },
  };

  const defaultOutputMetric: CoverCropMetrics = {
    adoption: {
      formattedValue: '33.3%',
      unit: 'unit-interval',
      value: 0.3333333333333333,
    },
    adoptionStdDev: {
      formattedValue: '0.4%',
      unit: 'unit-interval',
      value: 0.004166666666666667,
    },
    covercroppedArea: {
      formattedValue: '1K',
      unit: 'ha',
      value: 1000,
    },
    covercroppedAreaStdDev: {
      formattedValue: '4.1',
      unit: 'ha',
      value: 4.1,
    },
    totalTrackedArea: {
      formattedValue: '3K',
      unit: 'ha',
      value: 3000,
    },
    unknownArea: {
      formattedValue: '3K',
      unit: 'ha',
      value: 3000,
    },
    notApplicableArea: {
      formattedValue: '100',
      unit: 'ha',
      value: 100,
    },
  };

  it('Should return a CoverCropMetric in ac given Imperial units system', () => {
    expect(makeCoverCropExpectedMetrics(MeasurementEnum.ImperialUnits)(defaultInput)).toEqual(
      defaultOutputImperial
    );
  });

  it('Should return a CoverCropMetric in ha given Metric units system', () => {
    expect(makeCoverCropExpectedMetrics(MeasurementEnum.MetricUnits)(defaultInput)).toEqual(
      defaultOutputMetric
    );
  });

  it('Should return null when nil parameters are provided for cover crop or no cover crop areas', () => {
    requiredCoverCropMetricKeys.forEach(key => {
      expect(
        makeCoverCropExpectedMetrics(MeasurementEnum.MetricUnits)({
          ...defaultInput,
          [key]: null,
        })
      ).toEqual(null);
      expect(
        makeCoverCropExpectedMetrics(MeasurementEnum.MetricUnits)({
          ...defaultInput,
          [key]: undefined,
        })
      ).toEqual(null);
      expect(
        makeCoverCropExpectedMetrics(MeasurementEnum.MetricUnits)({
          ...omit(defaultInput, key),
        })
      ).toEqual(null);
    });
  });

  it('Should return null when non finite parameters are provided', () => {
    [...requiredCoverCropMetricKeys, ...notRequiredCoverCropMetricKeys].forEach(key => {
      expect(
        makeCoverCropExpectedMetrics(MeasurementEnum.MetricUnits)({
          ...defaultInput,
          [key]: NaN,
        })
      ).toEqual(null);
      expect(
        makeCoverCropExpectedMetrics(MeasurementEnum.MetricUnits)({
          ...defaultInput,
          [key]: Infinity,
        })
      ).toEqual(null);
    });
  });

  it('Should return null when 0 parameters are provided for cover_cc_expected_area_m2 and cover_nocc_expected_area_m2', () => {
    expect(
      makeCoverCropExpectedMetrics(MeasurementEnum.MetricUnits)({
        ...defaultInput,
        cover_cc_expected_area_m2: 0,
        cover_nocc_expected_area_m2: 0,
      })
    ).toEqual(null);
  });

  it('should not require either cover_unknowncc_area_m2 or cover_not_applicable_cc_area_m2', () => {
    notRequiredCoverCropMetricKeys.forEach(key => {
      expect(
        makeCoverCropExpectedMetrics(MeasurementEnum.MetricUnits)({
          ...defaultInput,
          [key]: undefined,
        })
      ).not.toBeNull();
      expect(
        makeCoverCropExpectedMetrics(MeasurementEnum.MetricUnits)({
          ...defaultInput,
          [key]: null,
        })
      ).not.toBeNull();
      expect(
        makeCoverCropExpectedMetrics(MeasurementEnum.MetricUnits)({
          ...omit(defaultInput, key),
        })
      ).not.toBeNull();
    });
  });

  // TODO: SI-3064 udpate depending on expected value roll out and KPICoverCropMock updates
  it.todo(
    'Should return a CoverCropMetric in acres units given KPI response data and an Imperial units system'
  );

  it.todo(
    'Should return a CoverCropMetric in hectares units given KPI response data and a Metric units system'
  );
});

describe('makeCoverCropExpectedWithNaExclusiveMetrics', () => {
  const requiredCoverCropMetricKeys: Array<keyof CoverCropMetricData> = [
    'cover_cc_adoption_rate_na_inclusive',
    'cover_cc_adoption_rate_na_inclusive_stddev', // required unless cover_cc_adoption_rate_na_inclusive is 0
  ];
  const notRequiredCoverCropMetricKeys = [
    'cover_cc_expected_area_m2',
    'cover_cc_area_m2_stddev',
    'cover_nocc_expected_area_m2',
    'cover_cc_adoption_rate',
    'cover_cc_adoption_rate_stddev',
    'cover_unknowncc_area_m2',
    'cover_not_applicable_cc_area_m2',
  ];

  const defaultOutputImperial: CoverCropMetrics = {
    adoption: {
      formattedValue: '25%',
      unit: 'unit-interval',
      value: 0.25,
    },
    adoptionStdDev: {
      formattedValue: '0.6%',
      unit: 'unit-interval',
      value: 0.005555555,
    },
    totalTrackedArea: {
      formattedValue: '7.7K',
      unit: 'ac',
      value: 7660.259412304867,
    },
    adoptionNotApplicableExclusive: {
      formattedValue: '33.3%',
      unit: 'unit-interval',
      value: 0.3333333333333333,
    },
    adoptionStdDevNotApplicableExclusive: {
      formattedValue: '0.4%',
      unit: 'unit-interval',
      value: 0.004166666666666667,
    },
    totalTrackedAreaNaExclusive: {
      formattedValue: '7.4K',
      unit: 'ac',
      value: 7413.154269972451,
    },
    covercroppedArea: {
      formattedValue: '2.5K',
      unit: 'ac',
      value: 2471.0514233241506,
    },
    covercroppedAreaStdDev: {
      formattedValue: '10',
      unit: 'ac',
      value: 10.131310835629016,
    },
    unknownArea: {
      formattedValue: '7.4K',
      unit: 'ac',
      value: 7413.154269972451,
    },
    notApplicableArea: {
      formattedValue: '247',
      unit: 'ac',
      value: 247.10514233241506,
    },
  };

  const defaultOutputMetric: CoverCropMetrics = {
    adoption: {
      formattedValue: '25%',
      unit: 'unit-interval',
      value: 0.25,
    },
    adoptionStdDev: {
      formattedValue: '0.6%',
      unit: 'unit-interval',
      value: 0.005555555,
    },
    totalTrackedArea: {
      formattedValue: '3.1K',
      unit: 'ha',
      value: 3100,
    },
    adoptionNotApplicableExclusive: {
      formattedValue: '33.3%',
      unit: 'unit-interval',
      value: 0.3333333333333333,
    },
    adoptionStdDevNotApplicableExclusive: {
      formattedValue: '0.4%',
      unit: 'unit-interval',
      value: 0.004166666666666667,
    },
    totalTrackedAreaNaExclusive: {
      formattedValue: '3K',
      unit: 'ha',
      value: 3000,
    },
    covercroppedArea: {
      formattedValue: '1K',
      unit: 'ha',
      value: 1000,
    },
    covercroppedAreaStdDev: {
      formattedValue: '4.1',
      unit: 'ha',
      value: 4.1,
    },
    unknownArea: {
      formattedValue: '3K',
      unit: 'ha',
      value: 3000,
    },
    notApplicableArea: {
      formattedValue: '100',
      unit: 'ha',
      value: 100,
    },
  };

  it('Should return a CoverCropMetric in ac given Imperial units system', () => {
    expect(
      makeCoverCropExpectedWithNaExclusiveMetrics(MeasurementEnum.ImperialUnits)(defaultInput)
    ).toEqual(defaultOutputImperial);
  });

  it('Should return a CoverCropMetric in ha given Metric units system', () => {
    expect(
      makeCoverCropExpectedWithNaExclusiveMetrics(MeasurementEnum.MetricUnits)(defaultInput)
    ).toEqual(defaultOutputMetric);
  });

  it('Should return null when nil parameters are provided for required keys', () => {
    requiredCoverCropMetricKeys.forEach(key => {
      expect(
        makeCoverCropExpectedWithNaExclusiveMetrics(MeasurementEnum.MetricUnits)({
          ...defaultInput,
          [key]: null,
        })
      ).toEqual(null);
      expect(
        makeCoverCropExpectedWithNaExclusiveMetrics(MeasurementEnum.MetricUnits)({
          ...defaultInput,
          [key]: undefined,
        })
      ).toEqual(null);
      expect(
        makeCoverCropExpectedWithNaExclusiveMetrics(MeasurementEnum.MetricUnits)({
          ...omit(defaultInput, key),
        })
      ).toEqual(null);
    });
  });

  it('Should return null when non finite parameters are provided', () => {
    [...requiredCoverCropMetricKeys, ...notRequiredCoverCropMetricKeys].forEach(key => {
      expect(
        makeCoverCropExpectedWithNaExclusiveMetrics(MeasurementEnum.MetricUnits)({
          ...defaultInput,
          [key]: NaN,
        })
      ).toEqual(null);
      expect(
        makeCoverCropExpectedWithNaExclusiveMetrics(MeasurementEnum.MetricUnits)({
          ...defaultInput,
          [key]: Infinity,
        })
      ).toEqual(null);
    });
  });

  it('Should return null when 0 parameters are provided for cover_cc_expected_area_m2 and cover_nocc_expected_area_m2 and cover_not_applicable_cc_area_m2', () => {
    expect(
      makeCoverCropExpectedWithNaExclusiveMetrics(MeasurementEnum.MetricUnits)({
        ...defaultInput,
        cover_cc_expected_area_m2: 0,
        cover_nocc_expected_area_m2: 0,
        cover_not_applicable_cc_area_m2: undefined,
      })
    ).toEqual(null);
    expect(
      makeCoverCropExpectedWithNaExclusiveMetrics(MeasurementEnum.MetricUnits)({
        ...defaultInput,
        cover_cc_expected_area_m2: 0,
        cover_nocc_expected_area_m2: 0,
        //@ts-expect-error checking all nil values
        cover_not_applicable_cc_area_m2: null,
      })
    ).toEqual(null);
    expect(
      makeCoverCropExpectedWithNaExclusiveMetrics(MeasurementEnum.MetricUnits)({
        ...defaultInput,
        cover_cc_expected_area_m2: 0,
        cover_nocc_expected_area_m2: 0,
        cover_not_applicable_cc_area_m2: 0,
      })
    ).toEqual(null);
  });

  it('should not require non-required keys', () => {
    notRequiredCoverCropMetricKeys.forEach(key => {
      expect(
        makeCoverCropExpectedWithNaExclusiveMetrics(MeasurementEnum.MetricUnits)({
          ...defaultInput,
          [key]: undefined,
        })
      ).not.toBeNull();
      expect(
        makeCoverCropExpectedWithNaExclusiveMetrics(MeasurementEnum.MetricUnits)({
          ...defaultInput,
          [key]: null,
        })
      ).not.toBeNull();
      expect(
        makeCoverCropExpectedWithNaExclusiveMetrics(MeasurementEnum.MetricUnits)({
          ...omit(defaultInput, key),
        })
      ).not.toBeNull();
    });
  });

  it('should return null for adoption na exclusive and adoption std dev na exclusive if cover_cc_adoption_rate is nil', () => {
    expect(
      makeCoverCropExpectedWithNaExclusiveMetrics(MeasurementEnum.MetricUnits)({
        ...defaultInput,
        //@ts-expect-error checking all nil values
        cover_cc_adoption_rate: null,
      })
    ).toEqual({
      ...defaultOutputMetric,
      adoptionNotApplicableExclusive: null,
      adoptionStdDevNotApplicableExclusive: null,
    });

    expect(
      makeCoverCropExpectedWithNaExclusiveMetrics(MeasurementEnum.MetricUnits)({
        ...defaultInput,
        cover_cc_adoption_rate: undefined,
      })
    ).toEqual({
      ...defaultOutputMetric,
      adoptionNotApplicableExclusive: null,
      adoptionStdDevNotApplicableExclusive: null,
    });
  });

  it('should coalesce missing cover_cc_adoption_rate_na_inclusive_stddev to 0 if cover_cc_adoption_rate_na_inclusive is 0', () => {
    expect(
      makeCoverCropExpectedWithNaExclusiveMetrics(MeasurementEnum.MetricUnits)({
        ...defaultInput,
        cover_cc_adoption_rate_na_inclusive: 0,
        cover_cc_adoption_rate_na_inclusive_stddev: undefined,
      })
    ).toEqual({
      ...defaultOutputMetric,
      adoption: {formattedValue: '0%', unit: 'unit-interval', value: 0},
      adoptionStdDev: {formattedValue: '0%', unit: 'unit-interval', value: 0},
    });
    expect(
      makeCoverCropExpectedWithNaExclusiveMetrics(MeasurementEnum.MetricUnits)({
        ...defaultInput,
        cover_cc_adoption_rate_na_inclusive: 0,
        //@ts-expect-error checking all nil values
        cover_cc_adoption_rate_na_inclusive_stddev: null,
      })
    ).toEqual({
      ...defaultOutputMetric,
      adoption: {formattedValue: '0%', unit: 'unit-interval', value: 0},
      adoptionStdDev: {formattedValue: '0%', unit: 'unit-interval', value: 0},
    });
    expect(
      makeCoverCropExpectedWithNaExclusiveMetrics(MeasurementEnum.MetricUnits)({
        ...defaultInput,
        //@ts-expect-error checking all nil values
        cover_cc_adoption_rate_na_inclusive_stddev: null,
      })
    ).toEqual(null);
  });

  it('should coalesce missing cover_cc_adoption_rate_stddev to 0 if cover_cc_adoption_rate is defined', () => {
    expect(
      makeCoverCropExpectedWithNaExclusiveMetrics(MeasurementEnum.MetricUnits)({
        ...defaultInput,
        cover_cc_adoption_rate_stddev: undefined,
      })
    ).toEqual({
      ...defaultOutputMetric,
      adoptionStdDevNotApplicableExclusive: {formattedValue: '0%', unit: 'unit-interval', value: 0},
    });
    expect(
      makeCoverCropExpectedWithNaExclusiveMetrics(MeasurementEnum.MetricUnits)({
        ...defaultInput,
        //@ts-expect-error checking all nil values
        cover_cc_adoption_rate_stddev: null,
      })
    ).toEqual({
      ...defaultOutputMetric,
      adoptionStdDevNotApplicableExclusive: {formattedValue: '0%', unit: 'unit-interval', value: 0},
    });
    expect(
      makeCoverCropExpectedWithNaExclusiveMetrics(MeasurementEnum.MetricUnits)({
        ...defaultInput,
        cover_cc_adoption_rate: undefined,
        cover_cc_adoption_rate_stddev: undefined,
      })
    ).toEqual({
      ...defaultOutputMetric,
      adoptionNotApplicableExclusive: null,
      adoptionStdDevNotApplicableExclusive: null,
    });
  });

  it('should make N/A-exclusive metrics undefined if N/A area is nil or 0 or N/A-exclusive area is 0', () => {
    expect(
      makeCoverCropExpectedWithNaExclusiveMetrics(MeasurementEnum.MetricUnits)({
        ...defaultInput,
        cover_not_applicable_cc_area_m2: undefined,
      })
    ).toEqual({
      ...defaultOutputMetric,
      totalTrackedArea: {formattedValue: '3K', unit: 'ha', value: 3000},
      notApplicableArea: {formattedValue: '0', unit: 'ha', value: 0},
      adoptionNotApplicableExclusive: undefined,
      adoptionStdDevNotApplicableExclusive: undefined,
    });
    expect(
      makeCoverCropExpectedWithNaExclusiveMetrics(MeasurementEnum.MetricUnits)({
        ...defaultInput,
        cover_not_applicable_cc_area_m2: 0,
      })
    ).toEqual({
      ...defaultOutputMetric,
      totalTrackedArea: {formattedValue: '3K', unit: 'ha', value: 3000},
      notApplicableArea: {formattedValue: '0', unit: 'ha', value: 0},
      adoptionNotApplicableExclusive: undefined,
      adoptionStdDevNotApplicableExclusive: undefined,
    });
    expect(
      makeCoverCropExpectedWithNaExclusiveMetrics(MeasurementEnum.MetricUnits)({
        ...defaultInput,
        //@ts-expect-error checking all nil values
        cover_not_applicable_cc_area_m2: null,
      })
    ).toEqual({
      ...defaultOutputMetric,
      totalTrackedArea: {formattedValue: '3K', unit: 'ha', value: 3000},
      notApplicableArea: {formattedValue: '0', unit: 'ha', value: 0},
      adoptionNotApplicableExclusive: undefined,
      adoptionStdDevNotApplicableExclusive: undefined,
    });
    expect(
      makeCoverCropExpectedWithNaExclusiveMetrics(MeasurementEnum.MetricUnits)({
        ...defaultInput,
        cover_cc_expected_area_m2: 0,
        cover_cc_area_m2_stddev: 0,
        cover_nocc_expected_area_m2: 0,
      })
    ).toEqual({
      ...defaultOutputMetric,
      covercroppedArea: {formattedValue: '0', unit: 'ha', value: 0},
      covercroppedAreaStdDev: {formattedValue: '0', unit: 'ha', value: 0},
      totalTrackedArea: {formattedValue: '100', unit: 'ha', value: 100},
      totalTrackedAreaNaExclusive: {formattedValue: '0', unit: 'ha', value: 0},
      adoptionNotApplicableExclusive: undefined,
      adoptionStdDevNotApplicableExclusive: undefined,
    });
  });

  // TODO: SI-3064 udpate depending on expected value roll out and KPICoverCropMock updates
  it.todo(
    'Should return a CoverCropMetric in acres units given KPI response data and an Imperial units system'
  );

  it.todo(
    'Should return a CoverCropMetric in hectares units given KPI response data and a Metric units system'
  );
});
