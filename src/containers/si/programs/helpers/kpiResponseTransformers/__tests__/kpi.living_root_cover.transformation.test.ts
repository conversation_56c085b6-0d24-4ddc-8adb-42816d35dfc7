import {
  KPILivingRootCoverAnnualizedSummaryMock,
  KPILivingRootCoverMock,
} from 'containers/si/__mocks__/KPILivingRootCoverMock';
import {
  LIVING_ROOT_COVER_DAYS_UNIT,
  makeAllLivingRootCoverDaysMetricsFromMetrics,
  makeLivingRootCoverMetrics,
  private__livingRootCoverPercentOfMonthUnit,
  private__livingRootCoverPercentOfYearUnit,
  type LivingRootCoverDaysMetrics,
  type LivingRootCoverMetrics,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.living_root_cover.transformation';

const topLevelInput = KPILivingRootCoverMock.metric;

const topLevelOutput: LivingRootCoverMetrics = {
  yearAvg: {formattedValue: '40.4%', unit: 'unit-interval', value: 0.4038049564573703},
  Jan: {value: 0.14929201718873605, unit: 'unit-interval', formattedValue: '14.9%'},
  Feb: {value: 0.12798224943535247, unit: 'unit-interval', formattedValue: '12.8%'},
  Mar: {value: 0.16722227017952176, unit: 'unit-interval', formattedValue: '16.7%'},
  Apr: {value: 0.22927805002321833, unit: 'unit-interval', formattedValue: '22.9%'},
  May: {value: 0.3515736689876396, unit: 'unit-interval', formattedValue: '35.2%'},
  Jun: {value: 0.6982591768731766, unit: 'unit-interval', formattedValue: '69.8%'},
  Jul: {value: 0.8143771879863759, unit: 'unit-interval', formattedValue: '81.4%'},
  Aug: {value: 0.7218464650577425, unit: 'unit-interval', formattedValue: '72.2%'},
  Sep: {value: 0.6608435581522442, unit: 'unit-interval', formattedValue: '66.1%'},
  Oct: {value: 0.39747249972903287, unit: 'unit-interval', formattedValue: '39.7%'},
  Nov: {value: 0.3117938898219021, unit: 'unit-interval', formattedValue: '31.2%'},
  Dec: {value: 0.2157184440535017, unit: 'unit-interval', formattedValue: '21.6%'},
};

const topLevelDaysOutput: LivingRootCoverDaysMetrics = {
  yearAvgDays: {formattedValue: '147', unit: 'd', value: 147},
  AprDays: {
    formattedValue: '7',
    unit: 'd',
    value: 7,
  },
  AugDays: {
    formattedValue: '22',
    unit: 'd',
    value: 22,
  },
  DecDays: {
    formattedValue: '7',
    unit: 'd',
    value: 7,
  },
  FebDays: {
    formattedValue: '4',
    unit: 'd',
    value: 4,
  },
  JanDays: {
    formattedValue: '5',
    unit: 'd',
    value: 5,
  },
  JulDays: {
    formattedValue: '25',
    unit: 'd',
    value: 25,
  },
  JunDays: {
    formattedValue: '21',
    unit: 'd',
    value: 21,
  },
  MarDays: {
    formattedValue: '5',
    unit: 'd',
    value: 5,
  },
  MayDays: {
    formattedValue: '11',
    unit: 'd',
    value: 11,
  },
  NovDays: {
    formattedValue: '9',
    unit: 'd',
    value: 9,
  },
  OctDays: {
    formattedValue: '12',
    unit: 'd',
    value: 12,
  },
  SepDays: {
    formattedValue: '20',
    unit: 'd',
    value: 20,
  },
};

const twentyNineteenInput = KPILivingRootCoverAnnualizedSummaryMock.annualized_summary[2019]!;

const twentyNineteenOutput: LivingRootCoverMetrics = {
  yearAvg: {formattedValue: '40%', unit: 'unit-interval', value: 0.40019446612462795},
  Jan: {value: 0.17890749115669685, unit: 'unit-interval', formattedValue: '17.9%'},
  Feb: {value: 0.10126881036968122, unit: 'unit-interval', formattedValue: '10.1%'},
  Mar: {value: 0.12888317487693357, unit: 'unit-interval', formattedValue: '12.9%'},
  Apr: {value: 0.21565082479992254, unit: 'unit-interval', formattedValue: '21.6%'},
  May: {value: 0.34077414430756703, unit: 'unit-interval', formattedValue: '34.1%'},
  Jun: {value: 0.5751811072802341, unit: 'unit-interval', formattedValue: '57.5%'},
  Jul: {value: 0.8872524083806398, unit: 'unit-interval', formattedValue: '88.7%'},
  Aug: {value: 0.8072675290040868, unit: 'unit-interval', formattedValue: '80.7%'},
  Sep: {value: 0.7325548686927671, unit: 'unit-interval', formattedValue: '73.3%'},
  Oct: {value: 0.517317835877012, unit: 'unit-interval', formattedValue: '51.7%'},
  Nov: {value: 0.2106440611425436, unit: 'unit-interval', formattedValue: '21.1%'},
  Dec: {value: 0.10663133760745024, unit: 'unit-interval', formattedValue: '10.7%'},
};

describe('private__livingRootCoverPercentOfYearUnit', () => {
  it('should return the correct living root cover percent of year unit independent of Imperial or Metrics system', () => {
    expect(private__livingRootCoverPercentOfYearUnit()).toEqual({
      unit: 'unit-interval' as const,
      unitName: {
        singular: '% of year with cover',
        plural: '% of year with cover',
        abbr: 'yearly coverage',
      },
    });
  });
});

describe('private__livingRootCoverPercentOfMonthUnit', () => {
  it('should return the correct living root cover percent of month unit independent of Imperial or Metrics system', () => {
    expect(private__livingRootCoverPercentOfMonthUnit()).toEqual({
      unit: 'unit-interval' as const,
      unitName: {
        singular: '% of month with cover',
        plural: '% of month with cover',
        abbr: 'monthly coverage',
      },
    });
  });
});

describe('LIVING_ROOT_COVER_DAYS_UNIT', () => {
  it('should return the correct living root cover days unit independent of Imperial or Metrics system', () => {
    expect(LIVING_ROOT_COVER_DAYS_UNIT()).toEqual({
      unit: 'd' as const,
      unitName: {
        singular: 'day',
        plural: 'days',
        abbr: 'days',
      },
    });
  });
});

describe('makeLivingRootCoverMetrics', () => {
  it('Should return null when only null parameters are provided', () => {
    expect(
      makeLivingRootCoverMetrics({
        living_root_cover_1: undefined,
        living_root_cover_2: undefined,
        living_root_cover_3: undefined,
        living_root_cover_4: undefined,
        living_root_cover_5: undefined,
        living_root_cover_6: undefined,
        living_root_cover_7: undefined,
        living_root_cover_8: undefined,
        living_root_cover_9: undefined,
        living_root_cover_10: undefined,
        living_root_cover_11: undefined,
        living_root_cover_12: undefined,
      })
    ).toEqual(null);
    expect(
      makeLivingRootCoverMetrics({
        //@ts-expect-error checking all nil values
        living_root_cover_1: null,
        //@ts-expect-error checking all nil values
        living_root_cover_2: null,
        //@ts-expect-error checking all nil values
        living_root_cover_3: null,
        //@ts-expect-error checking all nil values
        living_root_cover_4: null,
        //@ts-expect-error checking all nil values
        living_root_cover_5: null,
        //@ts-expect-error checking all nil values
        living_root_cover_6: null,
        //@ts-expect-error checking all nil values
        living_root_cover_7: null,
        //@ts-expect-error checking all nil values
        living_root_cover_8: null,
        //@ts-expect-error checking all nil values
        living_root_cover_9: null,
        //@ts-expect-error checking all nil values
        living_root_cover_10: null,
        //@ts-expect-error checking all nil values
        living_root_cover_11: null,
        //@ts-expect-error checking all nil values
        living_root_cover_12: null,
      })
    ).toEqual(null);
    expect(makeLivingRootCoverMetrics({})).toEqual(null);
  });

  it('Should not return null on partial nil', () => {
    expect(
      makeLivingRootCoverMetrics({
        ...topLevelInput,
        living_root_cover_11: undefined,
      })
    ).not.toBeNull();

    expect(
      makeLivingRootCoverMetrics({
        ...topLevelInput,
        // @ts-expect-error checking all nil vals
        living_root_cover_2: null,
      })
    ).not.toBeNull();

    expect(
      makeLivingRootCoverMetrics({
        living_root_cover_1: 2.328596834757658,
        living_root_cover_2: 2.227703564615117,
        living_root_cover_3: 2.5536043748757438,
        living_root_cover_4: 2.7539995780119946,
      })
    ).not.toBeNull();
  });

  it('Should return null when non finite parameters are provided', () => {
    expect(
      makeLivingRootCoverMetrics({
        ...topLevelInput,
        living_root_cover_1: NaN,
      })
    ).toEqual(null);

    expect(
      makeLivingRootCoverMetrics({
        ...topLevelInput,
        living_root_cover_12: Infinity,
      })
    ).toEqual(null);
  });

  it('Should return 0 when 0 provided', () => {
    expect(
      makeLivingRootCoverMetrics({
        ...topLevelInput,
        living_root_cover_3: 0,
      })
    ).toEqual({
      ...topLevelOutput,
      Mar: {
        unit: 'unit-interval',
        value: 0,
        formattedValue: '0%',
      },
      yearAvg: {formattedValue: '39%', unit: 'unit-interval', value: 0.3898697672757436},
    });
  });

  it('Should return partial data when one value is undefined', () => {
    expect(
      makeLivingRootCoverMetrics({
        ...topLevelInput,
        living_root_cover_10: undefined,
      })
    ).toEqual({
      ...topLevelOutput,
      Oct: null,
      yearAvg: {
        formattedValue: '37.1%',
        unit: 'unit-interval',
        value: 0.3706822481466176,
      },
    });
  });

  it('Should return a LivingRootCoverMetrics output given KPI response data', () => {
    expect(makeLivingRootCoverMetrics(topLevelInput)).toEqual(topLevelOutput);
    expect(makeLivingRootCoverMetrics(twentyNineteenInput)).toEqual(twentyNineteenOutput);
  });
});

describe('fromMetricsMakeLivingRootCoverDaysMetrics', () => {
  it('Should return LivingRootCoverDaysMetrics given LivingRootCoverMetrics', () => {
    expect(makeAllLivingRootCoverDaysMetricsFromMetrics(topLevelOutput)).toEqual(
      topLevelDaysOutput
    );
  });

  it('Should return null when null metrics are provided', () => {
    expect(makeAllLivingRootCoverDaysMetricsFromMetrics(null)).toEqual(null);
    expect(
      makeAllLivingRootCoverDaysMetricsFromMetrics({...topLevelOutput, yearAvg: null})
    ).toMatchObject({
      yearAvgDays: null,
    });
  });
});
