import {KPIGhgEFMock, KPIGhgEFSubsectionSummaryMock} from 'containers/si/__mocks__/KPIGhgEFMock';
import {CO2E} from 'containers/si/constants';
import {
  makeGhgEFMetrics,
  private__ghgEFUnit,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.ghg_emissions_factor.transformation';

const topLevelInput = KPIGhgEFMock.metric;

const subregionInput = KPIGhgEFSubsectionSummaryMock.boundary_summary[874]!;

describe('private__ghgEFUnit', () => {
  it('should return the correct ghg ef unit', () => {
    expect(private__ghgEFUnit()).toEqual({
      unit: 'kg/kg',
      unitName: {
        singular: `kilogram ${CO2E} / kilogram yield`,
        plural: `kilograms ${CO2E} / kilogram yield`,
        abbr: `kg ${CO2E} / kg yield`,
      },
    });
  });
});

describe('makeGhgEFMetrics', () => {
  it('Should return GhgEFMetrics with appropriate units', () => {
    expect(
      makeGhgEFMetrics({
        ghg_emissions_factor: 0.1375152183104355,
        ghg_emissions_factor_stderr: 0.00360232,
      })
    ).toEqual({
      ghgEmissionsPerYield: {
        formattedValue: '0.138',
        unit: 'kg/kg',
        value: 0.1375152183104355,
      },
      ghgEFStdErr: {
        formattedValue: '0.0036',
        unit: 'kg/kg',
        value: 0.00360232,
      },
    });
  });

  it('Should return GhgEFMetrics in appropriate units given 0', () => {
    expect(
      makeGhgEFMetrics({
        ghg_emissions_factor: 0,
      })
    ).toEqual({
      ghgEmissionsPerYield: {
        formattedValue: '0',
        unit: 'kg/kg',
        value: 0,
      },
    });
  });

  it('Should return no ghgEFStdErr when 0 is provided', () => {
    expect(
      makeGhgEFMetrics({
        ghg_emissions_factor: 0.1375152183104355,
        ghg_emissions_factor_stderr: 0,
      })
    ).toEqual({
      ghgEmissionsPerYield: {
        formattedValue: '0.138',
        unit: 'kg/kg',
        value: 0.1375152183104355,
      },
    });
  });

  it('Should return null when null parameters are provided', () => {
    expect(
      makeGhgEFMetrics({
        //@ts-expect-error covering all nil cases
        ghg_emissions_factor: null,
      })
    ).toEqual(null);
    expect(
      makeGhgEFMetrics({
        ghg_emissions_factor: undefined,
      })
    ).toEqual(null);
    expect(makeGhgEFMetrics({})).toEqual(null);
  });

  it('Should return null when non finite parameters are provided', () => {
    expect(
      makeGhgEFMetrics({
        ghg_emissions_factor: Infinity,
      })
    ).toEqual(null);
  });

  it('Should return undefined for ghgEFStdErr when non finite parameter is provided', () => {
    expect(
      makeGhgEFMetrics({
        ghg_emissions_factor: 0.1375152183104355,
        ghg_emissions_factor_stderr: Infinity,
      })
    ).toEqual({
      ghgEmissionsPerYield: {
        formattedValue: '0.138',
        unit: 'kg/kg',
        value: 0.1375152183104355,
      },
    });

    expect(
      makeGhgEFMetrics({
        ghg_emissions_factor: 0.1375152183104355,
        ghg_emissions_factor_stderr: NaN,
      })
    ).toEqual({
      ghgEmissionsPerYield: {
        formattedValue: '0.138',
        unit: 'kg/kg',
        value: 0.1375152183104355,
      },
    });
  });

  it('Should return undefined for ghgEFStdErr when null or undefined parameter is provided', () => {
    expect(
      makeGhgEFMetrics({
        ghg_emissions_factor: 0.1375152183104355,
        //@ts-expect-error covering all nil cases
        ghg_emissions_factor_stderr: null,
      })
    ).toEqual({
      ghgEmissionsPerYield: {
        formattedValue: '0.138',
        unit: 'kg/kg',
        value: 0.1375152183104355,
      },
    });

    expect(
      makeGhgEFMetrics({
        ghg_emissions_factor: 0.1375152183104355,
        ghg_emissions_factor_stderr: undefined,
      })
    ).toEqual({
      ghgEmissionsPerYield: {
        formattedValue: '0.138',
        unit: 'kg/kg',
        value: 0.1375152183104355,
      },
    });
  });

  it('Should return GhgEFMetrics with appropriate units', () => {
    expect(makeGhgEFMetrics(topLevelInput)).toEqual({
      ghgEmissionsPerYield: {
        formattedValue: '0.288',
        unit: 'kg/kg',
        value: 0.2881857938401378,
      },
      ghgEFStdErr: {
        formattedValue: '0.0036',
        unit: 'kg/kg',
        value: 0.00360232,
      },
    });

    expect(makeGhgEFMetrics(subregionInput)).toEqual({
      ghgEmissionsPerYield: {
        formattedValue: '0.447',
        unit: 'kg/kg',
        value: 0.44716238681868603,
      },
      ghgEFStdErr: {
        formattedValue: '0.00559',
        unit: 'kg/kg',
        value: 0.005589028585233575,
      },
    });
  });
});
