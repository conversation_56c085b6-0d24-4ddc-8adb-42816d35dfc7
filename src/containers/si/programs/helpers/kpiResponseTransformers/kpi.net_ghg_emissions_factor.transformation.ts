import {isNil} from '_common/utils/typeGuards';

import type {NetGhgEmissionFactorMetricData} from 'containers/si/api/apiTypes';
import {CO2E} from 'containers/si/constants';
import {getRatioUnits} from 'containers/si/utils/convert';
import {formatAllNumbers} from 'containers/si/utils/formatters/number.format';
import {type Metric, type UnitDetail, type UnitType} from 'containers/si/utils/value.types';

export type NetGhgEFMetrics = {
  netGhgEmissionsPerYield: Metric;
  netGhgEFStdErr?: Metric;
};

export const private__netGhgEFUnit = () =>
  getRatioUnits({
    unit1: 'kg',
    unit2: 'kg',
    subUnit1: CO2E,
    subUnit2: `yield`,
  });

export const NET_GHG_EF_METRICS_FORMATTER_MAP: Record<
  keyof NetGhgEFMetrics,
  (value: number) => string
> = {
  netGhgEmissionsPerYield: formatAllNumbers,
  netGhgEFStdErr: formatAllNumbers,
};

export const NET_GHG_EF_METRICS_UNIT_MAP: Record<
  keyof NetGhgEFMetrics,
  () => UnitDetail<UnitType>
> = {
  netGhgEmissionsPerYield: private__netGhgEFUnit,
  netGhgEFStdErr: private__netGhgEFUnit,
};

/**
 * This should only be used as a transformer alone with one of the standard kpi.transformation.helpers
 * Example usage:
 * const metricData = makeTopLevelMetric(response, makeNetGhgEFMetrics);
 */
export const makeNetGhgEFMetrics = (
  data: NetGhgEmissionFactorMetricData
): NetGhgEFMetrics | null => {
  // These can be removed once a response deserializer and type validator is built
  // https://regrow.atlassian.net/browse/SI-1379
  // This method should handle
  // expected nulls or undefineds and
  // 0 and Finite checks as those are valid numbers
  if (isNil(data.net_ghg_emissions_factor) || !Number.isFinite(data.net_ghg_emissions_factor)) {
    return null;
  }

  const value = data.net_ghg_emissions_factor;
  const stdErrValue =
    isNil(data.net_ghg_emissions_factor_stderr) ||
    !Number.isFinite(data.net_ghg_emissions_factor_stderr) ||
    data.net_ghg_emissions_factor_stderr === 0
      ? undefined
      : data.net_ghg_emissions_factor_stderr;

  const unitDetails = NET_GHG_EF_METRICS_UNIT_MAP.netGhgEmissionsPerYield();

  return {
    netGhgEmissionsPerYield: {
      value,
      unit: unitDetails.unit,
      formattedValue: NET_GHG_EF_METRICS_FORMATTER_MAP.netGhgEmissionsPerYield(value),
    },
    ...(isNil(stdErrValue)
      ? {}
      : {
          netGhgEFStdErr: {
            value: stdErrValue,
            unit: NET_GHG_EF_METRICS_UNIT_MAP.netGhgEFStdErr().unit,
            formattedValue: NET_GHG_EF_METRICS_FORMATTER_MAP.netGhgEFStdErr(stdErrValue),
          },
        }),
  };
};
