import {type MeasurementEnum} from '_common/utils/measurement-unit-options';
import {isNil} from '_common/utils/typeGuards';

import type {CommonRotationsMetricData} from 'containers/si/api/apiTypes';
import {convertValue, getUnit} from 'containers/si/utils/convert';
import {formatAllNumbers} from 'containers/si/utils/formatters/number.format';
import type {Metric, UnitDetail, UnitType} from 'containers/si/utils/value.types';

export type CommonCropRotationMetric = {
  rotationArea: Metric;
};

export const private__commonCropRotationUnit = (unitsSystem: MeasurementEnum) =>
  getUnit(unitsSystem);

export const COMMON_CROP_ROTATION_METRICS_FORMATTER_MAP: Record<
  keyof CommonCropRotationMetric,
  (value: number) => string
> = {
  rotationArea: formatAllNumbers,
};

export const COMMON_CROP_ROTATION_METRICS_UNIT_MAP: Record<
  keyof CommonCropRotationMetric,
  (unitsSystem: MeasurementEnum) => UnitDetail<UnitType>
> = {
  rotationArea: private__commonCropRotationUnit,
};

/**
 * This should only be used as a transformer alone with makeCropRotationsMetricPairs
 * Example usage:
 * const metricData = makeCropRotationsMetricPairs(response, makeCommonCropRota(MeasurementEnum.Metric));
 */
export const makeCommonCropRotationsMetrics =
  (unitsSystem: MeasurementEnum) =>
  (data: CommonRotationsMetricData): CommonCropRotationMetric | null => {
    // These can be removed once a response deserializer and type validator is built
    // https://regrow.atlassian.net/browse/SI-1379
    // This method should handle
    // expected nulls or undefineds and
    // 0 and Finite checks as those are valid numbers
    if (isNil(data.common_crop_rtn_m2) || !Number.isFinite(data.common_crop_rtn_m2)) {
      return null;
    }

    const value = convertValue({value: data.common_crop_rtn_m2, from: 'm2', to: unitsSystem});

    return {
      rotationArea: {
        value,
        unit: COMMON_CROP_ROTATION_METRICS_UNIT_MAP.rotationArea(unitsSystem).unit,
        formattedValue: COMMON_CROP_ROTATION_METRICS_FORMATTER_MAP.rotationArea(value),
      },
    };
  };
