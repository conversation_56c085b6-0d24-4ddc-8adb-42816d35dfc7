import {isNil} from '_common/utils/typeGuards';

import type {PurchaseVolumeKgKPIMetricData} from 'containers/si/api/apiTypes';
import {convertValue, getUnit} from 'containers/si/utils/convert';
import {formatAllNumbers} from 'containers/si/utils/formatters/number.format';
import type {Metric, UnitDetail, UnitType} from 'containers/si/utils/value.types';

export type PurchaseVolumeKgMetrics = {
  volumePurchased: Metric;
};

export const PURCHASEVOLUME_FORMATTER_MAP: Record<
  keyof PurchaseVolumeKgMetrics,
  (value: number) => string
> = {
  volumePurchased: volume => formatAllNumbers(volume),
};

export const private_volumePurchasedUnit = () => getUnit('mt', 'purchased');

export const PURCHASEVOLUMEMETRICS_UNIT_MAP: Record<
  keyof PurchaseVolumeKgMetrics,
  () => UnitDetail<UnitType>
> = {
  volumePurchased: () => getUnit('mt', 'purchased'),
};

export const makePurchaseVolumeMetrics = (
  data: PurchaseVolumeKgKPIMetricData
): PurchaseVolumeKgMetrics | null => {
  if (isNil(data.purchase_volume_kg) || !Number.isFinite(data.purchase_volume_kg)) return null;

  const value = convertValue({
    value: data.purchase_volume_kg,
    from: 'kg',
    to: 'mt',
  });
  const unitDetails = PURCHASEVOLUMEMETRICS_UNIT_MAP.volumePurchased();

  return {
    volumePurchased: {
      value,
      unit: unitDetails.unit,
      formattedValue: PURCHASEVOLUME_FORMATTER_MAP.volumePurchased(value),
    },
  };
};
