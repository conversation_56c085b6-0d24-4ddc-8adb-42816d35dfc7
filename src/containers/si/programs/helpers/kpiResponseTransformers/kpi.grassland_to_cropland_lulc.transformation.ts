import {type MeasurementEnum} from '_common/utils/measurement-unit-options';
import {isNil} from '_common/utils/typeGuards';

import type {GrasslandToCroplandLulcMetricData} from 'containers/si/api/apiTypes';
import {convertValue, getUnit} from 'containers/si/utils/convert';
import {
  formatFloatToPercent,
  formatLargeNumbers,
} from 'containers/si/utils/formatters/number.format';
import type {UnitDetail, UnitType} from 'containers/si/utils/value.types';
import {type Metric} from 'containers/si/utils/value.types';

export type G2cLulcMetrics = {
  convertedArea: Metric;
  totalTrackedArea: Metric; // metric specific crop field area
  conversion: Metric;
};

export const private_g2cLulcAreaUnit = (unitsSystem: MeasurementEnum) => getUnit(unitsSystem);

export const private_g2cLulcConversionUnit = (): UnitDetail<UnitType> => {
  return {
    unit: 'unit-interval' as const,
    unitName: {
      singular: `% of land area converted`,
      plural: `% of land area converted`,
      abbr: '',
    },
  };
};

export const G2CLULCMETRICS_FORMATTER_MAP: Record<keyof G2cLulcMetrics, (value: number) => string> =
  {
    convertedArea: v => formatLargeNumbers(v, {notation: 'compact', maxFractionDigits: 1}),
    totalTrackedArea: v => formatLargeNumbers(v, {notation: 'compact', maxFractionDigits: 1}),
    conversion: value => formatFloatToPercent(value, {sigDigits: 1}),
  };

export const G2CLULCMETRICS_UNIT_MAP: Record<
  keyof G2cLulcMetrics,
  (unitsSystem: MeasurementEnum) => UnitDetail<UnitType>
> = {
  convertedArea: private_g2cLulcAreaUnit,
  totalTrackedArea: private_g2cLulcAreaUnit,
  conversion: private_g2cLulcConversionUnit,
};

/**
 * This should only be used as a transformer alone with one of the standard kpi.transformation.helpers
 * Example usage:
 * const metricData = makeTopLevelMetric(response, makeG2cLulcMetrics);
 */
export const makeG2cLulcMetrics =
  (unitsSystem: MeasurementEnum) =>
  (data: GrasslandToCroplandLulcMetricData | undefined): G2cLulcMetrics | null => {
    // These can be removed once a response deserializer and type validator is built
    // https://regrow.atlassian.net/browse/SI-1379
    // This method should handle
    // expected nulls or undefineds and
    // 0 and Finite checks as those are valid numbers

    const g2c_lulc_conversion_area_m2 = data?.g2c_lulc_conversion_area_m2;
    const g2c_lulc_total_tracked_area_m2 = data?.g2c_lulc_total_tracked_area_m2;

    if (
      !Number.isFinite(g2c_lulc_conversion_area_m2) ||
      !Number.isFinite(g2c_lulc_total_tracked_area_m2) ||
      isNil(g2c_lulc_conversion_area_m2) ||
      isNil(g2c_lulc_total_tracked_area_m2)
    ) {
      return null;
    }

    if (g2c_lulc_total_tracked_area_m2 === 0) return null;

    const g2cLulcConvertedAreaValue = convertValue({
      value: g2c_lulc_conversion_area_m2,
      from: 'm2',
      to: unitsSystem,
    });
    const g2cLulcConvertedFieldAreaUnitDetails = G2CLULCMETRICS_UNIT_MAP.convertedArea(unitsSystem);

    const totalTrackedAreaValue = convertValue({
      value: g2c_lulc_total_tracked_area_m2,
      from: 'm2',
      to: unitsSystem,
    });
    const g2cLulcTotalTrackedAreaUnitDetails =
      G2CLULCMETRICS_UNIT_MAP.totalTrackedArea(unitsSystem);

    const conversionValue = g2c_lulc_conversion_area_m2 / g2c_lulc_total_tracked_area_m2;
    const conversionUnitDetails = G2CLULCMETRICS_UNIT_MAP.conversion(unitsSystem);

    return {
      convertedArea: {
        value: g2cLulcConvertedAreaValue,
        unit: g2cLulcConvertedFieldAreaUnitDetails.unit,
        formattedValue: G2CLULCMETRICS_FORMATTER_MAP.convertedArea(g2cLulcConvertedAreaValue),
      },
      totalTrackedArea: {
        value: totalTrackedAreaValue,
        unit: g2cLulcTotalTrackedAreaUnitDetails.unit,
        formattedValue: G2CLULCMETRICS_FORMATTER_MAP.totalTrackedArea(totalTrackedAreaValue),
      },
      conversion: {
        value: conversionValue,
        unit: conversionUnitDetails.unit,
        formattedValue: G2CLULCMETRICS_FORMATTER_MAP.conversion(conversionValue),
      },
    };
  };
