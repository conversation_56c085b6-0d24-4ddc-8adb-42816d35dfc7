import {type MeasurementEnum} from '_common/utils/measurement-unit-options';
import {isNil} from '_common/utils/typeGuards';

import type {CropAreaKPIMetric} from 'containers/si/api/apiTypes';
import {convertValue, getUnit} from 'containers/si/utils/convert';
import {formatLargeNumbers} from 'containers/si/utils/formatters/number.format';
import {type Metric, type UnitDetail, type UnitType} from 'containers/si/utils/value.types';

export type CropAreaMetrics = {
  crop_area: Metric;
};

export const private__cropAreaUnit = (unitsSystem: MeasurementEnum) => getUnit(unitsSystem);

export const CROPAREAMETRICS_FORMATTER_MAP: Record<
  keyof CropAreaMetrics,
  (value: number) => string
> = {
  crop_area: formatLargeNumbers,
};

export const CROPAREAMETRICS_UNIT_MAP: Record<
  keyof CropAreaMetrics,
  (unitsSystem: MeasurementEnum) => UnitDetail<UnitType>
> = {
  crop_area: private__cropAreaUnit,
};

/**
 * This should only be used as a transformer alone with one of the standard kpi.transformation.helpers
 * Example usage:
 * const metricData = makeTopLevelMetric(response, makeCropAreaMetrics(MeasurementEnum.Metric));
 */
export const makeCropAreaMetrics =
  (unitsSystem: MeasurementEnum) =>
  (data: CropAreaKPIMetric): CropAreaMetrics | null => {
    // These can be removed once a response deserializer and type validator is built
    // https://regrow.atlassian.net/browse/SI-1379
    // This method should handle
    // expected nulls or undefineds and
    // 0 and Finite checks as those are valid numbers
    if (isNil(data.area_m2) || !Number.isFinite(data.area_m2)) {
      return null;
    }

    const value = convertValue({
      value: data.area_m2,
      from: 'm2',
      to: unitsSystem,
    });

    return {
      crop_area: {
        value,
        unit: CROPAREAMETRICS_UNIT_MAP.crop_area(unitsSystem).unit,
        formattedValue: CROPAREAMETRICS_FORMATTER_MAP.crop_area(value),
      },
    };
  };
