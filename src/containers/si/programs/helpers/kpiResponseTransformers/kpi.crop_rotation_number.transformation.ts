import sum from 'lodash/sum';

import {type Theme} from '@regrow-internal/design-system';

import type {MeasurementEnum} from '_common/utils/measurement-unit-options';
import {getTypedValues} from '_common/utils/object';
import {isNil} from '_common/utils/typeGuards';

import type {CropRotationNumberMetricData} from 'containers/si/api/apiTypes';
import {convertValue, getUnit} from 'containers/si/utils/convert';
import {formatAllNumbers} from 'containers/si/utils/formatters/number.format';
import type {Metric, UnitDetail, UnitType} from 'containers/si/utils/value.types';

export type CropRotationNumberMetric = {
  totalCropRotationDataArea: Metric;
  oneCropRotationArea: Metric;
  twoCropRotationArea: Metric;
  threeCropRotationArea: Metric;
};

export const private__cropRotationNumberUnit = (unitsSystem: MeasurementEnum) =>
  getUnit(unitsSystem);

export const CROP_ROTATION_NUMBER_METRICS_FORMATTER_MAP: Record<
  keyof CropRotationNumberMetric,
  (value: number) => string
> = {
  totalCropRotationDataArea: formatAllNumbers,
  oneCropRotationArea: formatAllNumbers,
  twoCropRotationArea: formatAllNumbers,
  threeCropRotationArea: formatAllNumbers,
};

export const CROP_ROTATION_NUMBER_METRICS_UNIT_MAP: Record<
  keyof CropRotationNumberMetric,
  (unitsSystem: MeasurementEnum) => UnitDetail<UnitType>
> = {
  totalCropRotationDataArea: private__cropRotationNumberUnit,
  oneCropRotationArea: private__cropRotationNumberUnit,
  twoCropRotationArea: private__cropRotationNumberUnit,
  threeCropRotationArea: private__cropRotationNumberUnit,
};

export const CROP_ROTATION_NUMBER_METRICS_LABEL_MAP: Record<
  keyof CropRotationNumberMetric,
  {label: string; categoryPaletteColorKey: keyof Theme['palette']['categoryPalette']}
> = {
  oneCropRotationArea: {label: '1 crop', categoryPaletteColorKey: '1'},
  twoCropRotationArea: {label: '2 crops', categoryPaletteColorKey: '2'},
  threeCropRotationArea: {label: '3+ crops', categoryPaletteColorKey: '3'},
  totalCropRotationDataArea: {label: 'total', categoryPaletteColorKey: '0'},
} as const;

/**
 * This should only be used as a transformer alone with one of the standard kpi.transformation.helpers
 * Example usage:
 * const metricData = makeTopLevelMetric(response, makeCropRotationNumberMetric);
 */
export const makeCropRotationNumberMetric =
  (unitsSystem: MeasurementEnum) =>
  (data: CropRotationNumberMetricData): CropRotationNumberMetric | null => {
    const allRotationNumberValues = getTypedValues(data);
    // These can be removed once a response deserializer and type validator is built
    // https://regrow.atlassian.net/browse/SI-1379
    // This method should handle
    // expected nulls or undefineds and
    // 0 and Finite checks as those are valid numbers
    if (
      isNil(data.croprtn_1_m2) ||
      isNil(data.croprtn_2_m2) ||
      isNil(data.croprtn_3_m2) ||
      allRotationNumberValues.some(r => !Number.isFinite(r))
    ) {
      return null;
    }

    const makeRotationValue = (key: keyof CropRotationNumberMetric, rtn_num_m2: number): Metric => {
      const value = convertValue({value: rtn_num_m2, from: 'm2', to: unitsSystem});

      return {
        value,
        unit: CROP_ROTATION_NUMBER_METRICS_UNIT_MAP[key](unitsSystem).unit,
        formattedValue: CROP_ROTATION_NUMBER_METRICS_FORMATTER_MAP[key](value),
      };
    };

    const totalRotationNumberDataM2 = sum(allRotationNumberValues);

    return {
      totalCropRotationDataArea: makeRotationValue(
        'totalCropRotationDataArea',
        totalRotationNumberDataM2
      ),
      oneCropRotationArea: makeRotationValue('oneCropRotationArea', data.croprtn_1_m2),
      twoCropRotationArea: makeRotationValue('twoCropRotationArea', data.croprtn_2_m2),
      threeCropRotationArea: makeRotationValue('threeCropRotationArea', data.croprtn_3_m2),
    };
  };
