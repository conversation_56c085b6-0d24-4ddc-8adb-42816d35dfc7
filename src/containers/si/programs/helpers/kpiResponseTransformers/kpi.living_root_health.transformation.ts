import {getTypedValues} from '_common/utils/object';
import {isDefined, isNil} from '_common/utils/typeGuards';

import type {LivingRootHealthMetricData} from 'containers/si/api/apiTypes';
import type {LivingRootMonthKeys} from 'containers/si/programs/helpers/types';
import {formatAllNumbers} from 'containers/si/utils/formatters/number.format';
import type {Metric, UnitDetail, UnitType} from 'containers/si/utils/value.types';

export type LivingRootHealthMetrics = Record<LivingRootMonthKeys, Metric | null>;

export const private__livingRootHealthUnit: UnitDetail<UnitType> = {
  unit: 'ndvi' as const,
  unitName: {
    singular: 'binned ndvi',
    plural: 'binned ndvi',
    abbr: 'binned ndvi',
  },
};

/**
 * This should only be used as a transformer alone with one of the standard kpi.transformation.helpers
 * Example usage:
 * const metrics = makeTopLevelMetric(response, makeLivingRootHealthMetrics);
 */
export const makeLivingRootHealthMetrics = (
  data: LivingRootHealthMetricData
): LivingRootHealthMetrics | null => {
  const livingRootHealthMonths = getTypedValues(data);

  // These can be removed once a response deserializer and type validator is built
  // https://regrow.atlassian.net/browse/SI-1379
  // This method should handle
  // expected null, undefined, Finite checks as those are valid numbers
  if (
    // Check if ALL values are nil (individual months could be if we do not have data for them)
    livingRootHealthMonths.every(isNil) ||
    // or any value is invalid
    livingRootHealthMonths.some(monthVal => !Number.isFinite(monthVal ?? 0))
  ) {
    return null;
  }

  const makeMetric = (value?: number | null): Metric | null =>
    isDefined(value)
      ? {
          value,
          unit: private__livingRootHealthUnit.unit,
          formattedValue: formatAllNumbers(value),
        }
      : null;

  return {
    Jan: makeMetric(data.living_root_health_1),
    Feb: makeMetric(data.living_root_health_2),
    Mar: makeMetric(data.living_root_health_3),
    Apr: makeMetric(data.living_root_health_4),
    May: makeMetric(data.living_root_health_5),
    Jun: makeMetric(data.living_root_health_6),
    Jul: makeMetric(data.living_root_health_7),
    Aug: makeMetric(data.living_root_health_8),
    Sep: makeMetric(data.living_root_health_9),
    Oct: makeMetric(data.living_root_health_10),
    Nov: makeMetric(data.living_root_health_11),
    Dec: makeMetric(data.living_root_health_12),
  };
};

export const LIVING_ROOT_HEALTH_NDVI_TO_CATEGORY_COLOR_MAP = [
  {inclusiveUpperBound: 0, color: '5', label: 'Bare soil'},
  {inclusiveUpperBound: 2.5, color: '6', label: ''},
  {inclusiveUpperBound: 4, color: '7', label: ''},
  {inclusiveUpperBound: 5, color: '8', label: ''},
  {inclusiveUpperBound: 6, color: '1', label: ''},
  {inclusiveUpperBound: 10000000, color: '0', label: 'Healthy vegetation'},
] as const;

export const getNDVIcolor = (metric: Metric | null) => {
  if (isNil(metric) || metric.unit !== 'ndvi') return null;

  return (
    LIVING_ROOT_HEALTH_NDVI_TO_CATEGORY_COLOR_MAP.find(
      ({inclusiveUpperBound}) => metric.value <= inclusiveUpperBound
    )?.color ?? null
  );
};
