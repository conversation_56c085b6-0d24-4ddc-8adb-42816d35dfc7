import type {MeasurementEnum} from '_common/utils/measurement-unit-options';
import {isNil} from '_common/utils/typeGuards';

import type {GhgKgPerM2MetricData} from 'containers/si/api/apiTypes';
import {CO2E} from 'containers/si/constants';
import {convertValue, getRatioUnits} from 'containers/si/utils/convert';
import {formatAllNumbers} from 'containers/si/utils/formatters/number.format';
import {type Metric, type UnitDetail, type UnitType} from 'containers/si/utils/value.types';

export type GhgMetrics = {
  ghgMassPerArea: Metric;
  ghgStdErr?: Metric;
};

export const private__ghgUnit = (unitsSystem: MeasurementEnum) =>
  getRatioUnits({unit1: 'mt', unit2: unitsSystem, subUnit1: CO2E});

export const GHGMETRICS_FORMATTER_MAP: Record<keyof GhgMetrics, (value: number) => string> = {
  ghgMassPerArea: formatAllNumbers,
  ghgStdErr: formatAllNumbers, // note this should always be the same as the ghgMassPerArea formatter
};

export const GHGMETRICS_UNIT_MAP: Record<
  keyof GhgMetrics,
  (unitsSystem: MeasurementEnum) => UnitDetail<UnitType>
> = {
  ghgMassPerArea: private__ghgUnit,
  ghgStdErr: private__ghgUnit, // note this should always be the same as the ghgMassPerArea unit
};

/**
 * This should only be used as a transformer alone with one of the standard kpi.transformation.helpers
 * Example usage:
 * const metricData = makeTopLevelMetric(response, makeGhgMetrics(MeasurementEnum.Metric));
 */
export const makeGhgMetrics =
  (unitsSystem: MeasurementEnum) =>
  (data: GhgKgPerM2MetricData): GhgMetrics | null => {
    // These can be removed once a response deserializer and type validator is built
    // https://regrow.atlassian.net/browse/SI-1379
    // This method should handle
    // expected nulls or undefineds and
    // 0 and Finite checks as those are valid numbers
    if (isNil(data.ghg_kg_per_m2) || !Number.isFinite(data.ghg_kg_per_m2)) {
      return null;
    }

    const massPerAreaValue =
      convertValue({
        value: data.ghg_kg_per_m2,
        from: 'kg',
        to: 'mt',
      }) / convertValue({value: 1, from: 'm2', to: unitsSystem});

    const stdErrValue =
      isNil(data.ghg_kg_per_m2_stderr) ||
      !Number.isFinite(data.ghg_kg_per_m2_stderr) ||
      data.ghg_kg_per_m2_stderr === 0
        ? undefined
        : convertValue({
            value: data.ghg_kg_per_m2_stderr,
            from: 'kg',
            to: 'mt',
          }) / convertValue({value: 1, from: 'm2', to: unitsSystem});

    return {
      ghgMassPerArea: {
        value: massPerAreaValue,
        unit: GHGMETRICS_UNIT_MAP.ghgMassPerArea(unitsSystem).unit,
        formattedValue: GHGMETRICS_FORMATTER_MAP.ghgMassPerArea(massPerAreaValue),
      },
      ...(isNil(stdErrValue)
        ? {}
        : {
            ghgStdErr: {
              value: stdErrValue,
              unit: GHGMETRICS_UNIT_MAP.ghgStdErr(unitsSystem).unit,
              formattedValue: GHGMETRICS_FORMATTER_MAP.ghgStdErr(stdErrValue),
            },
          }),
    };
  };
