import type {MeasurementEnum} from '_common/utils/measurement-unit-options';
import {getTypedValues} from '_common/utils/object';
import {isDefined, isNil} from '_common/utils/typeGuards';

import type {FertilizerPerAreaMetricData} from 'containers/si/api/apiTypes';
import {convertValue, getRatioUnits} from 'containers/si/utils/convert';
import {formatAllNumbers} from 'containers/si/utils/formatters/number.format';
import {fnIfDefinedElseNull} from 'containers/si/utils/utils';
import {type Metric, type UnitDetail, type UnitType} from 'containers/si/utils/value.types';

export type FertPerAreaMetrics = {
  fertMassPerArea: Metric;
  fertKPerArea: Metric | null;
  fertSPerArea: Metric | null;
  fertNPerArea: Metric | null;
  fertPPerArea: Metric | null;
};

export const private__fertPerAreaUnit = (unitsSystem: MeasurementEnum) =>
  getRatioUnits({unit1: 'kg', subUnit1: 'applied', unit2: unitsSystem});

export const FERT_PER_AREA_METRICS_FORMATTER_MAP: Record<
  keyof FertPerAreaMetrics,
  (value: number) => string
> = {
  fertMassPerArea: formatAllNumbers,
  fertKPerArea: formatAllNumbers,
  fertSPerArea: formatAllNumbers,
  fertNPerArea: formatAllNumbers,
  fertPPerArea: formatAllNumbers,
};

export const FERT_PER_AREA_METRICS_UNIT_MAP: Record<
  keyof FertPerAreaMetrics,
  (unitsSystem: MeasurementEnum) => UnitDetail<UnitType>
> = {
  fertMassPerArea: private__fertPerAreaUnit,
  fertKPerArea: private__fertPerAreaUnit,
  fertSPerArea: private__fertPerAreaUnit,
  fertNPerArea: private__fertPerAreaUnit,
  fertPPerArea: private__fertPerAreaUnit,
};

export const FERT_PER_AREA_METRICS_LABEL_MAP = {
  fertNPerArea: {label: 'nitrogen (N)', categoryPaletteColorKey: '1'},
  fertPPerArea: {label: 'phosphorus (P)', categoryPaletteColorKey: '2'},
  fertKPerArea: {label: 'potassium (K)', categoryPaletteColorKey: '3'},
  fertSPerArea: {label: 'sulfur (S)', categoryPaletteColorKey: '4'},
  fertMassPerArea: {label: 'total', categoryPaletteColorKey: '0'},
} as const;

/**
 * This should only be used as a transformer alone with one of the standard kpi.transformation.helpers
 * Example usage:
 * const metricData = makeTopLevelMetric(response, makeFertPerAreaMetrics(MeasurementEnum.Metric));
 */
export const makeFertPerAreaMetrics =
  (unitsSystem: MeasurementEnum) =>
  (data: FertilizerPerAreaMetricData): FertPerAreaMetrics | null => {
    const fertValues = getTypedValues(data);

    // These can be removed once a response deserializer and type validator is built
    // https://regrow.atlassian.net/browse/SI-1379
    // This method should handle
    // expected null, undefined, Finite checks as those are valid numbers
    if (
      isNil(data.fert_kg_per_m2) ||
      data.fert_kg_per_m2 === 0 ||
      // Check if ALL values are nil (individual chemicals could be if we do not have data for them)
      fertValues.every(isNil) ||
      // or any value is invalid
      fertValues.some(value => !Number.isFinite(value ?? 0))
    ) {
      return null;
    }

    const getValue = (valKgPerM2: number) =>
      valKgPerM2 / convertValue({value: 1, from: 'm2', to: unitsSystem});

    const unitDetails = FERT_PER_AREA_METRICS_UNIT_MAP.fertMassPerArea(unitsSystem);

    const fertMassPerAreaValue = getValue(data.fert_kg_per_m2);
    const fertKPerAreaValue = fnIfDefinedElseNull(data.fert_k_kg_per_m2, getValue);
    const fertSPerAreaValue = fnIfDefinedElseNull(data.fert_s_kg_per_m2, getValue);
    const fertPPerAreaValue = fnIfDefinedElseNull(data.fert_p_kg_per_m2, getValue);
    const fertNPerAreaValue = fnIfDefinedElseNull(data.fert_n_kg_per_m2, getValue);

    return {
      fertMassPerArea: {
        value: fertMassPerAreaValue,
        unit: unitDetails.unit,
        formattedValue: FERT_PER_AREA_METRICS_FORMATTER_MAP.fertMassPerArea(fertMassPerAreaValue),
      },
      fertKPerArea: isDefined(fertKPerAreaValue)
        ? {
            value: fertKPerAreaValue,
            unit: unitDetails.unit,
            formattedValue: FERT_PER_AREA_METRICS_FORMATTER_MAP.fertKPerArea(fertKPerAreaValue),
          }
        : null,
      fertSPerArea: isDefined(fertSPerAreaValue)
        ? {
            value: fertSPerAreaValue,
            unit: unitDetails.unit,
            formattedValue: FERT_PER_AREA_METRICS_FORMATTER_MAP.fertSPerArea(fertSPerAreaValue),
          }
        : null,
      fertPPerArea: isDefined(fertPPerAreaValue)
        ? {
            value: fertPPerAreaValue,
            unit: unitDetails.unit,
            formattedValue: FERT_PER_AREA_METRICS_FORMATTER_MAP.fertPPerArea(fertPPerAreaValue),
          }
        : null,
      fertNPerArea: isDefined(fertNPerAreaValue)
        ? {
            value: fertNPerAreaValue,
            unit: unitDetails.unit,
            formattedValue: FERT_PER_AREA_METRICS_FORMATTER_MAP.fertNPerArea(fertNPerAreaValue),
          }
        : null,
    };
  };
