import {isNil} from '_common/utils/typeGuards';

import type {VolumeWeightedNetEmissionsFactorKPIMetricData} from 'containers/si/api/apiTypes';
import {CO2E} from 'containers/si/constants';
import {getRatioUnits} from 'containers/si/utils/convert';
import {formatAllNumbers} from 'containers/si/utils/formatters/number.format';
import {type Metric, type UnitDetail, type UnitType} from 'containers/si/utils/value.types';

export type VolumeWeightedNetEFMetrics = {
  volumeWeighteNetEmissionsPerYeild: Metric;
};

export const private__netEFUnit = () =>
  getRatioUnits({
    unit1: 'kg',
    unit2: 'kg',
    subUnit1: CO2E,
    subUnit2: `yield`,
  });

export const VOLUMEWEIGHTEDNETEFMETRICS_FORMATTER_MAP: Record<
  keyof VolumeWeightedNetEFMetrics,
  (value: number) => string
> = {
  volumeWeighteNetEmissionsPerYeild: formatAllNumbers,
};

export const VOLUMEWEIGHTEDNETEFMETRICS_UNIT_MAP: Record<
  keyof VolumeWeightedNetEFMetrics,
  () => UnitDetail<UnitType>
> = {
  volumeWeighteNetEmissionsPerYeild: private__netEFUnit,
};

/**
 * This should only be used as a transformer alone with one of the standard kpi.transformation.helpers
 * Example usage:
 * const metricData = makeTopLevelMetric(response, makeVolumeWeightedNetEFMetrics(MeasurementEnum.Metric));
 */
export const makeVolumeWeightedNetEFMetrics = (
  data: VolumeWeightedNetEmissionsFactorKPIMetricData
): VolumeWeightedNetEFMetrics | null => {
  // These can be removed once a response deserializer and type validator is built
  // https://regrow.atlassian.net/browse/SI-1379
  // This method should handle
  // expected nulls or undefineds and
  // 0 and Finite checks as those are valid numbers
  if (
    isNil(data.volume_weighted_net_emissions_factor) ||
    !Number.isFinite(data.volume_weighted_net_emissions_factor)
  ) {
    return null;
  }

  const value = data.volume_weighted_net_emissions_factor;
  const unitDetails = VOLUMEWEIGHTEDNETEFMETRICS_UNIT_MAP.volumeWeighteNetEmissionsPerYeild();

  return {
    volumeWeighteNetEmissionsPerYeild: {
      value,
      unit: unitDetails.unit,
      formattedValue:
        VOLUMEWEIGHTEDNETEFMETRICS_FORMATTER_MAP.volumeWeighteNetEmissionsPerYeild(value),
    },
  };
};
