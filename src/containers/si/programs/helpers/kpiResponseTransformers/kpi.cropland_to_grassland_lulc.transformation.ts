import {type MeasurementEnum} from '_common/utils/measurement-unit-options';
import {isNil} from '_common/utils/typeGuards';

import type {CroplandToGrasslandMetricData} from 'containers/si/api/apiTypes';
import {convertValue, getUnit} from 'containers/si/utils/convert';
import {
  formatFloatToPercent,
  formatLargeNumbers,
} from 'containers/si/utils/formatters/number.format';
import type {UnitDetail, UnitType} from 'containers/si/utils/value.types';
import {type Metric} from 'containers/si/utils/value.types';

export type C2gLulcMetrics = {
  restoredArea: Metric;
  totalTrackedArea: Metric; // metric specific crop field area
  restoration: Metric;
};

export const private__c2gLulcAreaUnit = (unitsSystem: MeasurementEnum) => getUnit(unitsSystem);

export const private__c2gLulcRestorationUnit = (): UnitDetail<UnitType> => {
  return {
    unit: 'unit-interval' as const,
    unitName: {
      singular: `% of land area restored`,
      plural: `% of land area restored`,
      abbr: '',
    },
  };
};

export const C2GLULCMETRICS_FORMATTER_MAP: Record<keyof C2gLulcMetrics, (value: number) => string> =
  {
    restoredArea: v => formatLargeNumbers(v, {notation: 'compact', maxFractionDigits: 1}),
    totalTrackedArea: v => formatLargeNumbers(v, {notation: 'compact', maxFractionDigits: 1}),
    restoration: value => formatFloatToPercent(value, {sigDigits: 1}),
  };

export const C2GLULCMETRICS_UNIT_MAP: Record<
  keyof C2gLulcMetrics,
  (unitsSystem: MeasurementEnum) => UnitDetail<UnitType>
> = {
  restoredArea: private__c2gLulcAreaUnit,
  totalTrackedArea: private__c2gLulcAreaUnit,
  restoration: private__c2gLulcRestorationUnit,
};

/**
 * Do not use directly. This component is only exported for unit testing
 * Use accessors below: makeTopLevelC2gLulcMetrics, makeSubregionKpiSubtypeC2gLulcMetrics
 */
export const makeC2gLulcMetrics =
  (unitsSystem: MeasurementEnum) =>
  (data: CroplandToGrasslandMetricData): C2gLulcMetrics | null => {
    const {c2g_lulc_conversion_area_m2, c2g_lulc_total_tracked_area_m2} = data;
    // These can be removed once a response deserializer and type validator is built
    // https://regrow.atlassian.net/browse/SI-1379
    // This method should handle
    // expected nulls or undefineds and
    // 0 and Finite checks as those are valid numbers
    if (
      !Number.isFinite(c2g_lulc_conversion_area_m2) ||
      !Number.isFinite(c2g_lulc_total_tracked_area_m2) ||
      isNil(c2g_lulc_conversion_area_m2) ||
      isNil(c2g_lulc_total_tracked_area_m2)
    ) {
      return null;
    }

    if (c2g_lulc_total_tracked_area_m2 === 0) return null;

    const c2gLulcRestoredAreaValue = convertValue({
      value: c2g_lulc_conversion_area_m2,
      from: 'm2',
      to: unitsSystem,
    });
    const c2gLulcConvertedFieldAreaUnitDetails = C2GLULCMETRICS_UNIT_MAP.restoredArea(unitsSystem);

    const totalTrackedAreaValue = convertValue({
      value: c2g_lulc_total_tracked_area_m2,
      from: 'm2',
      to: unitsSystem,
    });
    const c2gLulcTotalTrackedAreaUnitDetails =
      C2GLULCMETRICS_UNIT_MAP.totalTrackedArea(unitsSystem);

    const restorationValue = c2g_lulc_conversion_area_m2 / c2g_lulc_total_tracked_area_m2;
    const restorationUnitDetails = C2GLULCMETRICS_UNIT_MAP.restoration(unitsSystem);

    return {
      restoredArea: {
        value: c2gLulcRestoredAreaValue,
        unit: c2gLulcConvertedFieldAreaUnitDetails.unit,
        formattedValue: C2GLULCMETRICS_FORMATTER_MAP.restoredArea(c2gLulcRestoredAreaValue),
      },
      totalTrackedArea: {
        value: totalTrackedAreaValue,
        unit: c2gLulcTotalTrackedAreaUnitDetails.unit,
        formattedValue: C2GLULCMETRICS_FORMATTER_MAP.totalTrackedArea(totalTrackedAreaValue),
      },
      restoration: {
        value: restorationValue,
        unit: restorationUnitDetails.unit,
        formattedValue: C2GLULCMETRICS_FORMATTER_MAP.restoration(restorationValue),
      },
    };
  };
