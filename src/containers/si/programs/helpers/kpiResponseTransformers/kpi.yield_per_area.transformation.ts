import {type MeasurementEnum} from '_common/utils/measurement-unit-options';
import {isNil} from '_common/utils/typeGuards';

import type {YieldKgPerM2MetricData} from 'containers/si/api/apiTypes';
import {convertValue, getRatioUnits} from 'containers/si/utils/convert';
import {formatLargeNumbers} from 'containers/si/utils/formatters/number.format';
import {type Metric, type UnitDetail, type UnitType} from 'containers/si/utils/value.types';

export type YieldPerAreaMetrics = {
  yieldPerArea: Metric;
};

export const private__yieldPerAreaUnit = (unitsSystem: MeasurementEnum) =>
  getRatioUnits({unit1: 'kg', subUnit1: 'yield', unit2: unitsSystem});

export const YIELD_PER_AREA_METRICS_FORMATTER_MAP: Record<
  keyof YieldPerAreaMetrics,
  (value: number) => string
> = {
  yieldPerArea: v => formatLargeNumbers(v, {notation: 'compact', maxFractionDigits: 1}),
};

export const YIELD_PER_AREA_METRICS_UNIT_MAP: Record<
  keyof YieldPerAreaMetrics,
  (unitsSystem: MeasurementEnum) => UnitDetail<UnitType>
> = {
  yieldPerArea: private__yieldPerAreaUnit,
};

/**
 * This should only be used as a transformer alone with one of the standard kpi.transformation.helpers
 * Example usage:
 * const metricData = makeTopLevelMetric(response, makeSocMetrics(MeasurementEnum.Metric));
 */
export const makeYieldPerAreaMetrics =
  (unitsSystem: MeasurementEnum) =>
  (data: YieldKgPerM2MetricData): YieldPerAreaMetrics | null => {
    // These can be removed once a response deserializer and type validator is built
    // https://regrow.atlassian.net/browse/SI-1379
    // This method should handle
    // expected nulls or undefineds and
    // 0 and Finite checks as those are valid numbers
    if (isNil(data.yield_kg_per_m2) || !Number.isFinite(data.yield_kg_per_m2)) {
      return null;
    }

    const value = data.yield_kg_per_m2 / convertValue({value: 1, from: 'm2', to: unitsSystem});

    const unitDetails = YIELD_PER_AREA_METRICS_UNIT_MAP.yieldPerArea(unitsSystem);

    return {
      yieldPerArea: {
        value,
        unit: unitDetails.unit,
        formattedValue: YIELD_PER_AREA_METRICS_FORMATTER_MAP.yieldPerArea(value),
      },
    };
  };
