import {isNil} from '_common/utils/typeGuards';

import type {GhgEmissionFactorMetricData} from 'containers/si/api/apiTypes';
import {CO2E} from 'containers/si/constants';
import {getRatioUnits} from 'containers/si/utils/convert';
import {formatAllNumbers} from 'containers/si/utils/formatters/number.format';
import {type Metric, type UnitDetail, type UnitType} from 'containers/si/utils/value.types';

export type GhgEFMetrics = {
  ghgEmissionsPerYield: Metric;
  ghgEFStdErr?: Metric;
};

export const private__ghgEFUnit = () =>
  getRatioUnits({
    unit1: 'kg',
    unit2: 'kg',
    subUnit1: CO2E,
    subUnit2: `yield`,
  });

export const GHG_EF_METRICS_FORMATTER_MAP: Record<keyof GhgEFMetrics, (value: number) => string> = {
  ghgEmissionsPerYield: formatAllNumbers,
  ghgEFStdErr: formatAllNumbers, // note this should always be the same as the ghgEmissionsPerYield formatter
};

export const GHG_EF_METRICS_UNIT_MAP: Record<keyof GhgEFMetrics, () => UnitDetail<UnitType>> = {
  ghgEmissionsPerYield: private__ghgEFUnit,
  ghgEFStdErr: private__ghgEFUnit, // note this should always be the same as the ghgEmissionsPerYield unit
};

/**
 * This should only be used as a transformer alone with one of the standard kpi.transformation.helpers
 * Example usage:
 * const metricData = makeTopLevelMetric(response, makeGhgEFMetrics);
 */
export const makeGhgEFMetrics = (data: GhgEmissionFactorMetricData): GhgEFMetrics | null => {
  // These can be removed once a response deserializer and type validator is built
  // https://regrow.atlassian.net/browse/SI-1379
  // This method should handle
  // expected nulls or undefineds and
  // 0 and Finite checks as those are valid numbers
  if (isNil(data.ghg_emissions_factor) || !Number.isFinite(data.ghg_emissions_factor)) {
    return null;
  }

  const value = data.ghg_emissions_factor;
  const stdErrValue =
    isNil(data.ghg_emissions_factor_stderr) ||
    !Number.isFinite(data.ghg_emissions_factor_stderr) ||
    data.ghg_emissions_factor_stderr === 0
      ? undefined
      : data.ghg_emissions_factor_stderr;

  const unitDetails = GHG_EF_METRICS_UNIT_MAP.ghgEmissionsPerYield();

  return {
    ghgEmissionsPerYield: {
      value,
      unit: unitDetails.unit,
      formattedValue: GHG_EF_METRICS_FORMATTER_MAP.ghgEmissionsPerYield(value),
    },
    ...(isNil(stdErrValue)
      ? {}
      : {
          ghgEFStdErr: {
            value: stdErrValue,
            unit: GHG_EF_METRICS_UNIT_MAP.ghgEFStdErr().unit,
            formattedValue: GHG_EF_METRICS_FORMATTER_MAP.ghgEFStdErr(stdErrValue),
          },
        }),
  };
};
