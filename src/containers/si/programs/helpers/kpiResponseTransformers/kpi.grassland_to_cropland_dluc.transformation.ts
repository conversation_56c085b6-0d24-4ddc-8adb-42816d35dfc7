import {type MeasurementEnum} from '_common/utils/measurement-unit-options';
import {isNil} from '_common/utils/typeGuards';

import type {GrasslandToCroplandDlucMetricData} from 'containers/si/api/apiTypes';
import {convertValue, getUnit} from 'containers/si/utils/convert';
import {formatLargeNumbers} from 'containers/si/utils/formatters/number.format';
import type {UnitDetail, UnitType} from 'containers/si/utils/value.types';
import {type Metric} from 'containers/si/utils/value.types';

export type G2cDlucMetrics = {
  convertedArea: Metric;
};

export const private__g2cDlucAreaUnit = (unitsSystem: MeasurementEnum) => getUnit(unitsSystem);

export const G2C_DLUC_METRICS_FORMATTER_MAP: Record<
  keyof G2cDlucMetrics,
  (value: number) => string
> = {
  convertedArea: v => formatLargeNumbers(v, {notation: 'compact', maxFractionDigits: 1}),
};

export const G2C_DLUC_METRICS_UNIT_MAP: Record<
  keyof G2cDlucMetrics,
  (unitsSystem: MeasurementEnum) => UnitDetail<UnitType>
> = {
  convertedArea: private__g2cDlucAreaUnit,
};

/**
 * This should only be used as a transformer alone with one of the standard kpi.transformation.helpers
 * Example usage:
 * const metricData = makeTopLevelMetric(response, makeG2cDlucMetrics);
 */
export const makeG2cDlucMetrics =
  (unitsSystem: MeasurementEnum) =>
  (data: GrasslandToCroplandDlucMetricData | undefined): G2cDlucMetrics | null => {
    // These can be removed once a response deserializer and type validator is built
    // https://regrow.atlassian.net/browse/SI-1379
    // This method should handle
    // expected nulls or undefineds and
    // 0 and Finite checks as those are valid numbers

    const g2c_dluc_conversion_area_m2 = data?.g2c_dluc_conversion_area_m2;

    if (!Number.isFinite(g2c_dluc_conversion_area_m2) || isNil(g2c_dluc_conversion_area_m2)) {
      return null;
    }

    const value = convertValue({
      value: g2c_dluc_conversion_area_m2,
      from: 'm2',
      to: unitsSystem,
    });

    const unitDetails = G2C_DLUC_METRICS_UNIT_MAP.convertedArea(unitsSystem);

    return {
      convertedArea: {
        value: value,
        unit: unitDetails.unit,
        formattedValue: G2C_DLUC_METRICS_FORMATTER_MAP.convertedArea(value),
      },
    };
  };
