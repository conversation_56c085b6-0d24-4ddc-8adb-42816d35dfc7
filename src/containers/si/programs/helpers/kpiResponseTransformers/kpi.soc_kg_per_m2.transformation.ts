import {type MeasurementEnum} from '_common/utils/measurement-unit-options';
import {isNil} from '_common/utils/typeGuards';

import type {SocKgPerM2MetricData} from 'containers/si/api/apiTypes';
import {CO2E} from 'containers/si/constants';
import {convertValue, getRatioUnits} from 'containers/si/utils/convert';
import {formatSmallNumbers} from 'containers/si/utils/formatters/number.format';
import {type Metric, type UnitDetail, type UnitType} from 'containers/si/utils/value.types';

export type SocMetrics = {
  dSocMassPerArea: Metric;
  dSocStdErr?: Metric;
};

export const private__socUnit = (unitsSystem: MeasurementEnum) =>
  getRatioUnits({unit1: 'mt', unit2: unitsSystem, subUnit1: CO2E});

export const SOCMETRICS_FORMATTER_MAP: Record<keyof SocMetrics, (value: number) => string> = {
  dSocMassPerArea: formatSmallNumbers,
  dSocStdErr: formatSmallNumbers, // note this should always be the same as the dSocMassPerArea formatter
};

export const SOCMETRICS_UNIT_MAP: Record<
  keyof SocMetrics,
  (unitsSystem: MeasurementEnum) => UnitDetail<UnitType>
> = {
  dSocMassPerArea: private__socUnit,
  dSocStdErr: private__socUnit, // note this should always be the same as the dSocMassPerArea unit
};

/**
 * This should only be used as a transformer alone with one of the standard kpi.transformation.helpers
 * Example usage:
 * const metricData = makeTopLevelMetric(response, makeSocMetrics(MeasurementEnum.Metric));
 */
export const makeSocMetrics =
  (unitsSystem: MeasurementEnum) =>
  (data: SocKgPerM2MetricData): SocMetrics | null => {
    // These can be removed once a response deserializer and type validator is built
    // https://regrow.atlassian.net/browse/SI-1379
    // This method should handle
    // expected nulls or undefineds and
    // 0 and Finite checks as those are valid numbers
    if (isNil(data.soc_kg_per_m2) || !Number.isFinite(data.soc_kg_per_m2)) {
      return null;
    }

    const massPerAreavalue =
      convertValue({
        value: data.soc_kg_per_m2,
        from: 'kg',
        to: 'mt',
      }) / convertValue({value: 1, from: 'm2', to: unitsSystem});

    const stdErrValue =
      isNil(data.soc_kg_per_m2_stderr) ||
      !Number.isFinite(data.soc_kg_per_m2_stderr) ||
      data.soc_kg_per_m2_stderr === 0
        ? undefined
        : convertValue({
            value: data.soc_kg_per_m2_stderr,
            from: 'kg',
            to: 'mt',
          }) / convertValue({value: 1, from: 'm2', to: unitsSystem});

    return {
      dSocMassPerArea: {
        value: massPerAreavalue,
        unit: SOCMETRICS_UNIT_MAP.dSocMassPerArea(unitsSystem).unit,
        formattedValue: SOCMETRICS_FORMATTER_MAP.dSocMassPerArea(massPerAreavalue),
      },
      ...(isNil(stdErrValue)
        ? {}
        : {
            dSocStdErr: {
              value: stdErrValue,
              unit: SOCMETRICS_UNIT_MAP.dSocStdErr(unitsSystem).unit,
              formattedValue: SOCMETRICS_FORMATTER_MAP.dSocStdErr(stdErrValue),
            },
          }),
    };
  };
