import {type Theme} from '@regrow-internal/design-system';

import {type MeasurementEnum} from '_common/utils/measurement-unit-options';

import type {TillageMetricData} from 'containers/si/api/apiTypes';
import {
  CROPLAND_SATELLITE_UNVERIFIED_TEXT,
  CROPLAND_SATELLITE_VERIFIED_TEXT,
} from 'containers/si/constants';
import {convertValue, getUnit} from 'containers/si/utils/convert';
import {
  formatFloatToPercent,
  formatLargeNumbers,
} from 'containers/si/utils/formatters/number.format';
import {isFiniteNumber} from 'containers/si/utils/utils';
import {type Metric, type UnitDetail, type UnitType} from 'containers/si/utils/value.types';

// TODO: SI-3064 clean up type, unit map, formatter map and label map depending on expected value roll out
export type TillageMetrics = {
  adoption: Metric;
  adoptionStdDev?: Metric;
  conservationTillageArea: Metric;
  conservationTillageAreaStdDev?: Metric;
  noTillageArea: Metric;
  noTillageAreaStdDev?: Metric;
  reducedTillageArea: Metric;
  reducedTillageAreaStdDev?: Metric;
  conventionalTillageArea: Metric;
  conventionalTillageAreaStdDev?: Metric;
  totalTrackedArea: Metric;
  unknownArea: Metric;
};

export const private__tillageAreaUnit = (unitsSystem: MeasurementEnum) => getUnit(unitsSystem);

export const private__tillageAdoptionUnit = () => {
  return {
    unit: 'unit-interval' as const,
    unitName: {
      singular: `% of cropland area practicing`,
      plural: `% of cropland area practicing`,
      abbr: `adoption`,
    },
  };
};

// TODO: SI-3064 clean up type, unit map, formatter map and label map depending on expected value roll out
export const TILLAGEMETRICS_FORMATTER_MAP: Record<keyof TillageMetrics, (value: number) => string> =
  {
    adoption: value => formatFloatToPercent(value, {sigDigits: 1}),
    adoptionStdDev: value => formatFloatToPercent(value, {sigDigits: 1}), // This should always match adoption formatter, to keep them consistent
    conservationTillageArea: formatLargeNumbers,
    conservationTillageAreaStdDev: formatLargeNumbers, // This should always match conservationTillageArea formatter, to keep them consistent
    noTillageArea: formatLargeNumbers,
    noTillageAreaStdDev: formatLargeNumbers, // This should always match noTillageArea formatter, to keep them consistent
    reducedTillageArea: formatLargeNumbers,
    reducedTillageAreaStdDev: formatLargeNumbers, // This should always match reducedTillageArea formatter, to keep them consistent
    conventionalTillageArea: formatLargeNumbers,
    conventionalTillageAreaStdDev: formatLargeNumbers, // This should always match conventionalTillageArea formatter, to keep them consistent
    totalTrackedArea: formatLargeNumbers,
    unknownArea: formatLargeNumbers,
  };

// TODO: SI-3064 clean up type, unit map, formatter map and label map depending on expected value roll out
export const TILLAGEMETRICS_UNIT_MAP: Record<
  keyof TillageMetrics,
  (unitsSystem: MeasurementEnum) => UnitDetail<UnitType>
> = {
  adoption: private__tillageAdoptionUnit,
  adoptionStdDev: private__tillageAdoptionUnit, // This should always match adoption unit, to keep them consistent
  conservationTillageArea: private__tillageAreaUnit,
  conservationTillageAreaStdDev: private__tillageAreaUnit, // This should always match conservationTillageArea unit, to keep them
  noTillageArea: private__tillageAreaUnit,
  noTillageAreaStdDev: private__tillageAreaUnit, // This should always match noTillageArea unit, to keep them
  reducedTillageArea: private__tillageAreaUnit,
  reducedTillageAreaStdDev: private__tillageAreaUnit, // This should always match reducedTillageArea unit, to keep them
  conventionalTillageArea: private__tillageAreaUnit,
  conventionalTillageAreaStdDev: private__tillageAreaUnit, // This should always match conventionalTillageArea unit, to keep them
  totalTrackedArea: private__tillageAreaUnit,
  unknownArea: private__tillageAreaUnit,
};

// TODO: SI-3064 clean up type, unit map, formatter map and label map depending on expected value roll out
export const TILLAGEMETRICS_LABEL_MAP: Record<
  keyof TillageMetrics,
  {label: string; categoryPaletteColorKey: keyof Theme['palette']['categoryPalette']}
> = {
  adoption: {label: 'adoption', categoryPaletteColorKey: '0'},
  adoptionStdDev: {label: 'adoption uncertainty', categoryPaletteColorKey: '0'},
  conservationTillageArea: {label: 'conservation till', categoryPaletteColorKey: '0'},
  conservationTillageAreaStdDev: {
    label: 'conservation till uncertainty',
    categoryPaletteColorKey: '0',
  },
  totalTrackedArea: {label: CROPLAND_SATELLITE_VERIFIED_TEXT, categoryPaletteColorKey: '0'},
  noTillageArea: {label: 'no till', categoryPaletteColorKey: '1'},
  noTillageAreaStdDev: {label: 'no till uncertainty', categoryPaletteColorKey: '1'},
  reducedTillageArea: {label: 'reduced till', categoryPaletteColorKey: '0'},
  reducedTillageAreaStdDev: {label: 'reduced till uncertainty', categoryPaletteColorKey: '0'},
  conventionalTillageArea: {label: 'conventional till', categoryPaletteColorKey: '7'},
  conventionalTillageAreaStdDev: {
    label: 'conventional till uncertainty',
    categoryPaletteColorKey: '7',
  },
  unknownArea: {label: CROPLAND_SATELLITE_UNVERIFIED_TEXT, categoryPaletteColorKey: '8'},
} as const;

// TODO: SI-3064 remove transform depending on expected value roll out
/**
 * This should only be used as a transformer alone with one of the standard kpi.transformation.helpers
 * Example usage:
 * const metricData = makeTopLevelMetric(response, makeTillageMetrics(MeasurementEnum.Metric));
 */
export const makeTillageMetrics =
  (unitsSystem: MeasurementEnum) =>
  (data: TillageMetricData): TillageMetrics | null => {
    const {till_conv_area_m2, till_notill_area_m2, till_redu_area_m2, till_unknown_area_m2} = data;

    // These can be removed once a response deserializer and type validator is built
    // https://regrow.atlassian.net/browse/SI-1379
    // This method should handle
    // expected nulls or undefineds and
    // Finite checks as those are valid numbers
    if (
      !isFiniteNumber(till_conv_area_m2) ||
      !isFiniteNumber(till_redu_area_m2) ||
      !isFiniteNumber(till_notill_area_m2) ||
      !isFiniteNumber(till_unknown_area_m2 ?? 0)
    ) {
      return null;
    }

    const conservation_till_area_m2 = till_redu_area_m2 + till_notill_area_m2;
    const total_area_m2 = till_conv_area_m2 + conservation_till_area_m2;

    if (total_area_m2 === 0) return null;

    const till_conserv_adoption_rate = conservation_till_area_m2 / total_area_m2;

    const totalTillageFieldAreaValue = convertValue({
      value: total_area_m2,
      from: 'm2',
      to: unitsSystem,
    });

    const conventionalTillageFieldAreaValue = convertValue({
      value: till_conv_area_m2,
      from: 'm2',
      to: unitsSystem,
    });

    const reducedTillageFieldAreaValue = convertValue({
      value: till_redu_area_m2,
      from: 'm2',
      to: unitsSystem,
    });

    const noTillageFieldAreaValue = convertValue({
      value: till_notill_area_m2,
      from: 'm2',
      to: unitsSystem,
    });

    const conservationTillageFieldAreaValue = convertValue({
      value: conservation_till_area_m2,
      from: 'm2',
      to: unitsSystem,
    });

    // NOTE: This coalesces to 0 ONLY while auto-gen types allow an undefined value for unknown area;
    // we do NOT want the FE to do this anywhere else in the app and this will be removed when the types are updated.
    const unknownAreaValue = convertValue({
      value: till_unknown_area_m2 ?? 0,
      from: 'm2',
      to: unitsSystem,
    });

    return {
      adoption: {
        value: till_conserv_adoption_rate,
        unit: TILLAGEMETRICS_UNIT_MAP.adoption(unitsSystem).unit,
        formattedValue: TILLAGEMETRICS_FORMATTER_MAP.adoption(till_conserv_adoption_rate),
      },
      totalTrackedArea: {
        value: totalTillageFieldAreaValue,
        unit: TILLAGEMETRICS_UNIT_MAP.totalTrackedArea(unitsSystem).unit,
        formattedValue: TILLAGEMETRICS_FORMATTER_MAP.totalTrackedArea(totalTillageFieldAreaValue),
      },
      conservationTillageArea: {
        value: conservationTillageFieldAreaValue,
        unit: TILLAGEMETRICS_UNIT_MAP.conservationTillageArea(unitsSystem).unit,
        formattedValue: TILLAGEMETRICS_FORMATTER_MAP.conservationTillageArea(
          conservationTillageFieldAreaValue
        ),
      },
      noTillageArea: {
        value: noTillageFieldAreaValue,
        unit: TILLAGEMETRICS_UNIT_MAP.noTillageArea(unitsSystem).unit,
        formattedValue: TILLAGEMETRICS_FORMATTER_MAP.noTillageArea(noTillageFieldAreaValue),
      },
      reducedTillageArea: {
        value: reducedTillageFieldAreaValue,
        unit: TILLAGEMETRICS_UNIT_MAP.reducedTillageArea(unitsSystem).unit,
        formattedValue: TILLAGEMETRICS_FORMATTER_MAP.reducedTillageArea(
          reducedTillageFieldAreaValue
        ),
      },
      conventionalTillageArea: {
        value: conventionalTillageFieldAreaValue,
        unit: TILLAGEMETRICS_UNIT_MAP.conventionalTillageArea(unitsSystem).unit,
        formattedValue: TILLAGEMETRICS_FORMATTER_MAP.conventionalTillageArea(
          conventionalTillageFieldAreaValue
        ),
      },
      unknownArea: {
        value: unknownAreaValue,
        unit: TILLAGEMETRICS_UNIT_MAP.unknownArea(unitsSystem).unit,
        formattedValue: TILLAGEMETRICS_FORMATTER_MAP.unknownArea(unknownAreaValue),
      },
    };
  };

// TODO: SI-3064 remove transform depending on expected value roll out
/**
 * This should only be used as a transformer alone with one of the standard kpi.transformation.helpers
 * Example usage:
 * const metricData = makeTopLevelMetric(response, makeTillageMetrics(MeasurementEnum.Metric));
 */
export const makeTillageExpectedMetrics =
  (unitsSystem: MeasurementEnum) =>
  (data: TillageMetricData): TillageMetrics | null => {
    const {
      till_conv_expected_area_m2,
      till_conv_area_m2_stddev,
      till_conserv_expected_area_m2,
      till_conserv_area_m2_stddev,
      till_notill_expected_area_m2,
      till_notill_area_m2_stddev,
      till_redu_expected_area_m2,
      till_redu_area_m2_stddev,
      till_unknown_area_m2,
      till_conserv_adoption_rate,
      till_conserv_adoption_rate_stddev,
    } = data;

    // These can be removed once a response deserializer and type validator is built
    // https://regrow.atlassian.net/browse/SI-1379
    // This method should handle
    // expected nulls or undefineds and
    // Finite checks as those are valid numbers
    if (
      !isFiniteNumber(till_conv_expected_area_m2) ||
      !isFiniteNumber(till_conv_area_m2_stddev) ||
      !isFiniteNumber(till_notill_expected_area_m2) ||
      !isFiniteNumber(till_notill_area_m2_stddev) ||
      !isFiniteNumber(till_redu_expected_area_m2) ||
      !isFiniteNumber(till_redu_area_m2_stddev) ||
      !isFiniteNumber(till_conserv_expected_area_m2) ||
      !isFiniteNumber(till_conserv_area_m2_stddev) ||
      !isFiniteNumber(till_unknown_area_m2 ?? 0) ||
      !isFiniteNumber(till_conserv_adoption_rate) ||
      !isFiniteNumber(till_conserv_adoption_rate_stddev)
    ) {
      return null;
    }

    if (
      till_conv_expected_area_m2 + till_redu_expected_area_m2 + till_notill_expected_area_m2 ===
      0
    )
      return null;

    const totalTillageFieldAreaValue = convertValue({
      value: till_conv_expected_area_m2 + till_conserv_expected_area_m2,
      from: 'm2',
      to: unitsSystem,
    });

    const conventionalTillageFieldAreaValue = convertValue({
      value: till_conv_expected_area_m2,
      from: 'm2',
      to: unitsSystem,
    });
    const conventionalTillageFieldAreaStdDevValue = convertValue({
      value: till_conv_area_m2_stddev,
      from: 'm2',
      to: unitsSystem,
    });

    const reducedTillageFieldAreaValue = convertValue({
      value: till_redu_expected_area_m2,
      from: 'm2',
      to: unitsSystem,
    });
    const reducedTillageFieldAreaStdDevValue = convertValue({
      value: till_redu_area_m2_stddev,
      from: 'm2',
      to: unitsSystem,
    });

    const noTillageFieldAreaValue = convertValue({
      value: till_notill_expected_area_m2,
      from: 'm2',
      to: unitsSystem,
    });
    const noTillageFieldAreaStdDevValue = convertValue({
      value: till_notill_area_m2_stddev,
      from: 'm2',
      to: unitsSystem,
    });

    const conservationTillageFieldAreaValue = convertValue({
      value: till_conserv_expected_area_m2,
      from: 'm2',
      to: unitsSystem,
    });
    const conservationTillageFieldAreaStdDevValue = convertValue({
      value: till_conserv_area_m2_stddev,
      from: 'm2',
      to: unitsSystem,
    });

    // NOTE: This coalesces to 0 ONLY while auto-gen types allow an undefined value for unknown area;
    // we do NOT want the FE to do this anywhere else in the app and this will be removed when the types are updated.
    const unknownAreaValue = convertValue({
      value: till_unknown_area_m2 ?? 0,
      from: 'm2',
      to: unitsSystem,
    });

    return {
      adoption: {
        value: till_conserv_adoption_rate,
        unit: TILLAGEMETRICS_UNIT_MAP.adoption(unitsSystem).unit,
        formattedValue: TILLAGEMETRICS_FORMATTER_MAP.adoption(till_conserv_adoption_rate),
      },
      adoptionStdDev: {
        value: till_conserv_adoption_rate_stddev,
        unit: TILLAGEMETRICS_UNIT_MAP.adoptionStdDev(unitsSystem).unit,
        formattedValue: TILLAGEMETRICS_FORMATTER_MAP.adoptionStdDev(
          till_conserv_adoption_rate_stddev
        ),
      },
      conservationTillageArea: {
        value: conservationTillageFieldAreaValue,
        unit: TILLAGEMETRICS_UNIT_MAP.conservationTillageArea(unitsSystem).unit,
        formattedValue: TILLAGEMETRICS_FORMATTER_MAP.conservationTillageArea(
          conservationTillageFieldAreaValue
        ),
      },
      conservationTillageAreaStdDev: {
        value: conservationTillageFieldAreaStdDevValue,
        unit: TILLAGEMETRICS_UNIT_MAP.conservationTillageAreaStdDev(unitsSystem).unit,
        formattedValue: TILLAGEMETRICS_FORMATTER_MAP.conservationTillageAreaStdDev(
          conservationTillageFieldAreaStdDevValue
        ),
      },
      noTillageArea: {
        value: noTillageFieldAreaValue,
        unit: TILLAGEMETRICS_UNIT_MAP.noTillageArea(unitsSystem).unit,
        formattedValue: TILLAGEMETRICS_FORMATTER_MAP.noTillageArea(noTillageFieldAreaValue),
      },
      noTillageAreaStdDev: {
        value: noTillageFieldAreaStdDevValue,
        unit: TILLAGEMETRICS_UNIT_MAP.noTillageAreaStdDev(unitsSystem).unit,
        formattedValue: TILLAGEMETRICS_FORMATTER_MAP.noTillageAreaStdDev(
          noTillageFieldAreaStdDevValue
        ),
      },
      reducedTillageArea: {
        value: reducedTillageFieldAreaValue,
        unit: TILLAGEMETRICS_UNIT_MAP.reducedTillageArea(unitsSystem).unit,
        formattedValue: TILLAGEMETRICS_FORMATTER_MAP.reducedTillageArea(
          reducedTillageFieldAreaValue
        ),
      },
      reducedTillageAreaStdDev: {
        value: reducedTillageFieldAreaStdDevValue,
        unit: TILLAGEMETRICS_UNIT_MAP.reducedTillageAreaStdDev(unitsSystem).unit,
        formattedValue: TILLAGEMETRICS_FORMATTER_MAP.reducedTillageAreaStdDev(
          reducedTillageFieldAreaStdDevValue
        ),
      },
      conventionalTillageArea: {
        value: conventionalTillageFieldAreaValue,
        unit: TILLAGEMETRICS_UNIT_MAP.conventionalTillageArea(unitsSystem).unit,
        formattedValue: TILLAGEMETRICS_FORMATTER_MAP.conventionalTillageArea(
          conventionalTillageFieldAreaValue
        ),
      },
      conventionalTillageAreaStdDev: {
        value: conventionalTillageFieldAreaStdDevValue,
        unit: TILLAGEMETRICS_UNIT_MAP.conventionalTillageAreaStdDev(unitsSystem).unit,
        formattedValue: TILLAGEMETRICS_FORMATTER_MAP.conventionalTillageAreaStdDev(
          conventionalTillageFieldAreaStdDevValue
        ),
      },
      totalTrackedArea: {
        value: totalTillageFieldAreaValue,
        unit: TILLAGEMETRICS_UNIT_MAP.totalTrackedArea(unitsSystem).unit,
        formattedValue: TILLAGEMETRICS_FORMATTER_MAP.totalTrackedArea(totalTillageFieldAreaValue),
      },
      unknownArea: {
        value: unknownAreaValue,
        unit: TILLAGEMETRICS_UNIT_MAP.unknownArea(unitsSystem).unit,
        formattedValue: TILLAGEMETRICS_FORMATTER_MAP.unknownArea(unknownAreaValue),
      },
    };
  };
