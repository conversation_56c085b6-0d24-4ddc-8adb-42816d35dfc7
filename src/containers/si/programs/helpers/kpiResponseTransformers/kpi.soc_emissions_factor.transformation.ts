import {isNil} from '_common/utils/typeGuards';

import type {SocEmissionFactorMetricData} from 'containers/si/api/apiTypes';
import {CO2E} from 'containers/si/constants';
import {getRatioUnits} from 'containers/si/utils/convert';
import {formatAllNumbers} from 'containers/si/utils/formatters/number.format';
import {type Metric, type UnitDetail, type UnitType} from 'containers/si/utils/value.types';

export type SocEFMetrics = {
  socEmissionsPerYield: Metric;
  socEFStdErr?: Metric;
};

export const private__socEFUnit = () =>
  getRatioUnits({
    unit1: 'kg',
    unit2: 'kg',
    subUnit1: CO2E,
    subUnit2: `yield`,
  });

export const SOC_EF_METRICS_FORMATTER_MAP: Record<keyof SocEFMetrics, (value: number) => string> = {
  socEmissionsPerYield: formatAllNumbers,
  socEFStdErr: formatAllNumbers,
};

export const SOC_EF_METRICS_UNIT_MAP: Record<keyof SocEFMetrics, () => UnitDetail<UnitType>> = {
  socEmissionsPerYield: private__socEFUnit,
  socEFStdErr: private__socEFUnit,
};

/**
 * This should only be used as a transformer alone with one of the standard kpi.transformation.helpers
 * Example usage:
 * const metricData = makeTopLevelMetric(response, makeSocEFMetrics);
 */
export const makeSocEFMetrics = (data: SocEmissionFactorMetricData): SocEFMetrics | null => {
  // These can be removed once a response deserializer and type validator is built
  // https://regrow.atlassian.net/browse/SI-1379
  // This method should handle
  // expected nulls or undefineds and
  // 0 and Finite checks as those are valid numbers
  if (isNil(data.soc_emissions_factor) || !Number.isFinite(data.soc_emissions_factor)) {
    return null;
  }

  const value = data.soc_emissions_factor;
  const stdErrValue =
    isNil(data.soc_emissions_factor_stderr) ||
    !Number.isFinite(data.soc_emissions_factor_stderr) ||
    data.soc_emissions_factor_stderr === 0
      ? undefined
      : data.soc_emissions_factor_stderr;

  const unitDetails = SOC_EF_METRICS_UNIT_MAP.socEmissionsPerYield();

  return {
    socEmissionsPerYield: {
      value,
      unit: unitDetails.unit,
      formattedValue: SOC_EF_METRICS_FORMATTER_MAP.socEmissionsPerYield(value),
    },
    ...(isNil(stdErrValue)
      ? {}
      : {
          socEFStdErr: {
            value: stdErrValue,
            unit: SOC_EF_METRICS_UNIT_MAP.socEFStdErr().unit,
            formattedValue: SOC_EF_METRICS_FORMATTER_MAP.socEFStdErr(stdErrValue),
          },
        }),
  };
};
