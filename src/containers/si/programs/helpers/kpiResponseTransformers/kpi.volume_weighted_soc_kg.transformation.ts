import {isNil} from '_common/utils/typeGuards';

import type {VolumeWeightedSocKgKPIMetricData} from 'containers/si/api/apiTypes';
import {CO2E} from 'containers/si/constants';
import {convertValue, getUnit} from 'containers/si/utils/convert';
import {formatAllNumbers} from 'containers/si/utils/formatters/number.format';
import type {Metric, UnitDetail, UnitType} from 'containers/si/utils/value.types';

export type VolumeWeightedSocMetrics = {
  volumeWeightedSocMass: Metric;
};

const private__volumeWeightedSocUnit = () => getUnit('mt', CO2E);

export const VOLUMEWEIGHTEDSOCMETRICS_FORMATTER_MAP: Record<
  keyof VolumeWeightedSocMetrics,
  (value: number) => string
> = {
  volumeWeightedSocMass: formatAllNumbers,
};

export const VOLUMEWEIGHTEDSOCMETRICS_UNIT_MAP: Record<
  keyof VolumeWeightedSocMetrics,
  () => UnitDetail<UnitType>
> = {
  volumeWeightedSocMass: private__volumeWeightedSocUnit,
};

/**
 * This should only be used as a transformer alone with one of the standard kpi.transformation.helpers
 * Example usage:
 * const metricData = makeTopLevelMetric(response, makeVolumeWeightedSocMetrics);
 */
export const makeVolumeWeightedSocMetrics = (
  data: VolumeWeightedSocKgKPIMetricData
): VolumeWeightedSocMetrics | null => {
  // These can be removed once a response deserializer and type validator is built
  // https://regrow.atlassian.net/browse/SI-1379
  // This method should handle
  // expected nulls or undefineds and
  // 0 and Finite checks as those are valid numbers
  if (isNil(data.volume_weighted_soc_kg) || !Number.isFinite(data.volume_weighted_soc_kg))
    return null;

  const value = convertValue({
    value: data.volume_weighted_soc_kg,
    from: 'kg',
    to: 'mt',
  });

  const unitDetails = VOLUMEWEIGHTEDSOCMETRICS_UNIT_MAP.volumeWeightedSocMass();

  return {
    volumeWeightedSocMass: {
      value,
      unit: unitDetails.unit,
      formattedValue: VOLUMEWEIGHTEDSOCMETRICS_FORMATTER_MAP.volumeWeightedSocMass(value),
    },
  };
};
