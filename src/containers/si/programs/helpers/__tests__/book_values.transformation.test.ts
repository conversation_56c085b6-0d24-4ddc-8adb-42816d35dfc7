import {isDefined} from '_common/utils/typeGuards';

import {KPIFertEFCropTypeSummaryMock} from 'containers/si/__mocks__/KPIFertEFMock ';
import {KPINetGhgEFCropTypeSummaryMock} from 'containers/si/__mocks__/KPINetGhgEFMock';
import {PROGRAM_BOOK_VALUES} from 'containers/si/__mocks__/ProgramBookValuesMock';
import type {CountryBookValueResponse, EmissionsFactorDetails} from 'containers/si/api/apiTypes';
import {CO2E} from 'containers/si/constants';
import {
  BY_CROP_BOOK_VALUE_EFS_METRICS_PAIRS,
  BY_CROP_REGROW_BASELINE_EFS_METRICS_PAIRS,
} from 'containers/si/programs/helpers/__mocks__/bookValueEFMetricsMock';
import {
  bookValueEFUnit,
  makeCropTypeBookValueEFMetricsPairs,
  makeCropTypeRegrowBaselineEFMetricsPairs,
  private__makeBookValueEFMetrics,
  type BookValueEFMetrics,
} from 'containers/si/programs/helpers/book_values.transformation';
import {makeCropTypeMetricPairs} from 'containers/si/programs/helpers/kpi.transformation.helpers';
import {makeFertEFMetrics} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.fert_emissions_factor.transformation';
import {makeNetGhgEFMetrics} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.net_ghg_emissions_factor.transformation';
import type {Metric} from 'containers/si/utils/value.types';

const defaultBookValueEFInput: EmissionsFactorDetails = {
  fert_emissions_factor: 0.046,
  ghg_emissions_factor: 0.072,
  trace_metals: 0.0,
  crop_residues: 0.0,
  seeds_seedlings_orchards: 0.0,
  irrigation: 0.0,
  luluc: 0.002,
  plant_protection: 0.013,
  other: 0.097,
};

const defaultBookValueEFMetrics: BookValueEFMetrics = {
  fert_emissions_factor: {
    value: 0.046,
    formattedValue: '0.046',
    unit: 'kg/kg',
  },
  ghg_emissions_factor: {
    value: 0.072,
    formattedValue: '0.072',
    unit: 'kg/kg',
  },
  trace_metals: null,
  crop_residues: null,
  seeds_seedlings_orchards: null,
  irrigation: null,
  luluc: {
    value: 0.002,
    formattedValue: '0.002',
    unit: 'kg/kg',
  },
  plant_protection: {
    value: 0.013,
    formattedValue: '0.013',
    unit: 'kg/kg',
  },
  other: {
    value: 0.097,
    formattedValue: '0.097',
    unit: 'kg/kg',
  },
};

describe('bookValueEFUnit', () => {
  it('should return the correct book value ef unit', () => {
    expect(bookValueEFUnit()).toEqual({
      unit: 'kg/kg',
      unitName: {
        singular: `kilogram ${CO2E} / kilogram yield`,
        plural: `kilograms ${CO2E} / kilogram yield`,
        abbr: `kg ${CO2E} / kg yield`,
      },
    });
  });
});

describe('private__makeBookValueEFMetrics', () => {
  it('Should return BookValueEFMetrics with appropriate units', () => {
    expect(private__makeBookValueEFMetrics(defaultBookValueEFInput)).toEqual(
      defaultBookValueEFMetrics
    );
  });

  it('Should return null when null parameters are provided', () => {
    expect(
      //@ts-expect-error covering all nil cases
      private__makeBookValueEFMetrics(null)
    ).toEqual(null);
    expect(private__makeBookValueEFMetrics(undefined)).toEqual(null);
    //@ts-expect-error covering all nil cases
    expect(private__makeBookValueEFMetrics({})).toEqual(null);
    expect(
      private__makeBookValueEFMetrics({
        //@ts-expect-error covering all nil cases
        fert_emissions_factor: null,
        //@ts-expect-error covering all nil cases
        ghg_emissions_factor: null,
        //@ts-expect-error covering all nil cases
        trace_metals: null,
        //@ts-expect-error covering all nil cases
        crop_residues: null,
        //@ts-expect-error covering all nil cases
        seeds_seedlings_orchards: null,
        //@ts-expect-error covering all nil cases
        irrigation: null,
        //@ts-expect-error covering all nil cases
        luluc: null,
        //@ts-expect-error covering all nil cases
        plant_protection: null,
        //@ts-expect-error covering all nil cases
        other: null,
      })
    ).toEqual(null);
  });

  it('Should return null when 0 value parameters are provided', () => {
    expect(
      private__makeBookValueEFMetrics({
        fert_emissions_factor: 0,
        ghg_emissions_factor: 0,
        trace_metals: 0,
        crop_residues: 0,
        seeds_seedlings_orchards: 0,
        irrigation: 0,
        luluc: 0,
        plant_protection: 0,
        other: 0,
      })
    ).toEqual(null);
  });

  it('Should return null when non finite parameters are provided', () => {
    expect(
      private__makeBookValueEFMetrics({
        fert_emissions_factor: NaN,
        ghg_emissions_factor: NaN,
        trace_metals: NaN,
        crop_residues: NaN,
        seeds_seedlings_orchards: NaN,
        irrigation: NaN,
        luluc: NaN,
        plant_protection: NaN,
        other: NaN,
      })
    ).toEqual(null);
  });
});

describe('makeCropTypeBookValueEFMetricsPairs', () => {
  it('Should return crop id book value ef metric pairs', () => {
    expect(makeCropTypeBookValueEFMetricsPairs(PROGRAM_BOOK_VALUES)).toEqual(
      BY_CROP_BOOK_VALUE_EFS_METRICS_PAIRS
    );
  });
  it('Should return null when null parameters are provided', () => {
    expect(makeCropTypeBookValueEFMetricsPairs(undefined)).toEqual(null);
    //@ts-expect-error covering all nil cases
    expect(makeCropTypeBookValueEFMetricsPairs(null)).toEqual(null);
    expect(makeCropTypeBookValueEFMetricsPairs([])).toEqual(null);
    const bookValuesWithNilCropValues: CountryBookValueResponse = {
      ...PROGRAM_BOOK_VALUES[0]!,
      //@ts-expect-error covering all nil cases
      crop_book_values: null,
    };
    expect(makeCropTypeBookValueEFMetricsPairs([bookValuesWithNilCropValues])).toEqual(null);
    const bookValuesWithEmptyCropValues: CountryBookValueResponse = {
      ...PROGRAM_BOOK_VALUES[0]!,
      crop_book_values: [],
    };
    expect(makeCropTypeBookValueEFMetricsPairs([bookValuesWithEmptyCropValues])).toEqual(null);
  });
});

describe('makeCropTypeRegrowBaselineEFMetricsPairs', () => {
  const fieldEmissionsEFByCropMetricsPairs = makeCropTypeMetricPairs(
    KPINetGhgEFCropTypeSummaryMock,
    makeNetGhgEFMetrics
  )!.map<[string, Metric]>(([cropIds, metrics]) => [cropIds, metrics!.netGhgEmissionsPerYield]);

  const fertilizerEmissionsEFByCropMetricsPairs = makeCropTypeMetricPairs(
    KPIFertEFCropTypeSummaryMock,
    makeFertEFMetrics
  );
  it('Should return crop id book value ef metric pairs with regrow emissions', () => {
    const result = makeCropTypeRegrowBaselineEFMetricsPairs({
      bookValueEFMetricsByCropTypePairs: BY_CROP_BOOK_VALUE_EFS_METRICS_PAIRS,
      fertilizerEmissionsEFByCropMetricsPairs,
      fieldEmissionsEFByCropMetricsPairs,
    });

    expect(result).toEqual(BY_CROP_REGROW_BASELINE_EFS_METRICS_PAIRS);
  });

  it('Should return book values when regrow ef parameters are not provided', () => {
    expect(
      makeCropTypeRegrowBaselineEFMetricsPairs({
        bookValueEFMetricsByCropTypePairs: BY_CROP_BOOK_VALUE_EFS_METRICS_PAIRS,
      })
    ).toEqual(BY_CROP_BOOK_VALUE_EFS_METRICS_PAIRS);
  });

  it('Should return null when book value is null / not provided (do not replace with regrow ef)', () => {
    const withNullFieldEmissionsByCropBookValueEFsMetricsPairs =
      BY_CROP_BOOK_VALUE_EFS_METRICS_PAIRS.map<[string, BookValueEFMetrics | null]>(
        ([cropId, bookValueEfMetrics]) => [
          cropId,
          isDefined(bookValueEfMetrics)
            ? {...bookValueEfMetrics, ghg_emissions_factor: null, fert_emissions_factor: null}
            : null,
        ]
      );

    expect(
      makeCropTypeRegrowBaselineEFMetricsPairs({
        bookValueEFMetricsByCropTypePairs: withNullFieldEmissionsByCropBookValueEFsMetricsPairs,
        fertilizerEmissionsEFByCropMetricsPairs,
        fieldEmissionsEFByCropMetricsPairs,
      })
    ).toEqual(withNullFieldEmissionsByCropBookValueEFsMetricsPairs);
  });

  it('Should return null when null book value parameters are provided', () => {
    expect(
      makeCropTypeRegrowBaselineEFMetricsPairs({
        bookValueEFMetricsByCropTypePairs: null,
      })
    ).toEqual(null);
  });
});
