import {MeasurementEnum} from '_common/utils/measurement-unit-options';

import {
  KPICoverCropAnnualizedMock,
  KPICoverCropCropTypeSummaryMock,
  KPICoverCropSubsectionAnnualizedMock,
} from 'containers/si/__mocks__/KPICoverCropMock';
import {
  KPIFarmCountMock,
  KPIFarmCountSummarizeBySubsectionMock,
} from 'containers/si/__mocks__/KPIFarmCountMock';
import {
  KPIG2cLulcAnnualizedMock,
  KPIG2cLulcKpiMock,
  KPIG2cLulcSubsectionAnnualizedMock,
  KPIG2cLulcSubsectionMock,
} from 'containers/si/__mocks__/KPIG2cLulcMock';
import {KPINetImpactCropTypeSummaryMock} from 'containers/si/__mocks__/KPINetImpactMock';
import type {
  CoverCropKPIResponse,
  FarmCountKPIResponse,
  LivingRootConfidenceKPIResponse,
  LivingRootCoverKPIResponse,
} from 'containers/si/api/apiTypes';
import {
  COVER_CROP_ANNUALIZED_EXPECTED_OUTPUT_IMPERIAL_UNITS,
  COVER_CROP_CROP_TYPE_EXPECTED_OUTPUT_IMPERIAL_UNITS,
  COVER_CROP_SUBREGION_ANNUALIZED_EXPECTED_OUTPUT_IMPERIAL_UNITS,
} from 'containers/si/programs/helpers/__mocks__/covercropMock';
import {
  makeAnnualizedMetricPairs,
  makeBoundaryMetricPairs,
  makeBoundaryPairsWithAnnualizedMetricPairs,
  makeCropTypeMetricPairs,
  makeTopLevelMetric,
} from 'containers/si/programs/helpers/kpi.transformation.helpers';
import {makeCoverCropMetrics} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.cover_crop.transformation';
import {
  makeFarmCountMetrics,
  type FarmCountMetrics,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.farm_count.transformation';
import {
  makeG2cLulcMetrics,
  type G2cLulcMetrics,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.grassland_to_cropland_lulc.transformation';
import {
  makeNetImpactMetrics,
  type NetImpactMetrics,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.net_impact_kg_per_m2.transformation';

/* eslint-disable @typescript-eslint/consistent-type-assertions */

describe('makeTopLevelMetric', () => {
  /**
   * Type assertion "tests"
   * These are covering several type test cases that can go wrong if the types are messed up.
   * Typescript should catch if the kpi response argument does not match the tranformer method argument
   * Equivalent "should not raise ts errors" cases are also below.
   * If the "should pass" cases raise typescript errors
   * or the "should fail" cases start to pass, something has gone wrong!
   */
  const g2cLulcResponse = KPIG2cLulcKpiMock;
  const farmCountResponse = KPIFarmCountMock;

  const _mismatchedResponseAndTransformerShouldError = makeTopLevelMetric(
    g2cLulcResponse,
    // @ts-expect-error This should error for mismatch on response and transformer method (DO NOT REMOVE! IF THIS IS NO LONGER NEEDED, THE TYPES ARE BROKEN)
    makeFarmCountMetrics
  );
  // When the transformer and response match, they should not type error
  const _farmCountMetric = makeTopLevelMetric(farmCountResponse, makeFarmCountMetrics);
  const _g2cLulcMetric = makeTopLevelMetric(
    g2cLulcResponse,
    makeG2cLulcMetrics(MeasurementEnum.MetricUnits)
  );

  test('returns null when nil response is passed', () => {
    const nullResponse = null as FarmCountKPIResponse | null;
    const undefinedResponse = undefined as FarmCountKPIResponse | undefined;
    expect(makeTopLevelMetric(nullResponse, makeFarmCountMetrics)).toBeNull();
    expect(makeTopLevelMetric(undefinedResponse, makeFarmCountMetrics)).toBeNull();
  });

  test('returns transformed metric data when response is passed', () => {
    const farmTopLevelOutput: FarmCountMetrics = {
      farm_count: {
        formattedValue: '1.1M',
        unit: 'count',
        value: 1111749,
      },
    };

    const g2cTopLevelOutput: G2cLulcMetrics = {
      conversion: {
        formattedValue: '0.2%',
        unit: 'unit-interval',
        value: 0.002030278776901377,
      },
      convertedArea: {
        formattedValue: '880.9K',
        unit: 'ha',
        value: 880883.9999999998,
      },
      totalTrackedArea: {
        formattedValue: '433.9M',
        unit: 'ha',
        value: 433873421.73,
      },
    };

    expect(makeTopLevelMetric(KPIFarmCountMock, makeFarmCountMetrics)).toEqual(farmTopLevelOutput);
    expect(
      makeTopLevelMetric(KPIG2cLulcKpiMock, makeG2cLulcMetrics(MeasurementEnum.MetricUnits))
    ).toEqual(g2cTopLevelOutput);
  });
});

describe('makeSubsectionMetric', () => {
  /**
   * Type assertion "tests"
   * These are covering several type test cases that can go wrong if the types are messed up.
   * Typescript should catch if
   *    1. the kpi response argument does not support `subsection_summary`
   *    2. the kpi response argument does not match the tranformer method argument
   * Equivalent "should not raise ts errors" cases are also below.
   * If the "should pass" cases raise typescript errors
   * or the "should fail" cases start to pass, something has gone wrong!
   */

  const g2cLulcResponse = KPIG2cLulcSubsectionMock;
  const farmCountResponse = KPIFarmCountSummarizeBySubsectionMock;
  // TODO: replace with a real mock when we have a v2 mock for this
  const unsupportedResponse = {kpi_name: 'living_root_cover'} as LivingRootCoverKPIResponse;

  const _unsupportedResponse = makeBoundaryMetricPairs(
    // @ts-expect-error This should error for response that does not support boundary_summary (DO NOT REMOVE! IF THIS IS NO LONGER NEEDED, THE TYPES ARE BROKEN)
    unsupportedResponse,
    makeFarmCountMetrics
  );

  const _mismatchedResponseAndTransformerShouldError = makeBoundaryMetricPairs(
    g2cLulcResponse,
    // @ts-expect-error This should error for mismatch on response and transformer method (DO NOT REMOVE! IF THIS IS NO LONGER NEEDED, THE TYPES ARE BROKEN)
    makeFarmCountMetrics
  );
  // When the transformer and response match, they should not type error
  const _farmCountMetric = makeBoundaryMetricPairs(farmCountResponse, makeFarmCountMetrics);
  const _g2cLulcMetric = makeBoundaryMetricPairs(
    g2cLulcResponse,
    makeG2cLulcMetrics(MeasurementEnum.MetricUnits)
  );

  test('returns null when nil response is passed', () => {
    const nullResponse = null as FarmCountKPIResponse | null;
    const undefinedResponse = undefined as FarmCountKPIResponse | undefined;
    expect(makeBoundaryMetricPairs(nullResponse, makeFarmCountMetrics)).toBeNull();
    expect(makeBoundaryMetricPairs(undefinedResponse, makeFarmCountMetrics)).toBeNull();
  });

  test('returns transformed metric data when response is passed', () => {
    const farmSubregion872Output: FarmCountMetrics = {
      farm_count: {
        formattedValue: '638K',
        unit: 'count',
        value: 638368,
      },
    };

    const farmCountResult = makeBoundaryMetricPairs(
      KPIFarmCountSummarizeBySubsectionMock,
      makeFarmCountMetrics
    );
    const farmCountSubregion872Result = farmCountResult?.find(([id, _]) => id === '872');

    expect(farmCountResult).toHaveLength(
      Object.keys(KPIFarmCountSummarizeBySubsectionMock.boundary_summary).length
    );

    expect(farmCountSubregion872Result).toBeDefined();
    expect(farmCountSubregion872Result![1]).toEqual(farmSubregion872Output);
    const g2cSubregion872Output: G2cLulcMetrics = {
      conversion: {
        formattedValue: '0.09%',
        unit: 'unit-interval',
        value: 0.0008602396122136921,
      },
      convertedArea: {
        formattedValue: '267.3K',
        unit: 'ac',
        value: 267272.3072871901,
      },
      totalTrackedArea: {
        formattedValue: '310.7M',
        unit: 'ac',
        value: 310695187.12281406,
      },
    };

    const g2cResult = makeBoundaryMetricPairs(
      KPIG2cLulcSubsectionMock,
      makeG2cLulcMetrics(MeasurementEnum.ImperialUnits)
    );
    const g2cSubregion872Result = g2cResult?.find(([id, _]) => id === '872');
    expect(g2cResult).toHaveLength(Object.keys(KPIG2cLulcSubsectionMock.boundary_summary).length);

    expect(g2cSubregion872Result).toBeDefined();
    expect(g2cSubregion872Result![1]).toEqual(g2cSubregion872Output);
  });
});

describe('makeAnnualizedMetric', () => {
  /**
   * Type assertion "tests"
   * These are covering several type test cases that can go wrong if the types are messed up.
   * Typescript should catch if
   *    1. the kpi response argument does not support `annualized_summary`
   *    2. the kpi response argument does not match the tranformer method argument
   * Equivalent "should not raise ts errors" cases are also below.
   * If the "should pass" cases raise typescript errors
   * or the "should fail" cases start to pass, something has gone wrong!
   */

  const g2cLulcResponse = KPIG2cLulcAnnualizedMock;
  const coverCropResponse = KPICoverCropAnnualizedMock;
  // TODO: replace with a real mock when we have a v2 mock for this
  const unsupportedResponse = {
    kpi_name: 'living_root_confidence',
  } as LivingRootConfidenceKPIResponse;

  const _unsupportedResponse = makeAnnualizedMetricPairs(
    // @ts-expect-error This should error for mismatch on response that does not support annualized summary type  (DO NOT REMOVE! IF THIS IS NO LONGER NEEDED, THE TYPES ARE BROKEN)
    unsupportedResponse,
    makeCoverCropMetrics(MeasurementEnum.MetricUnits)
  );

  const _mismatchedResponseAndTransformerShouldError = makeAnnualizedMetricPairs(
    g2cLulcResponse,
    // @ts-expect-error This should error for mismatch on response and transformer method (DO NOT REMOVE! IF THIS IS NO LONGER NEEDED, THE TYPES ARE BROKEN)
    makeCoverCropMetrics(MeasurementEnum.MetricUnits)
  );
  // When the transformer and response match, they should not type error
  const _coverCropMetric = makeAnnualizedMetricPairs(
    coverCropResponse,
    makeCoverCropMetrics(MeasurementEnum.MetricUnits)
  );
  const _g2cLulcMetric = makeAnnualizedMetricPairs(
    g2cLulcResponse,
    makeG2cLulcMetrics(MeasurementEnum.MetricUnits)
  );

  test('returns null when nil response is passed', () => {
    const nullResponse = null as CoverCropKPIResponse | null;
    const undefinedResponse = undefined as CoverCropKPIResponse | undefined;
    expect(
      makeAnnualizedMetricPairs(nullResponse, makeCoverCropMetrics(MeasurementEnum.MetricUnits))
    ).toBeNull();
    expect(
      makeAnnualizedMetricPairs(
        undefinedResponse,
        makeCoverCropMetrics(MeasurementEnum.MetricUnits)
      )
    ).toBeNull();
  });

  test('returns transformed metric data when response is passed', () => {
    const coverCropResult = makeAnnualizedMetricPairs(
      KPICoverCropAnnualizedMock,
      makeCoverCropMetrics(MeasurementEnum.ImperialUnits)
    );
    const covercropAnnualized2015Result = coverCropResult?.find(
      ([year, _]) => year === '2015'
    )?.[1];

    expect(coverCropResult).toBeDefined();
    expect(Object.keys(coverCropResult!)).toHaveLength(
      Object.keys(KPICoverCropAnnualizedMock.annualized_summary).length
    );

    expect(covercropAnnualized2015Result).toBeDefined();
    expect(covercropAnnualized2015Result).toEqual(
      COVER_CROP_ANNUALIZED_EXPECTED_OUTPUT_IMPERIAL_UNITS
    );

    const g2cAnnualized2017Output: G2cLulcMetrics = {
      conversion: {
        formattedValue: '0.1%',
        unit: 'unit-interval',
        value: 0.0010503989909117053,
      },
      convertedArea: {
        formattedValue: '838.2K',
        unit: 'ac',
        value: 838234.98225399,
      },
      totalTrackedArea: {
        formattedValue: '798M',
        unit: 'ac',
        value: 798015791.624509,
      },
    };

    const g2cResult = makeAnnualizedMetricPairs(
      KPIG2cLulcAnnualizedMock,
      makeG2cLulcMetrics(MeasurementEnum.ImperialUnits)
    );
    const g2cAnnualized2017Result = g2cResult?.find(([year, _]) => year === '2017')?.[1];

    expect(g2cResult).toBeDefined();
    expect(Object.keys(g2cResult!)).toHaveLength(
      Object.keys(KPIG2cLulcAnnualizedMock.annualized_summary).length
    );

    expect(g2cAnnualized2017Result).toBeDefined();
    expect(g2cAnnualized2017Result).toEqual(g2cAnnualized2017Output);
  });
});

describe('makeCropTypeMetricPairs', () => {
  /**
   * Type assertion "tests"
   * These are covering several type test cases that can go wrong if the types are messed up.
   * Typescript should catch if
   *    1. the kpi response argument does not support `crop_type_summary`
   *    2. the kpi response argument does not match the tranformer method argument
   * Equivalent "should not raise ts errors" cases are also below.
   * If the "should pass" cases raise typescript errors
   * or the "should fail" cases start to pass, something has gone wrong!
   */

  const netImpactResponse = KPINetImpactCropTypeSummaryMock;
  const coverCropResponse = KPICoverCropCropTypeSummaryMock;
  const unsupportedResponse = KPIG2cLulcKpiMock;

  const _unsupportedResponse = makeCropTypeMetricPairs(
    // @ts-expect-error This should error for response types that don't support crop_type_summary (DO NOT REMOVE! IF THIS IS NO LONGER NEEDED, THE TYPES ARE BROKEN)
    unsupportedResponse,
    makeNetImpactMetrics(MeasurementEnum.MetricUnits)
  );

  const _mismatchedResponseAndTransformerShouldError = makeCropTypeMetricPairs(
    netImpactResponse,
    // @ts-expect-error This should error for mismatch on response and transformer method (DO NOT REMOVE! IF THIS IS NO LONGER NEEDED, THE TYPES ARE BROKEN)
    makeCoverCropMetrics(MeasurementEnum.MetricUnits)
  );
  // When the transformer and response match, they should not type error
  const _coverCropMetric = makeCropTypeMetricPairs(
    coverCropResponse,
    makeCoverCropMetrics(MeasurementEnum.MetricUnits)
  );
  const _g2cLulcMetric = makeCropTypeMetricPairs(
    netImpactResponse,
    makeNetImpactMetrics(MeasurementEnum.MetricUnits)
  );

  test('returns null when nil response is passed', () => {
    const nullResponse = null as CoverCropKPIResponse | null;
    const undefinedResponse = undefined as CoverCropKPIResponse | undefined;
    expect(
      makeCropTypeMetricPairs(nullResponse, makeCoverCropMetrics(MeasurementEnum.MetricUnits))
    ).toBeNull();
    expect(
      makeCropTypeMetricPairs(undefinedResponse, makeCoverCropMetrics(MeasurementEnum.MetricUnits))
    ).toBeNull();
  });

  test('returns transformed metric data when response is passed', () => {
    const coverCropResult = makeCropTypeMetricPairs(
      coverCropResponse,
      makeCoverCropMetrics(MeasurementEnum.ImperialUnits)
    );
    const covercropCropTypeSummaryCornResult = coverCropResult?.find(
      ([crop_id, _]) => crop_id === '1'
    )?.[1];

    expect(coverCropResult).toBeDefined();
    expect(Object.keys(coverCropResult!)).toHaveLength(
      Object.keys(coverCropResponse.crop_type_summary).length
    );

    expect(covercropCropTypeSummaryCornResult).toBeDefined();
    expect(covercropCropTypeSummaryCornResult).toEqual(
      COVER_CROP_CROP_TYPE_EXPECTED_OUTPUT_IMPERIAL_UNITS
    );

    const netImpactCornCropTypeOutput: NetImpactMetrics = {
      netImpact: {
        formattedValue: '0.117',
        unit: 'mt/ac',
        value: 0.11677079767089155,
      },
    };

    const netResult = makeCropTypeMetricPairs(
      netImpactResponse,
      makeNetImpactMetrics(MeasurementEnum.ImperialUnits)
    );
    const netCornResult = netResult?.find(([crop_id, _]) => crop_id === '1')?.[1];

    expect(netResult).toBeDefined();
    expect(Object.keys(netResult!)).toHaveLength(
      Object.keys(netImpactResponse.crop_type_summary).length
    );

    expect(netCornResult).toBeDefined();
    expect(netCornResult).toEqual(netImpactCornCropTypeOutput);
  });
});

describe('makeBoundaryAnnualizedMetric', () => {
  /**
   * Type assertion "tests"
   * These are covering several type test cases that can go wrong if the types are messed up.
   * Typescript should catch if
   *    1. the kpi response argument does not support `subsection_annualized_summary`
   *    2. the kpi response argument does not match the tranformer method argument
   * Equivalent "should not raise ts errors" cases are also below.
   * If the "should pass" cases raise typescript errors
   * or the "should fail" cases start to pass, something has gone wrong!
   */

  const g2cLulcResponse = KPIG2cLulcSubsectionAnnualizedMock;
  const coverCropResponse = KPICoverCropSubsectionAnnualizedMock;
  // TODO: replace with a real mock when we have a v2 mock for this
  const unsupportedResponse = {
    kpi_name: 'living_root_confidence',
  } as LivingRootConfidenceKPIResponse;

  const _unsupportedResponse = makeBoundaryPairsWithAnnualizedMetricPairs(
    // @ts-expect-error This should error for mismatch on response that does not support boundary annualized summary type (DO NOT REMOVE! IF THIS IS NO LONGER NEEDED, THE TYPES ARE BROKEN)
    unsupportedResponse,
    makeCoverCropMetrics(MeasurementEnum.MetricUnits)
  );

  const _mismatchedResponseAndTransformerShouldError = makeBoundaryPairsWithAnnualizedMetricPairs(
    g2cLulcResponse,
    // @ts-expect-error This should error for mismatch on response and transformer method (DO NOT REMOVE! IF THIS IS NO LONGER NEEDED, THE TYPES ARE BROKEN)
    makeCoverCropMetrics(MeasurementEnum.MetricUnits)
  );
  // When the transformer and response match, they should not type error
  const _coverCropMetric = makeBoundaryPairsWithAnnualizedMetricPairs(
    coverCropResponse,
    makeCoverCropMetrics(MeasurementEnum.MetricUnits)
  );
  const _g2cLulcMetric = makeBoundaryPairsWithAnnualizedMetricPairs(
    g2cLulcResponse,
    makeG2cLulcMetrics(MeasurementEnum.MetricUnits)
  );

  test('returns null when nil response is passed', () => {
    const nullResponse = null as CoverCropKPIResponse | null;
    const undefinedResponse = undefined as CoverCropKPIResponse | undefined;
    expect(
      makeBoundaryPairsWithAnnualizedMetricPairs(
        nullResponse,
        makeCoverCropMetrics(MeasurementEnum.MetricUnits)
      )
    ).toBeNull();
    expect(
      makeBoundaryPairsWithAnnualizedMetricPairs(
        undefinedResponse,
        makeCoverCropMetrics(MeasurementEnum.MetricUnits)
      )
    ).toBeNull();
  });

  test('returns transformed metric data when response is passed', () => {
    const coverCropResult = makeBoundaryPairsWithAnnualizedMetricPairs(
      KPICoverCropSubsectionAnnualizedMock,
      makeCoverCropMetrics(MeasurementEnum.ImperialUnits)
    );
    const covercropAnnualized872Result = coverCropResult?.find(([id, _]) => id === '872')?.[1];

    expect(covercropAnnualized872Result).toBeDefined();
    expect(covercropAnnualized872Result).toHaveLength(
      Object.keys(KPICoverCropSubsectionAnnualizedMock.boundary_annualized_summary['872']!).length
    );

    const covercropAnnualized_872_2015_Result = covercropAnnualized872Result?.find(
      ([year, _]) => year === '2015'
    )?.[1];
    expect(covercropAnnualized_872_2015_Result).toBeDefined();
    expect(covercropAnnualized_872_2015_Result).toEqual(
      COVER_CROP_SUBREGION_ANNUALIZED_EXPECTED_OUTPUT_IMPERIAL_UNITS
    );
  });
});
