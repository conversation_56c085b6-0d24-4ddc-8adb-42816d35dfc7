import fromPairs from 'lodash/fromPairs';

import {type Theme} from '@regrow-internal/design-system';

import {getTypedEntries} from '_common/utils/object';
import {isDefined, isEmptyArray, isEmptyObject, isNil} from '_common/utils/typeGuards';

import type {CountryBookValueResponse, EmissionsFactorDetails} from 'containers/si/api/apiTypes';
import {CO2E} from 'containers/si/constants';
import {type FertEFMetrics} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.fert_emissions_factor.transformation';
import {getRatioUnits} from 'containers/si/utils/convert';
import {formatSmallNumbers} from 'containers/si/utils/formatters/number.format';
import type {Metric} from 'containers/si/utils/value.types';

export type BookValueEFMetrics = {
  fert_emissions_factor: Metric | null;
  ghg_emissions_factor: Metric | null;
  trace_metals: Metric | null;
  crop_residues: Metric | null;
  seeds_seedlings_orchards: Metric | null;
  irrigation: Metric | null;
  luluc: Metric | null;
  plant_protection: Metric | null;
  other: Metric | null;
};

export const BOOK_VALUE_EF_METRICS_LABEL_MAP: Record<
  keyof BookValueEFMetrics,
  {
    label: string;
    categoryPaletteColorKey: keyof Theme['palette']['categoryPalette'];
    categoryPaletteColorLighten: number;
  }
> = {
  fert_emissions_factor: {
    label: 'Fertilizer production',
    categoryPaletteColorKey: '1',
    categoryPaletteColorLighten: 0.3,
  },
  ghg_emissions_factor: {
    label: 'Field emissions',
    categoryPaletteColorKey: '1',
    categoryPaletteColorLighten: 0,
  },
  trace_metals: {
    label: 'Trace metals',
    categoryPaletteColorKey: '2',
    categoryPaletteColorLighten: 0.9,
  },
  crop_residues: {
    label: 'Crop residue',
    categoryPaletteColorKey: '2',
    categoryPaletteColorLighten: 0.75,
  },
  seeds_seedlings_orchards: {
    label: 'Seeds',
    categoryPaletteColorKey: '2',
    categoryPaletteColorLighten: 0.6,
  },
  irrigation: {
    label: 'Irrigation',
    categoryPaletteColorKey: '2',
    categoryPaletteColorLighten: 0.45,
  },
  luluc: {
    label: 'Land use change',
    categoryPaletteColorKey: '2',
    categoryPaletteColorLighten: 0.3,
  },
  plant_protection: {
    label: 'Plant protection',
    categoryPaletteColorKey: '2',
    categoryPaletteColorLighten: 0.15,
  },
  other: {label: 'Other', categoryPaletteColorKey: '2', categoryPaletteColorLighten: 0},
} as const;

export const bookValueEFUnit = () =>
  getRatioUnits({
    unit1: 'kg',
    unit2: 'kg',
    subUnit1: CO2E,
    subUnit2: `yield`,
  });

export const bookValueEFFormatter = formatSmallNumbers;

export const private__makeBookValueEFMetrics = (
  data: EmissionsFactorDetails | undefined
): BookValueEFMetrics | null => {
  // These can be removed once a response deserializer and type validator is built
  // https://regrow.atlassian.net/browse/SI-1379
  // This method should handle
  // expected null, undefined, Finite checks as those are valid numbers

  if (
    isNil(data) ||
    isEmptyObject(data) ||
    getTypedEntries(data).every(([_key, value]) => !Number.isFinite(value)) ||
    getTypedEntries(data).every(([_key, value]) => value === 0)
  ) {
    return null;
  }

  const makeMetric = (
    bookValueEFKey: keyof BookValueEFMetrics,
    emissionFactorDetails: EmissionsFactorDetails | undefined
  ) => {
    const value = emissionFactorDetails?.[bookValueEFKey];

    if (isNil(value) || value === 0) return null; // BE 0 is equivalent to no data

    return {
      value,
      formattedValue: bookValueEFFormatter(value),
      unit: bookValueEFUnit().unit,
    };
  };

  return {
    fert_emissions_factor: makeMetric('fert_emissions_factor', data),
    ghg_emissions_factor: makeMetric('ghg_emissions_factor', data),
    trace_metals: makeMetric('trace_metals', data),
    crop_residues: makeMetric('crop_residues', data),
    seeds_seedlings_orchards: makeMetric('seeds_seedlings_orchards', data),
    irrigation: makeMetric('irrigation', data),
    luluc: makeMetric('luluc', data),
    plant_protection: makeMetric('plant_protection', data),
    other: makeMetric('other', data),
  };
};

/**
 * @param data: Array of CountryBookValueResponses (from fetch book_values request)
 * @returns Pairs of crop Id and BookValueEFMetrics
 */
export const makeCropTypeBookValueEFMetricsPairs = (
  data: Array<CountryBookValueResponse> | undefined
): Array<[string, BookValueEFMetrics | null]> | null => {
  if (isNil(data?.[0]?.crop_book_values) || isEmptyArray(data[0].crop_book_values)) return null;

  return data[0].crop_book_values.map<[string, BookValueEFMetrics | null]>(
    ({crop_id, emission_factor_details}) => [
      String(crop_id),
      private__makeBookValueEFMetrics(emission_factor_details),
    ]
  );
};

/**
 * Generates Regrow baseline EF metrics from Book Value EFs and Regrow Emissions (net or ghg) EF and Regrow Fertilizer EF
 * Note, ensure all parameter EF units are kg/kg
 */
export const makeCropTypeRegrowBaselineEFMetricsPairs = ({
  bookValueEFMetricsByCropTypePairs,
  fieldEmissionsEFByCropMetricsPairs,
  fertilizerEmissionsEFByCropMetricsPairs,
}: {
  bookValueEFMetricsByCropTypePairs: Array<[string, BookValueEFMetrics | null]> | null;
  fieldEmissionsEFByCropMetricsPairs?: Array<[string, Metric | null]> | null;
  fertilizerEmissionsEFByCropMetricsPairs?: Array<[string, FertEFMetrics | null]> | null;
}): Array<[string, BookValueEFMetrics | null]> | null => {
  if (isNil(bookValueEFMetricsByCropTypePairs)) return null;

  const fieldEmissionsEFByCropMetricsLookup = fromPairs(fieldEmissionsEFByCropMetricsPairs);
  const fertilizerEmissionsEFByCropMetricsLookup = fromPairs(
    fertilizerEmissionsEFByCropMetricsPairs
  );

  return bookValueEFMetricsByCropTypePairs.map<[string, BookValueEFMetrics | null]>(
    ([cropId, bookValueEFMetrics]) =>
      isNil(bookValueEFMetrics)
        ? [cropId, null]
        : [
            cropId,
            {
              ...bookValueEFMetrics,
              ...(isDefined(bookValueEFMetrics.ghg_emissions_factor)
                ? {
                    ghg_emissions_factor:
                      fieldEmissionsEFByCropMetricsLookup[cropId] ??
                      bookValueEFMetrics.ghg_emissions_factor,
                  }
                : {}),
              ...(isDefined(bookValueEFMetrics.fert_emissions_factor)
                ? {
                    fert_emissions_factor:
                      fertilizerEmissionsEFByCropMetricsLookup[cropId]?.fertEmissionsPerYield ??
                      bookValueEFMetrics.fert_emissions_factor,
                  }
                : {}),
            },
          ]
  );
};
