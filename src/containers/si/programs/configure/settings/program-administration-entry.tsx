import React, {useState} from 'react';
import {Redirect} from 'react-router-dom';

import {Box, IconButton, SvgIcon, Tab, Tabs} from '@regrow-internal/design-system';

import {PlaceholderLoader} from '_common/components/placeholder-loader/placeholder-loader';
import {useParsedMatchParams} from '_common/hooks/use-parsed-match-params';
import {isDefined} from '_common/utils/typeGuards';

import SIApi from 'containers/si/api/si';
import {useFetchProgram} from 'containers/si/api/swr/hooks/useFetchProgram';
import {SIInputPopup} from 'containers/si/components/Legacy/si-input-popup';
import {Page<PERSON>ontainer, PageHeader, PageTitle} from 'containers/si/components/pageComponents';
import {useIsAdmin} from 'containers/si/hooks/useIsAdmin';
import {SIProgramAdmins} from 'containers/si/programs/configure/settings/program-admins/si-program-admins';
import {SIProgramParameters} from 'containers/si/programs/configure/settings/program-parameters/si-program-parameters';
import {SIProgramUsers} from 'containers/si/programs/configure/settings/program-users/si-program-users';
import {SI_HOME} from 'containers/si/routing/routes';

type TabT = 'admins' | 'parameters' | 'users';
const tabs: Array<{label: string; value: TabT}> = [
  {label: 'Program Parameters', value: 'parameters'},
  {label: 'Admins', value: 'admins'},
  {label: 'Users', value: 'users'},
];

export const ProgramAdministration = () => {
  const {programId} = useParsedMatchParams<{programId: number}>();

  const {isRegrowAdmin, isRegrowOrProgramAdmin} = useIsAdmin();
  const {data: program, mutate} = useFetchProgram();

  const [tab, setTab] = useState<TabT>('parameters');
  const [programNamePopupOpen, setProgramNamePopupOpen] = useState(false);

  const onUpdateProgramName = async (name: string | number) => {
    await SIApi.updateProgram(programId, {name: String(name)});
    void mutate();
    setProgramNamePopupOpen(false);
  };

  if (!isRegrowOrProgramAdmin) {
    return <Redirect to={SI_HOME} />;
  }

  return (
    <PageContainer>
      <PageHeader>
        <Box alignItems="center" display="flex" gap={2}>
          <PageTitle>
            {isDefined(program?.name) ? (
              `${program?.name} (#${program?.id})`
            ) : (
              <PlaceholderLoader width={200} height={26} />
            )}
          </PageTitle>

          {isRegrowAdmin && (
            <IconButton onClick={() => setProgramNamePopupOpen(true)}>
              <SvgIcon type="pencil" />
            </IconButton>
          )}
        </Box>
      </PageHeader>

      <Box display="flex" flexDirection="row" flexWrap="nowrap">
        <Tabs value={tab ?? false} onChange={(_, newValue: TabT) => setTab(newValue)}>
          {tabs.map(t_ => (
            <Tab {...t_} key={t_.value} />
          ))}
        </Tabs>
        <Box
          borderBottom={theme => `2px solid ${theme.palette.semanticPalette.stroke.main}`}
          flexGrow={1}
        />
      </Box>

      {tab === 'parameters' && <SIProgramParameters />}
      {tab === 'admins' && <SIProgramAdmins selectedProgramId={programId} />}
      {tab === 'users' && <SIProgramUsers selectedProgramId={programId} />}

      {programNamePopupOpen && (
        <SIInputPopup
          title="Edit Program Name"
          subtitle="Program Name"
          type="text"
          value={program?.name ?? ''}
          onSave={onUpdateProgramName}
          onHide={() => setProgramNamePopupOpen(false)}
        />
      )}
    </PageContainer>
  );
};
