import type {ComponentType} from 'react';
import React, {useEffect, useMemo, useState} from 'react';
import {CircularProgress} from 'react-md';

import {Box, Button, IconButton, SvgIcon} from '@regrow-internal/design-system';

import {useAppDispatch, useAppSelector} from 'store/useRedux';

import {Flex} from '_common/components/flex';
import {TableSubtext} from '_common/components/fluro-styled-components/fluro-styled-components.styled';
import {
  FluroDataTable,
  FluroTableBody,
  FluroTableColumn,
  FluroTableHeader,
  FluroTableRow,
} from '_common/components/fluro-table-components';
import {showNotification} from '_common/components/NotificationSnackbar';
import {SearchInput} from '_common/components/search-input';
import {Text} from '_common/components/text/text';
import {useStateWithDebouncedValue} from '_common/hooks/use-debounced-value';
import {selectIsLoading} from '_common/modules/helpers/selectors';
import {isDefined} from '_common/utils/typeGuards';

import {selectMeasurement} from 'containers/login/login-selectors';
import {ImpersonateButton} from 'containers/regrow-admin/users/impersonate-button';
import SIApi from 'containers/si/api/si';
import {ConfirmDialog} from 'containers/si/components/dialogs/ConfirmDialog';
import {useIsAdmin} from 'containers/si/hooks/useIsAdmin';
import {selectSIProgramAdminsByProgramId} from 'containers/si/module/selectors/access.selectors';
import {fetchSIProgramAdmins, removeSIProgramUser} from 'containers/si/module/thunks';
import {ActionType} from 'containers/si/module/types';
import {AddUserToSIProgramPopUp} from 'containers/si/programs/configure/settings/components/add-user-to-si-program-pop-up';
import type {AddSIUserToAProgramFormData} from 'containers/si/programs/configure/settings/types';

type Props = {
  selectedProgramId: number;
};

export const SIProgramAdmins: ComponentType<Props> = ({selectedProgramId}) => {
  const dispatch = useAppDispatch();
  const {isRegrowAdmin} = useIsAdmin();
  const programAdminsList = useAppSelector(s =>
    selectSIProgramAdminsByProgramId(s, selectedProgramId)
  );
  const isLoadingAdmins = useAppSelector(s =>
    selectIsLoading(s, [ActionType.FETCH_SI_PROGRAM_ADMINS])
  );
  const [bulkAddProgramAdminPopupVisible, setAddProgramAdminPopupVisible] = useState(false);
  const [deleteProgramAdminData, setDeleteProgramAdminData] = useState<{
    programId: number;
    userId: number;
  } | null>(null);
  const measurement = useAppSelector(selectMeasurement);
  const [searchText, setSearchText, debouncedSearchText] = useStateWithDebouncedValue('', 300);

  useEffect(() => {
    void dispatch(fetchSIProgramAdmins(selectedProgramId));
  }, [dispatch, selectedProgramId]);

  const onAddProgramAdmin = async (formData: AddSIUserToAProgramFormData) => {
    setAddProgramAdminPopupVisible(false);
    try {
      await SIApi.addProgramUser(formData.program, formData.user_id, 'admin');
      await dispatch(fetchSIProgramAdmins(selectedProgramId));

      showNotification({
        type: 'success',
        title: 'Success',
        message: `${formData.email} was invited as an admin to the program.`,
      });
    } catch (error) {
      showNotification({
        type: 'error',
        title: 'Error',
        message: `Couldn't add admin to the program`,
      });
    }
  };

  const onRemoveProgramAdmin = async () => {
    if (isDefined(deleteProgramAdminData)) {
      await dispatch(
        removeSIProgramUser({
          programId: deleteProgramAdminData.programId,
          user_id: deleteProgramAdminData.userId,
          role: 'admin',
        })
      );
      showNotification({
        type: 'success',
        title: 'Success',
        message: 'The program admin was removed.',
      });
      setDeleteProgramAdminData(null);
    } else {
      showNotification({
        type: 'error',
        title: 'Error',
        message: 'The program admin could not be removed.',
      });
    }
  };

  const preparedProgramAdminsList = useMemo(() => {
    return programAdminsList.map(p => ({
      ...p,
      loweredFirstName: (p.first_name || '').toLowerCase(),
      loweredLastName: (p.last_name || '').toLowerCase(),
      loweredEmail: p.email.toLowerCase(),
    }));
  }, [programAdminsList]);

  const filteredProgramAdminsList = useMemo(() => {
    const lowerSearchString = debouncedSearchText.toLowerCase();

    return preparedProgramAdminsList.filter(
      p =>
        p.loweredFirstName.includes(lowerSearchString) ||
        p.loweredLastName.includes(lowerSearchString) ||
        p.loweredEmail.includes(lowerSearchString)
    );
  }, [preparedProgramAdminsList, debouncedSearchText]);

  return (
    <div className={'margin-bottom-auto'}>
      <Text variant="h1" secondary>
        Program admins {programAdminsList.length !== 0 && `(${programAdminsList.length})`}
      </Text>
      <Flex justifyContent="space-between" className="mb-1">
        <SearchInput
          loading={isLoadingAdmins}
          value={searchText}
          onChange={setSearchText}
          placeholder="Search program admins"
        />
        {isRegrowAdmin && (
          <Button onClick={() => setAddProgramAdminPopupVisible(true)}>
            Add new program admin
          </Button>
        )}
      </Flex>
      {filteredProgramAdminsList.length > 0 ? (
        <FluroDataTable>
          <FluroTableHeader>
            <FluroTableRow>
              <FluroTableColumn>
                <div>Name</div>
                <TableSubtext>Email</TableSubtext>
              </FluroTableColumn>
              <FluroTableColumn type={'date'}>Last login</FluroTableColumn>
              <FluroTableColumn># {measurement} enrolled</FluroTableColumn>
            </FluroTableRow>
          </FluroTableHeader>

          <FluroTableBody>
            {filteredProgramAdminsList.map(admin => {
              return (
                <FluroTableRow key={admin.user_id}>
                  <FluroTableColumn>
                    <div>
                      {admin?.first_name} {admin?.last_name}
                    </div>
                    <TableSubtext>{admin?.email}</TableSubtext>
                  </FluroTableColumn>

                  <FluroTableColumn type={'date'}>{admin?.last_login_time || ''}</FluroTableColumn>

                  <FluroTableColumn>
                    -
                    {/*{toFixedFloat(convert(admin.area_ha).from(MeasurementEnum.MetricUnits).to(measurement), 1)}*/}
                  </FluroTableColumn>

                  <FluroTableColumn>
                    <Box display="flex" gap={3} flexWrap="nowrap" justifyContent="flex-end">
                      {isRegrowAdmin && (
                        <ImpersonateButton
                          email={admin?.email}
                          userId={Number(admin?.user_id)}
                          variant="button"
                        />
                      )}
                      {isRegrowAdmin && (
                        <IconButton
                          onClick={() =>
                            setDeleteProgramAdminData({
                              programId: selectedProgramId,
                              userId: admin.user_id,
                            })
                          }
                        >
                          <SvgIcon type="delete" />
                        </IconButton>
                      )}
                    </Box>
                  </FluroTableColumn>
                </FluroTableRow>
              );
            })}
          </FluroTableBody>
        </FluroDataTable>
      ) : null}
      {isLoadingAdmins ? (
        <CircularProgress className={'progress'} id={'si-admins-table'} />
      ) : filteredProgramAdminsList.length === 0 ? (
        programAdminsList.length !== 0 && debouncedSearchText ? (
          `No program admins found for search = ${debouncedSearchText}`
        ) : (
          'There are currently no admins for the current program.'
        )
      ) : null}
      {bulkAddProgramAdminPopupVisible && (
        <AddUserToSIProgramPopUp
          title={'Add new admin'}
          onHide={() => setAddProgramAdminPopupVisible(false)}
          onAdd={onAddProgramAdmin}
          programId={selectedProgramId}
          userToAddType={'admin'}
        />
      )}
      {!!deleteProgramAdminData && (
        <ConfirmDialog
          title={'Remove program admin'}
          text={
            <div>
              This individual will lose it’s Program Admin access to the platform, but Project and
              User Accounts that this individual manages will remain intact.
            </div>
          }
          onHide={() => setDeleteProgramAdminData(null)}
          onConfirm={() => onRemoveProgramAdmin()}
          saveText="Remove program admin"
        />
      )}
    </div>
  );
};
