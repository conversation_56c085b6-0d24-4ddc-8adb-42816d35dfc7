import range from 'lodash/range';
import {useFeatureFlagEnabled} from 'posthog-js/react';
import React, {useCallback, useMemo, useState} from 'react';
import {FontIcon} from 'react-md';
import styled, {useTheme} from 'styled-components';

import {useAppSelector} from 'store/useRedux';

import {Flex} from '_common/components/flex/flex';
import {FluroButton} from '_common/components/fluro-button/fluro-button';
import {FluroChip} from '_common/components/fluro-chip/fluro-chip';
import {
  FluroDataTable,
  FluroTableBody,
  FluroTableColumn,
  FluroTableRow,
} from '_common/components/fluro-table-components/fluro-table-components';
import {SurveyIcon} from '_common/components/icons/mrv-icons';
import {showNotification} from '_common/components/NotificationSnackbar';
import {Circle} from '_common/components/shapes/circle';
import {Text} from '_common/components/text/text';
import {useParsedMatchParams} from '_common/hooks/use-parsed-match-params';
import {convertUnit} from '_common/utils/conversions';
import {MeasurementEnum} from '_common/utils/measurement-unit-options';
import {isDefined} from '_common/utils/typeGuards';

import {selectMeasurement} from 'containers/login/login-selectors';
import type {
  GlobalKPIDataProductAccess,
  OutcomeKPI,
  PracticeKPI,
  ProgramResponse,
  TabAccess,
} from 'containers/si/api/apiTypes';
import SIApi from 'containers/si/api/si';
import {useFetchProgram} from 'containers/si/api/swr/hooks/useFetchProgram';
import {SIInputPopup} from 'containers/si/components/Legacy/si-input-popup';
import {
  globalKPILabel,
  outcomeKPILabel,
  practiceKPILabel,
  practiceKPILabelWithGreennessLevel,
  tabAccessLabel,
} from 'containers/si/constants';
import {useIsAdmin} from 'containers/si/hooks/useIsAdmin';
import {useTabAccess} from 'containers/si/hooks/useTabAccess';
import {KPIsPopup} from 'containers/si/programs/configure/settings/program-parameters/KPIsPopup/KPIsPopup';
import {SimpleSelector} from 'containers/si/programs/configure/settings/program-parameters/simple-selector';
import {TabAccessPopup} from 'containers/si/programs/configure/settings/program-parameters/tabs-popup';

const StyledFontIcon = styled(FontIcon)`
  color: ${({theme}) => theme.colorPalette.fs_main.green_600} !important;
`;

export const SIProgramParameters = () => {
  const theme = useTheme();
  const {programId} = useParsedMatchParams<{programId: number}>();
  const {programTabAccess} = useTabAccess();
  const {isRegrowAdmin} = useIsAdmin();

  const {data: program, mutate} = useFetchProgram();
  const measurement = useAppSelector(selectMeasurement);

  // TODO: SI-3110 - update practiceKPILabel constant when greenness levels kpi is rolled out
  const isGreennessLevelKpiEnabled = useFeatureFlagEnabled('si-kpi-greennness-levels');
  const practiceKPILabelAdjusted = isGreennessLevelKpiEnabled
    ? practiceKPILabelWithGreennessLevel
    : practiceKPILabel;

  const [kpiPopUp, setKpiPopUp] = useState(false);
  const [tabsPopUp, setTabsPopUp] = useState(false);
  const [configurationPopUp, setConfigurationPopUp] = useState<
    'Area' | 'Program description' | null
  >(null);

  const formattedArea = Number(
    convertUnit(measurement, MeasurementEnum.ImperialUnits, program?.acreage_limit_ha).toFixed()
  );

  const configurationPopUpConfig: {
    [field: string]: {
      key: 'acreage_limit_ha';
      type: 'number';
      defaultValue?: number;
    };
  } = {
    Area: {
      key: 'acreage_limit_ha',
      type: 'number',
      defaultValue: formattedArea,
    },
  };

  const currentYear = useMemo(() => new Date().getFullYear(), []);

  const getYearsList = useMemo(
    () =>
      range(Math.max(currentYear - 10, 2015), currentYear + 6).map(y => ({
        label: y.toString(),
        value: y,
      })),
    [currentYear]
  );

  const onSaveKpis = useCallback(
    async ({
      practice_kpis,
      outcome_kpis,
      global_kpi_data_product_access,
    }: {
      practice_kpis: Array<PracticeKPI>;
      outcome_kpis: Array<OutcomeKPI>;
      global_kpi_data_product_access: Array<GlobalKPIDataProductAccess>;
    }) => {
      await SIApi.updateProgram(programId, {
        practice_kpis,
        outcome_kpis,
        global_kpi_data_product_access,
      });
      void mutate();
    },
    [mutate, programId]
  );

  const onSaveTabs = useCallback(
    async (tabs: Array<TabAccess>) => {
      const updatedProgram: Partial<ProgramResponse> = {tab_accesses: tabs};
      await SIApi.updateProgram(programId, updatedProgram);
      void mutate();
    },
    [mutate, programId]
  );

  const handleUpdateProgramConfiguration = async ({
    key,
    value,
  }: {
    key: 'acreage_limit_ha' | 'name' | 'crop_year_start' | 'crop_year_end';
    value: string | number;
  }) => {
    if (key === 'acreage_limit_ha') {
      value =
        measurement === MeasurementEnum.MetricUnits
          ? value
          : /* Casting hides warnings and errors. It can result in hard to debug code, and unpredictable behaviour. In almost all cases casting isn't needed. */
            /* eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- fixme */
            convertUnit(measurement, MeasurementEnum.MetricUnits, value as number);
    }
    if (key === 'crop_year_start') {
      if (isDefined(program) && Number(value) > program.crop_year_end) {
        showNotification({
          title: 'Error',
          message: `The start year must be before the end year.`,
          type: 'error',
        });
        return;
      }
    }
    if (key === 'crop_year_end') {
      if (isDefined(program) && Number(value) < program.crop_year_start) {
        showNotification({
          title: 'Error',
          message: `The end year must be after the start year.`,
          type: 'error',
        });
        return;
      }
    }
    const updatedProgram: Partial<ProgramResponse> = {[key]: value};
    await SIApi.updateProgram(programId, updatedProgram);
    void mutate();

    setConfigurationPopUp(null);
  };

  return (
    <>
      <FluroDataTable className="program-config-table">
        <FluroTableBody>
          <FluroTableRow hoverBg={false}>
            <FluroTableColumn>
              <Flex nowrap alignItems="center">
                <Circle>
                  <StyledFontIcon>straighten</StyledFontIcon>
                </Circle>
                <Text nowrap inline className="ml-05">
                  {/* Acreage */}
                  {measurement === MeasurementEnum.MetricUnits ? 'Hectares' : 'Acreage'}
                </Text>
              </Flex>
            </FluroTableColumn>
            <FluroTableColumn>
              {formattedArea ?? 0} {measurement}
            </FluroTableColumn>
            <FluroTableColumn>
              <Flex justifyContent="flex-end">
                <FluroButton
                  flat
                  blank
                  disabled={!isRegrowAdmin}
                  onClick={() => setConfigurationPopUp('Area')}
                >
                  edit
                </FluroButton>
              </Flex>
            </FluroTableColumn>
          </FluroTableRow>

          <FluroTableRow hoverBg={false}>
            <FluroTableColumn>
              <Flex nowrap alignItems="center">
                <Circle>
                  <StyledFontIcon>date_range</StyledFontIcon>
                </Circle>
                <Text nowrap inline className="ml-05">
                  Years
                </Text>
              </Flex>
            </FluroTableColumn>
            <FluroTableColumn>
              <div className="pt-2 pb-2">
                <Flex nowrap alignItems="center">
                  <div style={{width: '100px'}}>
                    <SimpleSelector
                      options={getYearsList}
                      value={program?.crop_year_start ?? currentYear}
                      onChange={value =>
                        handleUpdateProgramConfiguration({
                          key: 'crop_year_start',
                          value,
                        })
                      }
                      disabled={!isRegrowAdmin}
                    />
                  </div>
                  <span className="pl-1 pr-1">-</span>
                  <div style={{width: '100px'}}>
                    <SimpleSelector
                      options={getYearsList}
                      value={program?.crop_year_end ?? currentYear}
                      onChange={value =>
                        handleUpdateProgramConfiguration({
                          key: 'crop_year_end',
                          value,
                        })
                      }
                      disabled={!isRegrowAdmin}
                    />
                  </div>
                </Flex>
              </div>
            </FluroTableColumn>
          </FluroTableRow>

          <FluroTableRow hoverBg={false}>
            <FluroTableColumn>
              <Flex nowrap alignItems="center">
                <Circle>
                  <SurveyIcon fill={theme.colorPalette.fs_main.green_600} />
                </Circle>
                <Text nowrap inline className="ml-05">
                  Data Products (KPIs)
                </Text>
              </Flex>
            </FluroTableColumn>
            <FluroTableColumn>
              <Flex gap="4px">
                {program?.outcome_kpis?.map(asset => {
                  const label = outcomeKPILabel[asset];

                  return (
                    isDefined(label) && (
                      <FluroChip tone="light" size="small" label={label} key={asset} />
                    )
                  );
                })}
                {program?.global_kpi_data_product_access?.map(asset => {
                  const label = globalKPILabel[asset];

                  return (
                    isDefined(label) && (
                      <FluroChip tone="light" size="small" label={label} key={asset} />
                    )
                  );
                })}
                {program?.practice_kpis?.map(asset => (
                  <FluroChip
                    tone="light"
                    size="small"
                    label={practiceKPILabelAdjusted[asset]}
                    key={asset}
                  />
                ))}
              </Flex>
            </FluroTableColumn>
            <FluroTableColumn>
              <Flex justifyContent="flex-end">
                <FluroButton flat blank disabled={!isRegrowAdmin} onClick={() => setKpiPopUp(true)}>
                  edit
                </FluroButton>
              </Flex>
            </FluroTableColumn>
          </FluroTableRow>

          <FluroTableRow hoverBg={false}>
            <FluroTableColumn>
              <Flex nowrap alignItems="center">
                <Circle>
                  <StyledFontIcon>check_circle</StyledFontIcon>
                </Circle>
                <Text nowrap inline className="ml-05">
                  Product Features
                </Text>
              </Flex>
            </FluroTableColumn>
            <FluroTableColumn>
              <Flex gap="4px">
                {programTabAccess.map(asset => (
                  <FluroChip tone="light" size="small" label={tabAccessLabel[asset]} key={asset} />
                ))}
              </Flex>
            </FluroTableColumn>
            <FluroTableColumn>
              <Flex justifyContent="flex-end">
                <FluroButton
                  flat
                  blank
                  disabled={!isRegrowAdmin}
                  onClick={() => setTabsPopUp(true)}
                >
                  edit
                </FluroButton>
              </Flex>
            </FluroTableColumn>
          </FluroTableRow>
        </FluroTableBody>
      </FluroDataTable>
      {configurationPopUp && (
        <SIInputPopup
          title={
            measurement === MeasurementEnum.MetricUnits
              ? 'Set Program Hectares'
              : ' Set Program Acreage'
          }
          type={'number'}
          value={formattedArea}
          onSave={async value => {
            const configKey = configurationPopUpConfig[configurationPopUp]?.key;
            if (configKey) {
              await handleUpdateProgramConfiguration({
                key: configKey,
                value,
              });
            }
          }}
          onHide={() => setConfigurationPopUp(null)}
        />
      )}
      {kpiPopUp && (
        <KPIsPopup
          title={'Data Products (KPIs)'}
          outcome_kpis={program?.outcome_kpis ?? []}
          global_kpi_data_product_access={program?.global_kpi_data_product_access ?? []}
          practice_kpis={program?.practice_kpis ?? []}
          onSave={onSaveKpis}
          onHide={() => setKpiPopUp(false)}
        />
      )}
      {tabsPopUp && (
        <TabAccessPopup
          title={'Product Features'}
          tabAccesses={programTabAccess}
          onSave={onSaveTabs}
          onHide={() => setTabsPopUp(false)}
        />
      )}
    </>
  );
};
