import type {Dispatch, FC, ReactElement, SetStateAction} from 'react';

import type {EyeIcon, PartFieldIcon} from '_common/components/icons/map-icons';
import type {SurveyIcon, WinterCropIcon} from '_common/components/icons/mrv-icons';

import type {
  AggregationLevel,
  CoverCropAdopterPracticeOptions,
  CoverCropPracticeOptions,
  DataScenarioResponse,
  TillageAdopterPracticeOptions,
} from 'containers/si/api/apiTypes';
import type {DataScenarioCreateStateValidationErrs} from 'containers/si/programs/configure/data-scenarios/components/CreateSteps/dataScenarios.validation';
import type {STEP_KEYS} from 'containers/si/programs/configure/data-scenarios/constants';
import type {FormState} from 'containers/si/utils/form';

export interface StepConfig {
  title: string;
  subtitle?: string;
  // sorry to do this, we don't have one consolidated icon and icon type definition
  // TODO: update to use DS icons
  StepperIconComponent?:
    | typeof EyeIcon
    | typeof PartFieldIcon
    | typeof SurveyIcon
    | typeof WinterCropIcon;
  StepComponent: FC<StepComponentProps>;
  order: STEP_KEYS;
  isNextDisabled: (validationErrors: DataScenarioCreateStateValidationErrs) => boolean;
}

export type StepsItem = {
  label: string;
  value: number;
  active?: boolean;
  icon: ReactElement;
  screen?: ReactElement;
  disabled: boolean;
};

export interface StepComponentProps {
  formState: DataScenarioFormState;
  onSetFormState: (state: Partial<DataScenarioCreateState>) => void;
  setCurrentStep: Dispatch<SetStateAction<STEP_KEYS>>;
}

// SI-883 This is the FE work for representing reduced till/no till options as "conservation_till"
// BE work to follow; replace this type with the updated BE type at that time
// (this should not live in apiTypes as an override, because the request and response types are unchanged)
export type TillagePracticeOptionsFE = 'conventional_till' | 'conservation_till';

export type FlattenedScenarioFilters = {
  fieldOrFarm: AggregationLevel;
  crops: Array<number> | null;
  cropRotations: Array<string> | null;
  coverCrop: CoverCropPracticeOptions | null;
  coverCropAdopterType: CoverCropAdopterPracticeOptions | null;
  tillage: TillagePracticeOptionsFE | null;
  tillageAdopterType: TillageAdopterPracticeOptions | null;
  size: {min: number | null; max: number | null};
};

export type FlattenedScenario = Pick<DataScenarioResponse, 'id' | 'name' | 'status'> & {
  regions: Array<number>;
} & FlattenedScenarioFilters;

export type DataScenarioCreateState = {
  name: string | null;
  regions: Array<number> | null; // subregion ids
} & FlattenedScenarioFilters;

export type DataScenarioFormState = FormState<
  DataScenarioCreateState,
  DataScenarioCreateStateValidationErrs
>;
