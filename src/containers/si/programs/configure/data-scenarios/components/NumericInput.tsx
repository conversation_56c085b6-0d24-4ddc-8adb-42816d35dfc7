import React from 'react';

import {FSInput} from '_common/components/styled-native-components/FSInput';
import {FSInputGroup} from '_common/components/styled-native-components/FsInputGroup';
import {isEmptyString} from '_common/utils/typeGuards';

import {ErrorTypography} from 'containers/si/programs/configure/data-scenarios/components/ErrorTypography';
import {allPassValidation} from 'containers/si/utils/validation/allPassValidation';
import {nonNilValidation} from 'containers/si/utils/validation/generic';
import type {NumericInputT} from 'containers/si/utils/validation/number';
import {validNumberValidation} from 'containers/si/utils/validation/number';
import {nonEmptyStrValidation} from 'containers/si/utils/validation/string';
import type {Validation} from 'containers/si/utils/validation/types';

type NumericInputProps = {
  /** externalError is for form-level validation. e.g. min < max validation for a pair of numeric inputs  */
  externalError?: string;
  customPredicates?: Array<Validation<NumericInputT>>;
  InputProps?: React.ComponentProps<typeof FSInput>;
} & (
  | {
      required: true;
      setValue: (val: number) => void;
      value: number;
    }
  | {
      required: false;
      setValue: (val: number | null) => void;
      value: number | null;
    }
);

export const NumericInput = ({
  value,
  setValue,
  required,
  customPredicates = [],
  InputProps = {},
  externalError,
}: NumericInputProps) => {
  const [internalError, setInternalError] = React.useState('');
  const defaultValue = React.useRef(value);

  // Reject all key presses that are non-numeric
  const allowOnlyNumbers = React.useCallback((e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key.length === 1 && !/[\d.-]/.test(e.key)) {
      e.preventDefault();
    }
  }, []);

  const defaultPredicates = React.useMemo(
    () => [...(required ? [nonNilValidation, nonEmptyStrValidation] : []), validNumberValidation],
    [required]
  );

  // hooks linter doesn't understand currying
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const allPassValidation_ = React.useCallback(
    allPassValidation([...defaultPredicates, ...customPredicates]),
    [defaultPredicates, customPredicates]
  );

  const handleChange = React.useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const newValue = e.target.value;

      const {allPass, failure} = allPassValidation_(newValue);
      setInternalError(failure.message);

      if (allPass) {
        if (required === false && isEmptyString(newValue)) {
          setValue(null);
        } else {
          setValue(Number(newValue));
        }
      }
    },
    [allPassValidation_, required, setValue]
  );

  return (
    <FSInputGroup>
      <FSInput
        type="number"
        defaultValue={defaultValue.current ?? undefined} // use a ref to keep value stable
        onKeyDown={allowOnlyNumbers}
        onChange={handleChange}
        {...InputProps}
      />

      <ErrorTypography>{(internalError || externalError) ?? ''}</ErrorTypography>
    </FSInputGroup>
  );
};
