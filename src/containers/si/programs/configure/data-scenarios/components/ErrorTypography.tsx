// This file should be obsolete when we have FormControl from DS
import React from 'react';

import {Box, Typography, useTheme} from '@regrow-internal/design-system';

export const ErrorTypography: React.FC = ({children}) => {
  const theme = useTheme();

  return (
    <Box minHeight={16}>
      <Typography variant="body2" color={theme.palette.error.main}>
        {children}
      </Typography>
    </Box>
  );
};
