import React from 'react';
import {useSWRConfig} from 'swr';

import {
  Button,
  DialogActions,
  DialogContent,
  SimpleDialog,
  TextField,
} from '@regrow-internal/design-system';

import {showNotification} from '_common/components/NotificationSnackbar';
import {isNonEmptyString} from '_common/utils/typeGuards';

import SIApi from 'containers/si/api/si';
import {siUrls} from 'containers/si/api/swr/siUrls';
import {nameValidation} from 'containers/si/programs/configure/data-scenarios/components/CreateSteps/steps/validation.nameAndRegion';
import {ErrorTypography} from 'containers/si/programs/configure/data-scenarios/components/ErrorTypography';
import {allPassValidation} from 'containers/si/utils/validation/allPassValidation';

import type {ModalProps} from './modals.types';

export const EditModal = (props: ModalProps) => {
  const {mutate} = useSWRConfig();
  const [scenarioName, setScenarioName] = React.useState(props.scenarioName);

  const [error, setError] = React.useState('');

  const saveDisabled = scenarioName === props.scenarioName || isNonEmptyString(error);

  const handleSave = React.useCallback(() => {
    SIApi.renameDataScenario(props.programId, props.scenarioId, {name: scenarioName})
      .then(() => {
        showNotification({
          type: 'success',
          message: 'Data scenario successfully renamed.',
        });
        void mutate(siUrls.fetchProgram(props.programId)); // update program
        props.onClose();
      })
      .catch(() => {
        showNotification({
          type: 'error',
          message: 'Data scenario could not be renamed.',
        });
      });
  }, [mutate, props, scenarioName]);

  return (
    <SimpleDialog
      id={`rename-${props.scenarioId}`}
      open
      onClose={props.onClose}
      title={`Rename Data Scenario`}
    >
      <DialogContent>
        <TextField
          fullWidth
          placeholder="Scenario name"
          label="Name your data scenario"
          onChange={ev => {
            setScenarioName(ev.target.value);
            setError(allPassValidation(nameValidation)(ev.target.value).failure.message);
          }}
          value={scenarioName}
        />
        <ErrorTypography>{error}</ErrorTypography>
      </DialogContent>
      <DialogActions>
        <Button variant="outlined" color="secondary" onClick={props.onClose}>
          Cancel
        </Button>
        <Button variant="contained" color="primary" disabled={saveDisabled} onClick={handleSave}>
          Save
        </Button>
      </DialogActions>
    </SimpleDialog>
  );
};
