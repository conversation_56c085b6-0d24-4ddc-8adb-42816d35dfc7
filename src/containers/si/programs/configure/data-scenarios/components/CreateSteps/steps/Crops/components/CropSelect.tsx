import React, {useMemo} from 'react';

import {CropTypeSelect} from 'containers/si/components/Legacy/CropTypeSelect';
import {useGetCropLabelById} from 'containers/si/hooks/useGetCropLabelById';
import {useProgramCrops} from 'containers/si/hooks/useProgramCrops';
import {StyledHackForFluroSelect} from 'containers/si/programs/configure/data-scenarios/components/data-scenarios.styled';
import {ErrorTypography} from 'containers/si/programs/configure/data-scenarios/components/ErrorTypography';

interface CropSelectProps {
  subregionIds: Array<number>;
  selectedCropIds: Array<number>;
  onChange: (cropIds?: Array<number>) => void;
  error: string;
}

/**
 * @deprecated TODO: Replace all usages with si/components/CropSelect/ProgramCropsSelect
 */
export const CropSelect: React.FC<CropSelectProps> = ({
  subregionIds,
  selectedCropIds,
  onChange,
  error,
}) => {
  // This is a transition block of code to accommodate for slowly deprecating legacy crop helpers and legacy crop selects
  // In general, this should be managed by the updated CropSelect
  const {cropIds, isLoading: isProgramCropsLoading} = useProgramCrops(subregionIds);
  const {getSISupportedCropLabelById, isLoading: isGetCropHelpersLoading} = useGetCropLabelById();

  const cropOptions = useMemo(() => {
    if (isProgramCropsLoading || isGetCropHelpersLoading) return [];

    return cropIds.map(id => ({
      label: getSISupportedCropLabelById(id),
      value: String(id),
      icon: '',
      type: '',
      color: '',
    }));
  }, [isProgramCropsLoading, isGetCropHelpersLoading, cropIds, getSISupportedCropLabelById]);

  return (
    <>
      <StyledHackForFluroSelect>
        <CropTypeSelect
          cropTypeOptions={cropOptions}
          onChange={onChange}
          selectedCropIds={selectedCropIds}
        />
      </StyledHackForFluroSelect>
      <ErrorTypography>{error}</ErrorTypography>
    </>
  );
};
