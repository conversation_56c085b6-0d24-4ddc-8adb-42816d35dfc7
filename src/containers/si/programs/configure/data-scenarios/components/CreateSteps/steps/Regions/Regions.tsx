import uniq from 'lodash/uniq';
import React from 'react';

import {isDefined} from '_common/utils/typeGuards';

import {useFetchSupplySheds} from 'containers/si/api/swr/hooks/useFetchSupplySheds';
import {findSupplyShedIdBySubsectionId} from 'containers/si/module/helpers/supply-shed-helpers';
import {RegionSelectWithMap} from 'containers/si/programs/configure/data-scenarios/components/CreateSteps/steps/Regions/components/RegionSelectWithMap';
import {ErrorTypography} from 'containers/si/programs/configure/data-scenarios/components/ErrorTypography';
import type {
  DataScenarioCreateState,
  StepComponentProps,
} from 'containers/si/programs/configure/data-scenarios/types';
import {showErrorOnDirty} from 'containers/si/utils/form';

export const Regions: React.FC<StepComponentProps> = ({formState, onSetFormState}) => {
  const setRegions = (subsectionIds: DataScenarioCreateState['regions'] = null) => {
    onSetFormState({
      regions: subsectionIds,
    });
  };

  const {data: programSupplySheds} = useFetchSupplySheds();

  const selectedSubsections = formState.state.regions ?? [];

  const initialSelectedSupplySheds = React.useMemo(() => {
    const selectedSupplySheds: Array<number> = uniq(
      selectedSubsections
        .map(subsectionId => findSupplyShedIdBySubsectionId(programSupplySheds ?? [], subsectionId))
        .filter(isDefined)
    );

    return selectedSupplySheds;
    // This should only run once when component mounts
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <>
      <ErrorTypography>{showErrorOnDirty(formState, 'regions')}</ErrorTypography>
      <RegionSelectWithMap
        onChange={subsectionIds => setRegions(subsectionIds)}
        selectedSubsections={selectedSubsections}
        initialSelectedSupplySheds={initialSelectedSupplySheds}
      />
    </>
  );
};
