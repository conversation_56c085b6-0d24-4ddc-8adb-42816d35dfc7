import React from 'react';

import {Box, Typography} from '@regrow-internal/design-system';

import type {MeasurementEnum} from '_common/utils/measurement-unit-options';

import {NumericInput} from 'containers/si/programs/configure/data-scenarios/components/NumericInput';
import {FIELD_OR_FARM_LABEL} from 'containers/si/programs/configure/data-scenarios/constants';
import type {DataScenarioCreateState} from 'containers/si/programs/configure/data-scenarios/types';
import {measurementDisplay} from 'containers/si/utils/formatters/format';

type FieldSizeRangeInputProps = {
  measurement: MeasurementEnum;
  size: DataScenarioCreateState['size'];
  fieldOrFarm: DataScenarioCreateState['fieldOrFarm'];
  setSize: ({min, max}: DataScenarioCreateState['size']) => void;
  errors: {min: string; max: string};
};

export const SizeRangeInput = ({
  measurement,
  size: {min, max},
  fieldOrFarm,
  setSize,
  errors,
}: FieldSizeRangeInputProps) => {
  return (
    <Box mt={4} minHeight={102}>
      <Typography variant="body2">{`${
        FIELD_OR_FARM_LABEL[fieldOrFarm]
      } size, in ${measurementDisplay(measurement)} (optional)`}</Typography>
      <Box display={'flex'} gap={2}>
        <Box width={200}>
          <NumericInput
            required={false}
            value={min}
            externalError={errors.min}
            InputProps={{placeholder: `Minimum ${fieldOrFarm} Size`, min: 0}}
            setValue={v => setSize({max, min: v})}
          />
        </Box>
        <AlignmentBox>{'-'}</AlignmentBox>
        <Box width={200}>
          <NumericInput
            required={false}
            value={max}
            externalError={errors.max}
            InputProps={{placeholder: `Maximum ${fieldOrFarm} Size`, min: 0}}
            setValue={v => setSize({min, max: v})}
          />
        </Box>
      </Box>
    </Box>
  );
};

const AlignmentBox = (props: {children: React.ReactNode}) => (
  <Box display={'flex'} alignItems={'center'} height={44} {...props} />
);
