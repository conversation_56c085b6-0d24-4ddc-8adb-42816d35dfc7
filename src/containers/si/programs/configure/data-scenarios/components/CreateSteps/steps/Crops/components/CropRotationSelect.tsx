import difference from 'lodash/difference';
import React from 'react';

import {Box, styled, Tooltip, Typography, useTheme} from '@regrow-internal/design-system';

import type {Option} from 'types';

import type {Props as OptionsGroupsProps} from '_common/components/grouped-selection-control/options-group';
import {toggle} from '_common/utils/pure-utils';
import {isEmptyArray, isNonEmptyArray, isNonEmptyString} from '_common/utils/typeGuards';

import {AreaSelectMenu} from 'containers/si/components/Legacy/AreaSelect/AreaSelectMenu';
import {StyledHackForFluroSelect} from 'containers/si/programs/configure/data-scenarios/components/data-scenarios.styled';
import {ErrorTypography} from 'containers/si/programs/configure/data-scenarios/components/ErrorTypography';

interface CropRotationSelectProps {
  onChange: (selected: Array<string>) => void;
  selectedCropRotations: Array<string>;
  error: string;
  cropRotationOptions: Array<Option>;
  allCropRotations: Array<string>;
}

/**
 * @deprecated TODO: Replace all usages with an updated CropRotationSelect which follows pattern in si/components/CropSelect/
 */
export const CropRotationSelect: React.FC<CropRotationSelectProps> = ({
  onChange,
  cropRotationOptions,
  allCropRotations,
  selectedCropRotations,
  error,
}) => {
  const theme = useTheme();

  const cropRotationOptionsEmpty = isEmptyArray(cropRotationOptions);

  const handleSelect: OptionsGroupsProps['onChange'] = selected => {
    if (typeof selected === 'boolean') {
      if (selected) {
        isNonEmptyArray(difference(allCropRotations, selectedCropRotations))
          ? onChange(allCropRotations)
          : onChange([]);
      }
    } else {
      const cropRotations: Array<string> = toggle<string>(
        selectedCropRotations,
        String(selected.value)
      );
      onChange(cropRotations);
    }
  };

  return (
    <>
      <Tooltip
        id="crop-rotation-select-disabled"
        title={cropRotationOptionsEmpty ? 'No common crop rotations found' : ''}
      >
        <StyledHackForFluroSelect>
          <StyledOverrideForLongerRotationStrings>
            <AreaSelectMenu
              className="area-select-menu"
              allowSelectAll
              allowSearch
              noneSelectedLabel="Select most common rotations"
              allLabel="Select all rotations"
              selectedLabel="rotations"
              onSelect={handleSelect}
              options={cropRotationOptions}
              disabled={cropRotationOptionsEmpty}
              selected={selectedCropRotations}
            />
          </StyledOverrideForLongerRotationStrings>
        </StyledHackForFluroSelect>
      </Tooltip>

      {!cropRotationOptionsEmpty &&
        (isNonEmptyString(error) ? (
          <ErrorTypography>{error}</ErrorTypography>
        ) : (
          <Box minHeight={16}>
            <Typography variant="body2" color={theme.palette.text.secondary}>
              Crops marked with an asterisk (*) are not currently supported and will not be shown in
              the data displayed
            </Typography>
          </Box>
        ))}
    </>
  );
};

//TODO: Remove this once DS select is available
const StyledOverrideForLongerRotationStrings = styled('div')`
  .md-selection-control-container {
    height: auto;
    max-width: 220px;
  }
`;
