import React from 'react';

import {FluroInput} from '_common/components/fluro-form-components';

import {ErrorTypography} from 'containers/si/programs/configure/data-scenarios/components/ErrorTypography';
import type {StepComponentProps} from 'containers/si/programs/configure/data-scenarios/types';
import {showErrorOnDirty} from 'containers/si/utils/form';

export const ScenarioName: React.FC<StepComponentProps> = ({formState, onSetFormState}) => {
  const setScenarioName = React.useCallback(
    (scenarioName: string = '') => {
      onSetFormState({name: scenarioName || null});
    },
    [onSetFormState]
  );

  return (
    // TODO: replace with DS input
    <>
      <FluroInput
        placeholder="Scenario name"
        label="Name your data scenario"
        onChange={setScenarioName}
        value={formState.state.name ?? undefined}
      />
      <ErrorTypography>{showErrorOnDirty(formState, 'name')}</ErrorTypography>
    </>
  );
};
