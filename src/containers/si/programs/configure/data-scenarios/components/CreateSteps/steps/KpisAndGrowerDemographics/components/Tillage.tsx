import React from 'react';

import {Box, Checkbox, FormControlLabel} from '@regrow-internal/design-system';

import {AdopterTypeSelect} from 'containers/si/programs/configure/data-scenarios/components/CreateSteps/steps/KpisAndGrowerDemographics/components/AdopterTypeSelect';
import {
  PracticeOptionsWrapper,
  PracticeSelection,
} from 'containers/si/programs/configure/data-scenarios/components/CreateSteps/steps/KpisAndGrowerDemographics/components/viewComponents';
import type {TillageState} from 'containers/si/programs/configure/data-scenarios/components/CreateSteps/steps/KpisAndGrowerDemographics/validation.kpiDemographics';
import {ErrorTypography} from 'containers/si/programs/configure/data-scenarios/components/ErrorTypography';
import {
  TILLAGE_PRACTICE_LABELS,
  TILLAGE_PRACTICES,
} from 'containers/si/programs/configure/data-scenarios/constants';
import type {DataScenarioCreateState} from 'containers/si/programs/configure/data-scenarios/types';

type TillageProps = {
  setTillageAndTillageAdopter: (state: TillageState) => void;
  tillageAndTillageAdopter: TillageState;
  errors: {tillage: string; tillageAdopterType: string};
};

export const Tillage = ({
  tillageAndTillageAdopter: {tillage, tillageAdopterType},
  setTillageAndTillageAdopter,
  errors,
}: TillageProps) => {
  const adopterSelectionIsEnabled = React.useCallback(
    (tillVal?: DataScenarioCreateState['tillage']) => tillVal === 'conservation_till',
    []
  );

  const onChange = React.useCallback(
    (state: Partial<TillageState>) => {
      setTillageAndTillageAdopter({
        tillage,
        // Ensure no state where adopter hidden and invalid
        tillageAdopterType: adopterSelectionIsEnabled(state.tillage) ? tillageAdopterType : null,
        ...state,
      });
    },
    [tillage, adopterSelectionIsEnabled, tillageAdopterType, setTillageAndTillageAdopter]
  );

  return (
    <PracticeSelection titleText="Tillage" iconType={'tillage'}>
      <PracticeOptionsWrapper>
        {TILLAGE_PRACTICES.map(practice => (
          <Box key={practice} margin={2}>
            <FormControlLabel
              label={TILLAGE_PRACTICE_LABELS[practice]}
              control={
                <Checkbox
                  checked={tillage === practice}
                  onChange={e => onChange({tillage: e.target.checked ? practice : null})}
                  inputProps={{'aria-label': `select ${practice}`}}
                />
              }
            />
          </Box>
        ))}
        <ErrorTypography>{errors.tillage}</ErrorTypography>
      </PracticeOptionsWrapper>
      {adopterSelectionIsEnabled(tillage) && (
        <AdopterTypeSelect
          adopterType={tillageAdopterType}
          setAdopterType={adopterType => onChange({tillageAdopterType: adopterType})}
          error={errors.tillageAdopterType}
        />
      )}
    </PracticeSelection>
  );
};
