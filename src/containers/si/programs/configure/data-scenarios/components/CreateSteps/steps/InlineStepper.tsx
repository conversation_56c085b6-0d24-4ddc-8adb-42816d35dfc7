import React from 'react';
import styled from 'styled-components';

import {Button, SvgIcon} from '@regrow-internal/design-system';

import FluroSteps from '_common/components/fluro-steps/fluro-steps-dynamic';
import {isDefined} from '_common/utils/typeGuards';

import type {StepsItem} from 'containers/si/programs/configure/data-scenarios/types';

export const InlineStepper = ({
  stepItems,
  currentStep,
  onUpdateStep,
  finalStepComponent,
  hideBackNextControls = false,
}: {
  onUpdateStep: (step: number) => void;
  currentStep: number;
  stepItems: Array<StepsItem>; // should be sorted by value;
  finalStepComponent?: React.ReactElement;
  hideBackNextControls?: boolean;
}) => {
  const firstStep = stepItems[0];
  const lastStep = stepItems[stepItems.length - 1];
  const previousStep = stepItems[getStepIndexByValue(currentStep - 1, stepItems)] ?? firstStep;
  const nextStep = stepItems[getStepIndexByValue(currentStep + 1, stepItems)] ?? lastStep;

  return (
    <StyledStepperWrapper>
      {!hideBackNextControls && (
        <Button
          disabled={currentStep === firstStep?.value}
          onClick={() => previousStep && onUpdateStep(previousStep.value)}
          startIcon={<SvgIcon type="chevron-left" />}
        >
          Back
        </Button>
      )}

      <FluroSteps items={stepItems} onItemClick={onUpdateStep} selectedItem={currentStep} />

      {!hideBackNextControls &&
        (currentStep === lastStep?.value && isDefined(finalStepComponent) ? (
          finalStepComponent
        ) : (
          <Button
            disabled={currentStep === lastStep?.value || nextStep?.disabled}
            onClick={() => nextStep !== undefined && onUpdateStep(nextStep?.value)}
            endIcon={<SvgIcon type="chevron-right" />}
          >
            Next
          </Button>
        ))}
    </StyledStepperWrapper>
  );
};

const getStepIndexByValue = (value: string | number, steps: Array<StepsItem>) =>
  steps.findIndex(step => step.value && step.value === value);

const StyledStepperWrapper = styled.div`
  display: flex;
  gap: 20px;
  align-items: flex-start;
  justify-content: center;

  /* Unfortunately, using styled(FluroSteps) doesn't work, and I have to target classname */
  .fluro-steps-container {
    width: fit-content;
    margin: -10px 0 0 0;
    padding: 0;
  }
  .fluro-step {
    width: unset;
    max-width: 150px;
    .line {
      top: 15px;
    }
  }
`;
