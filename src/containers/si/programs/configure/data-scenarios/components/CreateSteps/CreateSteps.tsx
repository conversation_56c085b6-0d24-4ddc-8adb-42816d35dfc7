import union from 'lodash/union';
import React, {useState} from 'react';

import {Box, useTheme, type SimpleDialogProps} from '@regrow-internal/design-system';

import {getTypedKeys} from '_common/utils/object';

import {CreateDialog} from 'containers/si/programs/configure/data-scenarios/components/CreateDialog';
import {validateCreateState} from 'containers/si/programs/configure/data-scenarios/components/CreateSteps/dataScenarios.validation';
import {InlineStepper} from 'containers/si/programs/configure/data-scenarios/components/CreateSteps/steps/InlineStepper';
import {INITIAL_STATE, STEP_KEYS} from 'containers/si/programs/configure/data-scenarios/constants';
import {makeStepperItems} from 'containers/si/programs/configure/data-scenarios/helpers';
import type {
  DataScenarioCreateState,
  DataScenarioFormState,
} from 'containers/si/programs/configure/data-scenarios/types';
import {useSubmitForm} from 'containers/si/programs/configure/data-scenarios/useSubmitForm';

import {STEPS_CONFIG} from './steps/config';

interface CreateStepsProps {
  onClose: () => void;
  isVisible: SimpleDialogProps['open'];
}

const initialFormState: DataScenarioFormState = {
  state: INITIAL_STATE,
  dirty: [],
  errors: validateCreateState(INITIAL_STATE).errors,
};

export const CreateSteps = ({onClose, isVisible}: CreateStepsProps) => {
  const theme = useTheme();
  const [dataScenarioFormState, setDataScenarioFormState] =
    useState<DataScenarioFormState>(initialFormState);
  const [currentStep, setCurrentStep] = useState<STEP_KEYS>(0);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const submitForm = useSubmitForm(onClose, setIsSubmitting);

  const onSetFormState = React.useCallback(
    (newState: Partial<DataScenarioCreateState>) => {
      const dirty = union(getTypedKeys(newState), dataScenarioFormState.dirty);
      const state = {...dataScenarioFormState.state, ...newState};
      const {errors} = validateCreateState(state);
      setDataScenarioFormState({dirty, state, errors});
    },
    [dataScenarioFormState.dirty, dataScenarioFormState.state]
  );

  const handleClose = () => {
    onClose();
    setDataScenarioFormState(initialFormState);
    setCurrentStep(0);
  };

  const handleBackClick = () => {
    setCurrentStep(prev => (prev > 0 ? prev - 1 : prev));
  };

  const handleContinueClick = () => {
    if (currentStep === STEP_KEYS['SUMMARY']) {
      submitForm(dataScenarioFormState.state);
    } else {
      setCurrentStep(prev => prev + 1);
    }
  };

  const currentStepContent =
    STEPS_CONFIG.find(step => step.order === currentStep) ?? STEPS_CONFIG[0];

  const CurrentStepComponent = currentStepContent?.StepComponent ?? (() => <></>);
  const stepperItems = makeStepperItems(
    STEPS_CONFIG,
    dataScenarioFormState.errors,
    theme.palette.semanticPalette.text.brand
  );

  return (
    <CreateDialog
      title={currentStepContent?.title ?? ''}
      subtitle={currentStepContent?.subtitle}
      handleBackClick={handleBackClick}
      handleClose={handleClose}
      handleContinueClick={handleContinueClick}
      isFirstStep={currentStep === STEP_KEYS['NAME']}
      isLastStep={currentStep === STEP_KEYS['SUMMARY']}
      isNextDisabled={currentStepContent?.isNextDisabled(dataScenarioFormState.errors) === true}
      isVisible={isVisible}
      isSubmitting={isSubmitting}
    >
      {currentStep !== STEP_KEYS['NAME'] && (
        <Box my={5}>
          <InlineStepper
            stepItems={stepperItems}
            onUpdateStep={v => setCurrentStep(Number(v))}
            currentStep={currentStep}
            hideBackNextControls
          />
        </Box>
      )}
      <CurrentStepComponent
        formState={dataScenarioFormState}
        onSetFormState={onSetFormState}
        setCurrentStep={setCurrentStep}
      />
    </CreateDialog>
  );
};
