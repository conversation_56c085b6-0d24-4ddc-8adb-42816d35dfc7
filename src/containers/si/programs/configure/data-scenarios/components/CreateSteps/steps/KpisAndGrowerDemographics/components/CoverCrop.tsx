import React from 'react';

import {Box, Checkbox, FormControlLabel} from '@regrow-internal/design-system';

import {AdopterTypeSelect} from 'containers/si/programs/configure/data-scenarios/components/CreateSteps/steps/KpisAndGrowerDemographics/components/AdopterTypeSelect';
import {
  PracticeOptionsWrapper,
  PracticeSelection,
} from 'containers/si/programs/configure/data-scenarios/components/CreateSteps/steps/KpisAndGrowerDemographics/components/viewComponents';
import type {CoverCropState} from 'containers/si/programs/configure/data-scenarios/components/CreateSteps/steps/KpisAndGrowerDemographics/validation.kpiDemographics';
import {ErrorTypography} from 'containers/si/programs/configure/data-scenarios/components/ErrorTypography';
import {
  COVER_CROP_PRACTICE_LABELS,
  COVER_CROP_PRACTICES,
} from 'containers/si/programs/configure/data-scenarios/constants';

type CoverCropProps = {
  coverCropAndAdopter: CoverCropState;
  setCoverCropAndAdopter: (state: CoverCropState) => void;
  errors: {coverCrop: string; coverCropAdopterType: string};
};

export const CoverCrop = ({
  coverCropAndAdopter: {coverCrop, coverCropAdopterType},
  setCoverCropAndAdopter,
  errors,
}: CoverCropProps) => {
  const adopterSelectionIsEnabled = React.useCallback(cc => cc === 'cover_crop', []);

  const onChange = React.useCallback(
    (state: Partial<CoverCropState>) => {
      setCoverCropAndAdopter({
        coverCrop,
        // Ensure no state where adopter hidden and invalid
        coverCropAdopterType: adopterSelectionIsEnabled(state.coverCrop)
          ? coverCropAdopterType
          : null,
        ...state,
      });
    },
    [coverCrop, coverCropAdopterType, setCoverCropAndAdopter, adopterSelectionIsEnabled]
  );

  return (
    <PracticeSelection titleText="Cover Crop" iconType={'cover-crop'}>
      <PracticeOptionsWrapper>
        {COVER_CROP_PRACTICES.map(practice => (
          <Box key={practice} margin={2}>
            <FormControlLabel
              label={COVER_CROP_PRACTICE_LABELS[practice]}
              control={
                <Checkbox
                  checked={coverCrop === practice}
                  onChange={e => onChange({coverCrop: e.target.checked ? practice : null})}
                  inputProps={{'aria-label': `select ${practice}`}}
                />
              }
            />
          </Box>
        ))}
        <ErrorTypography>{errors.coverCrop}</ErrorTypography>
      </PracticeOptionsWrapper>
      {adopterSelectionIsEnabled(coverCrop) && (
        <AdopterTypeSelect
          adopterType={coverCropAdopterType}
          setAdopterType={adopterType => onChange({coverCropAdopterType: adopterType})}
          error={errors.coverCropAdopterType}
        />
      )}
    </PracticeSelection>
  );
};
