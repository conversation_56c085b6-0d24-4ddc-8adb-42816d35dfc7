import compact from 'lodash/compact';
import nth from 'lodash/nth';
import React from 'react';

import type {MeasurementEnum} from '_common/utils/measurement-unit-options';
import {isDefined} from '_common/utils/typeGuards';

import type {
  CoverCropAdopterFilter,
  CoverCropFilter,
  CropFilter,
  CropRotationFilter,
  DataScenarioRequest,
  SizeFilter,
  TillageAdopterFilter,
  TillageFilter,
  TillagePracticeOptions,
} from 'containers/si/api/apiTypes';
import type {DataScenarioCreateStateValidationErrs} from 'containers/si/programs/configure/data-scenarios/components/CreateSteps/dataScenarios.validation';
import type {
  DataScenarioCreateState,
  StepConfig,
  StepsItem,
} from 'containers/si/programs/configure/data-scenarios/types';
import {convertWithUnit} from 'containers/si/utils/convert';
import {fnIfDefinedElseNull} from 'containers/si/utils/utils';

export const makeStepperItems = (
  steps: Array<StepConfig>,
  validationErrors: DataScenarioCreateStateValidationErrs,
  iconColor: string
) => {
  const orderedSteps = [...steps].sort((a, b) => a.order - b.order);

  return orderedSteps
    .map(({title, StepperIconComponent, order}, i) => {
      // stepper doesn't check if the next value is disabled, but rather if THIS step is disabled
      const isDisabled = nth(orderedSteps, i - 1)?.isNextDisabled(validationErrors) ?? false;

      /* Casting hides warnings and errors. It can result in hard to debug code, and unpredictable behaviour. In almost all cases casting isn't needed. */
      /* eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- fixme */
      return {
        label: title,
        icon: StepperIconComponent ? <StepperIconComponent fill={iconColor} /> : undefined,
        disabled: isDisabled,
        value: Number(order),
      } as StepsItem;
    })
    .filter(stepItem => isDefined(stepItem.icon));
};

// #region transform
export const transformSize = (
  {size, fieldOrFarm: aggregation_level}: Pick<DataScenarioCreateState, 'size' | 'fieldOrFarm'>,
  measurement: MeasurementEnum
): SizeFilter | null => {
  const convertedMin = fnIfDefinedElseNull(
    size.min,
    n => convertWithUnit({value: n, from: measurement, to: 'm2'}).value
  );
  const convertedMax = fnIfDefinedElseNull(
    size.max,
    n => convertWithUnit({value: n, from: measurement, to: 'm2'}).value
  );

  return isDefined(convertedMin) || isDefined(convertedMax)
    ? {
        filter_type: 'size',
        aggregation_level,
        min_area_m2: convertedMin ?? undefined,
        max_area_m2: convertedMax ?? undefined,
      }
    : null;
};

export const transformCrops = (crops: DataScenarioCreateState['crops']): CropFilter | null =>
  fnIfDefinedElseNull(crops, ids => ({filter_type: 'crop', crop_ids: ids}));

export const transformCropRotations = (
  rotations: DataScenarioCreateState['cropRotations']
): CropRotationFilter | null =>
  fnIfDefinedElseNull(rotations, rotationSets => ({
    filter_type: 'crop_rotation',
    crop_rotation_csvs: rotationSets,
  }));

export const transformTillage = ({
  tillage,
  fieldOrFarm: aggregation_level,
}: Pick<DataScenarioCreateState, 'tillage' | 'fieldOrFarm'>): TillageFilter | null => {
  // Convert the local type "conservation_tillage" to ["reduce_till", "no_till"]
  const getTillageValue = (): Array<TillagePracticeOptions> | null => {
    switch (tillage) {
      case null:
        return null;
      case 'conventional_till':
        return ['conventional_till'];
      case 'conservation_till':
        return ['reduce_till', 'no_till'];
    }
  };

  return fnIfDefinedElseNull(getTillageValue(), practices => ({
    filter_type: 'tillage',
    aggregation_level,
    practices,
  }));
};

export const transformTillageAdopter = ({
  tillageAdopterType,
  fieldOrFarm: aggregation_level,
}: Pick<
  DataScenarioCreateState,
  'tillageAdopterType' | 'fieldOrFarm'
>): TillageAdopterFilter | null =>
  fnIfDefinedElseNull(tillageAdopterType, adopter => ({
    filter_type: 'tillage_adopter',
    aggregation_level,
    practices: [adopter],
  }));

export const transformCoverCrop = ({
  coverCrop,
  fieldOrFarm: aggregation_level,
}: Pick<DataScenarioCreateState, 'coverCrop' | 'fieldOrFarm'>): CoverCropFilter | null =>
  fnIfDefinedElseNull(coverCrop, practice => ({
    filter_type: 'cover_crop',
    aggregation_level,
    practices: [practice],
  }));

export const transformCoverCropAdopter = ({
  coverCropAdopterType,
  fieldOrFarm: aggregation_level,
}: Pick<
  DataScenarioCreateState,
  'coverCropAdopterType' | 'fieldOrFarm'
>): CoverCropAdopterFilter | null =>
  fnIfDefinedElseNull(coverCropAdopterType, adopter => ({
    filter_type: 'cover_crop_adopter',
    aggregation_level,
    practices: [adopter],
  }));

// There should never be an empty case, since validators will already have run
export const transformRegions = (
  regions: DataScenarioCreateState['regions']
): DataScenarioRequest['sourcing_regions'] =>
  (regions ?? []).map(id => ({unit_type: 'subsection', unit_id: id}));

export const transformCreateStateToRequest = (
  createState: DataScenarioCreateState,
  measurement: MeasurementEnum
): DataScenarioRequest => {
  const field_level_filters: DataScenarioRequest['field_level_filters'] = compact([
    transformSize(createState, measurement),
    transformCrops(createState.crops),
    transformCropRotations(createState.cropRotations),
    transformTillage(createState),
    transformTillageAdopter(createState),
    transformCoverCrop(createState),
    transformCoverCropAdopter(createState),
  ]);

  return {
    name: String(createState.name), // There should never be an empty case, since validators will already have run
    sourcing_regions: transformRegions(createState.regions),
    field_level_filters,
  };
};

// #endregion transform
