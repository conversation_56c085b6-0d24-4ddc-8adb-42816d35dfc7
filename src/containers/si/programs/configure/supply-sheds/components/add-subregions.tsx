import {push} from 'connected-react-router';
import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {Link, Redirect} from 'react-router-dom';
import {useSWRConfig} from 'swr';

import {
  Box,
  Button,
  CircularProgress,
  FormControl,
  FormControlLabel,
  FormLabel,
  LoadingButton,
  Stack,
  Switch,
  TextField,
  Typography,
} from '@regrow-internal/design-system';

import {useAppDispatch, useAppSelector} from 'store/useRedux';

import {showNotification} from '_common/components/NotificationSnackbar';
import {useParsedMatchParams} from '_common/hooks/use-parsed-match-params';
import {applyPathParams} from '_common/utils/routing-helpers';
import {isDefined} from '_common/utils/typeGuards';

import type {CommodityItem, SubsectionCreateRequest} from 'containers/si/api/apiTypes';
import SIApi from 'containers/si/api/si';
import {useFetchGeometries} from 'containers/si/api/swr/hooks/useFetchGeometries';
import {useFetchSupplyShedsWithSortAndSetColor} from 'containers/si/api/swr/hooks/useFetchSupplyShedsWithSortAndSetColor';
import {siUrls} from 'containers/si/api/swr/siUrls';
import {CenteredLoaderContainer} from 'containers/si/components/CenteredLoaderContainer';
import {AllCropsSelect} from 'containers/si/components/filters/CropsSelect/AllCropsSelect';
import {useIsAdmin} from 'containers/si/hooks/useIsAdmin';
import {findSupplyShedById} from 'containers/si/module/helpers/supply-shed-helpers';
import {
  setInteractionType,
  setProcessing,
  setProgramGeometries,
  setShouldShowMapTools,
  setShowProgramGeometries,
  setSubsectionGeometryById,
  setSubsectionSelectedFeatureById,
} from 'containers/si/module/reducer';
import {selectGetColorBySupplyShedId} from 'containers/si/module/selectors/entities.selectors';
import {
  selectInteractionType,
  selectSubsectionFeatures,
  selectSubsectionGeometries,
} from 'containers/si/module/selectors/map.selectors';
import type {FeatureById} from 'containers/si/module/types';
import {UploadBoundariesPopup} from 'containers/si/programs/configure/supply-sheds/components/map/upload-boundaries-popup';
import {SubsectionCard} from 'containers/si/programs/configure/supply-sheds/components/subsection-card';
import {
  getSubsectionFeatureId,
  getSubsectionGeometryName,
  getSubsectionSelectedFeatureCollectionId,
  getSubsectionSelectedFeatureName,
} from 'containers/si/programs/configure/supply-sheds/feature-helpers';
import {SI_SUPPLY_SHED_CONFIG} from 'containers/si/routing/routes';

/**
 * Takes all selections and combines into a single subregion
 */
const makeCombinedSubregion = (
  subsectionGeometries: Array<FeatureById[keyof FeatureById]>,
  subsectionSelectedFeatures: Array<FeatureById[keyof FeatureById]>,
  commodities: Array<CommodityItem>,
  supplyShedId: number,
  newSubregionName: string
) => {
  const combinedFeatures: NonNullable<SubsectionCreateRequest['selected_features']> =
    subsectionSelectedFeatures.map(feature => ({
      // feature.properties are untyped
      // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
      feature_id: feature.properties?.id ?? feature.properties?.FIPS,
      // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
      collection_id: feature.properties?.collection_id ?? feature.properties?.layer,
      // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
      feature_name:
        feature.properties?.feature_name ?? feature.properties?.title ?? feature.properties?.name,
    }));

  const newSubsection: SubsectionCreateRequest = {
    supply_shed_id: supplyShedId,
    name: newSubregionName,
    commodities,
  };
  if (subsectionGeometries.length > 0) newSubsection['geometries'] = subsectionGeometries;
  if (combinedFeatures.length > 0) newSubsection['selected_features'] = combinedFeatures;

  return [newSubsection];
};

/**
 * Takes all selections and creates an individual subregion out of each one
 */
const makeSeparateSubregions = (
  subsectionGeometries: Array<FeatureById[keyof FeatureById]>,
  subsectionSelectedFeatures: Array<FeatureById[keyof FeatureById]>,
  commodities: Array<CommodityItem>,
  supplyShedId: number
) => {
  const geometries = [...subsectionGeometries].map<SubsectionCreateRequest>(feature => ({
    supply_shed_id: supplyShedId,
    name: getSubsectionGeometryName(feature),
    commodities,
    geometries: [feature],
  }));

  const selectedFeatures = [...subsectionSelectedFeatures].map<SubsectionCreateRequest>(feature => {
    const name = getSubsectionSelectedFeatureName(feature);
    const feature_id = getSubsectionFeatureId(feature);

    return {
      supply_shed_id: supplyShedId,
      name,
      commodities,
      selected_features: [
        {
          feature_id,
          collection_id: getSubsectionSelectedFeatureCollectionId(feature),
          feature_name: name,
        },
      ],
    };
  });

  return [...geometries, ...selectedFeatures];
};

export const AddSubregions = () => {
  const dispatch = useAppDispatch();
  const {mutate: mutateSWR} = useSWRConfig();
  const {programSupplySheds, isLoading: isLoadingSupplySheds} =
    useFetchSupplyShedsWithSortAndSetColor();
  const {programId, supplyShedId} = useParsedMatchParams<{
    programId: number;
    supplyShedId: number;
  }>();

  const {isRegrowOrProgramAdmin} = useIsAdmin();
  const subsectionSelectedFeatures = useAppSelector(selectSubsectionFeatures);
  const subsectionGeometries = useAppSelector(selectSubsectionGeometries);
  const interactionType = useAppSelector(selectInteractionType);
  const getColorByShedId = useAppSelector(selectGetColorBySupplyShedId);

  const [commodities, setCommodities] = useState<Array<CommodityItem>>([]);
  const [uploadDialogIsOpen, setUploadDialogIsOpen] = useState(false);
  const [creatingSubsections, setCreatingSubsections] = useState(false);
  const [combineSelections, setCombineSelections] = useState(false);
  const [newSubregionName, setNewSubregionName] = useState('');

  const supplyShed = useMemo(
    () => findSupplyShedById(programSupplySheds, supplyShedId),
    [programSupplySheds, supplyShedId]
  );

  const {isLoading: isLoadingGeometries, allGeometryFeatures} = useFetchGeometries('supply-shed');

  useEffect(() => {
    return () => {
      // clear out selected features and geometries on unmount
      dispatch(setSubsectionGeometryById({}));
      dispatch(setSubsectionSelectedFeatureById({}));
    };
  }, [dispatch]);

  useEffect(() => {
    dispatch(setShouldShowMapTools(true));
    dispatch(setInteractionType('select'));
  }, [dispatch, programId, supplyShedId]);

  useEffect(() => {
    if (interactionType === 'upload') {
      setUploadDialogIsOpen(true);
    }
  }, [dispatch, interactionType]);

  useEffect(() => {
    // Generate the ProgramGeometries to display the Supply Shed on the map
    dispatch(setProgramGeometries(allGeometryFeatures));
    dispatch(setShowProgramGeometries(true));
  }, [dispatch, allGeometryFeatures]);

  const handleCropFilterChange = (selectedCropIds: Array<number>) => {
    const selectedCommodities = selectedCropIds.map(id => ({id, volume: 0}));
    setCommodities(selectedCommodities);
  };

  const createSubsections = useCallback(async () => {
    setCreatingSubsections(true);

    const requestBody: Array<SubsectionCreateRequest> = combineSelections
      ? makeCombinedSubregion(
          subsectionGeometries,
          subsectionSelectedFeatures,
          commodities,
          supplyShedId,
          newSubregionName
        )
      : makeSeparateSubregions(
          subsectionGeometries,
          subsectionSelectedFeatures,
          commodities,
          supplyShedId
        );

    try {
      await SIApi.addSupplyShedSubsections(programId, supplyShedId, requestBody);
      await mutateSWR(siUrls.fetchSupplySheds(programId));
      await mutateSWR(siUrls.fetchSupplyShedGeometries(programId));

      // Set processing for the processing popup
      dispatch(setProcessing(true));

      // After save push to the Supply Shed Config page
      dispatch(push(applyPathParams(SI_SUPPLY_SHED_CONFIG, {programId, supplyShedId})));
    } catch (error) {
      setCreatingSubsections(false);
      showNotification({
        type: 'error',
        title: 'Error',
        message: 'There was an error creating the subsection.',
      });
    }
  }, [
    combineSelections,
    commodities,
    dispatch,
    mutateSWR,
    newSubregionName,
    programId,
    subsectionGeometries,
    subsectionSelectedFeatures,
    supplyShedId,
  ]);

  const allGeometryNamesHaveValue = useMemo(() => {
    return subsectionGeometries.every(feature => feature.properties?.name);
  }, [subsectionGeometries]);

  return !isRegrowOrProgramAdmin ? (
    <Redirect to={SI_SUPPLY_SHED_CONFIG} />
  ) : (
    <>
      <Stack gap={4} flexGrow={1} py={4}>
        <Box display="flex" alignItems="center" gap={2}>
          <Typography variant="h4">{`Add Subregions${
            !isLoadingSupplySheds && !isLoadingGeometries ? ` to ${supplyShed?.name}` : ''
          }`}</Typography>
          {(isLoadingSupplySheds || isLoadingGeometries) && (
            <CircularProgress color="primary" size={16} />
          )}
        </Box>
        <FormControlLabel
          label={
            combineSelections
              ? 'Combine selections into one subregion'
              : 'Create separate subregions'
          }
          control={
            <Switch
              checked={combineSelections}
              onChange={async (_, shouldCombineSelections) => {
                setCombineSelections(shouldCombineSelections);
              }}
            />
          }
        />
        {combineSelections && (
          <TextField
            label="Subregion name"
            required
            onChange={evt => setNewSubregionName(evt.target.value)}
          />
        )}
        <FormControl>
          <Box mb={2}>
            <FormLabel>Track subregion commodities</FormLabel>
          </Box>

          <AllCropsSelect
            onCropsChange={handleCropFilterChange}
            cropIds={commodities.map(({id}) => id)}
          />
        </FormControl>

        {isDefined(supplyShed) && <SubsectionCard color={getColorByShedId(supplyShed.id)} />}

        {isLoadingSupplySheds || isLoadingGeometries ? (
          <CenteredLoaderContainer ariaLabel="Loading Supply Sheds" />
        ) : (
          <Stack justifyContent={'flex-end'} flexGrow={1}>
            <Box display="flex" justifyContent={'space-between'} gap={3}>
              <Button
                fullWidth
                component={Link}
                variant="outlined"
                color="secondary"
                to={applyPathParams(SI_SUPPLY_SHED_CONFIG, {programId: programId})}
              >
                Cancel
              </Button>
              <LoadingButton
                fullWidth
                loading={creatingSubsections}
                disabled={
                  creatingSubsections ||
                  !commodities.length ||
                  (subsectionGeometries.length === 0 && subsectionSelectedFeatures.length === 0) ||
                  !allGeometryNamesHaveValue ||
                  (combineSelections && newSubregionName.length === 0)
                }
                onClick={createSubsections}
              >
                Save
              </LoadingButton>
            </Box>
          </Stack>
        )}
      </Stack>
      {uploadDialogIsOpen && (
        <UploadBoundariesPopup
          onHide={() => {
            dispatch(setInteractionType('select'));
            setUploadDialogIsOpen(false);
          }}
        />
      )}
    </>
  );
};
