import {useMemo} from 'react';

import {useAppSelector} from 'store/useRedux';

import {useStable} from '_common/hooks/useStable';
import {isNil} from '_common/utils/typeGuards';

import {selectMeasurement} from 'containers/login/login-selectors';
import type {
  CommonFilters,
  CropRotationCommonRotationsKPIRequest,
} from 'containers/si/api/apiTypes';
import {useFetchKPIv2} from 'containers/si/api/swr/hooks/useFetchKPIv2';
import {makeCropRotationsMetricPairs} from 'containers/si/programs/helpers/kpi.transformation.helpers';
import {makeCommonCropRotationsMetrics} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.crop_rotation_common_rotations.transformation';

/**
 *  Fetches kpi data, and transforms common rotations summary
 *  Use this instead of useFetchAndTransformKPI hook for crop_rotation_common_rotations
 */
export const useFetchAndTransformCommonRotations = ({
  commonFilters,
  topN,
  shouldFetch,
}: {
  commonFilters: CommonFilters | undefined;
  topN: number;
  shouldFetch?: boolean;
}) => {
  const unitsSystem = useAppSelector(selectMeasurement);

  const stabilizedCommonFilters = useStable(commonFilters);

  const commonRotationsRequest: CropRotationCommonRotationsKPIRequest | undefined = useMemo(
    () =>
      isNil(stabilizedCommonFilters)
        ? undefined
        : {
            kpi_type: 'crop_rotation_common_rotations',
            summarize_by: ['crop_rotations'],
            top_n: topN,
            common_filters: stabilizedCommonFilters,
          },
    [stabilizedCommonFilters, topN]
  );

  const {response, isLoading} = useFetchKPIv2({payload: commonRotationsRequest, shouldFetch});

  // Note that unlike useFetchAndTransformKPI, rotations_summary is a required response key, so it will always be returned
  const rotationsSummary = makeCropRotationsMetricPairs(
    response,
    makeCommonCropRotationsMetrics(unitsSystem)
  );

  return {
    rawResponse: response,
    isLoading,
    rotationsSummary,
  };
};
