/* eslint-disable @typescript-eslint/consistent-type-assertions */
import {combineReducers, configureStore} from '@reduxjs/toolkit';
import {waitFor} from '@testing-library/react';
import {renderHook} from '@testing-library/react-hooks';
import React from 'react';
import {Provider} from 'react-redux';

import global from '_common/modules/global/reducer';
import {MeasurementEnum} from '_common/utils/measurement-unit-options';

import {CommonFiltersMock} from 'containers/si/__mocks__/CommonFiltersMock';
import {
  KPICoverCropAnnualizedMock,
  KPICoverCropCropTypeSummaryMock,
  KPICoverCropMock,
  KPICoverCropSubsectionAnnualizedMock,
  KPICoverCropSubsectionMock,
} from 'containers/si/__mocks__/KPICoverCropMock';
import {useFetchKPIv2} from 'containers/si/api/swr/hooks/useFetchKPIv2';
import {useFetchAndTransformKPI} from 'containers/si/hooks/useFetchAndTransformKPI';
import {
  COVER_CROP_ANNUALIZED_EXPECTED_OUTPUT_IMPERIAL_UNITS,
  COVER_CROP_CROP_TYPE_EXPECTED_OUTPUT_IMPERIAL_UNITS,
  COVER_CROP_SUBREGION_ANNUALIZED_EXPECTED_OUTPUT_IMPERIAL_UNITS,
  COVER_CROP_SUBREGION_EXPECTED_OUTPUT_IMPERIAL_UNITS,
  COVER_CROP_TOP_LEVEL_EXPECTED_OUTPUT_IMPERIAL_UNITS,
} from 'containers/si/programs/helpers/__mocks__/covercropMock';
import {makeCoverCropMetrics} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.cover_crop.transformation';

const store = configureStore({
  reducer: combineReducers({global}),
});

jest.mock(`containers/si/api/swr/hooks/useFetchKPIv2`, () => ({
  useFetchKPIv2: jest.fn(),
}));

const wrapper: React.FC = ({children}) => <Provider store={store}>{children}</Provider>;

const defaultParams = {
  kpi: 'cover_crop' as const,
  summaries: [
    'annualized' as const,
    'boundary' as const,
    'boundary_annualized' as const,
    'crop_type' as const,
  ],
  commonFilters: CommonFiltersMock,
  kpiTransformer: makeCoverCropMetrics(MeasurementEnum.ImperialUnits),
};

describe('useFetchAndTransformKPI', () => {
  let mockedUseFetchKPIv2: jest.MockedFunction<typeof useFetchKPIv2>;
  beforeEach(() => {
    mockedUseFetchKPIv2 = useFetchKPIv2 as jest.MockedFunction<typeof useFetchKPIv2>;
  });

  afterAll(() => {
    jest.clearAllMocks();
  });

  test('Result should be empty if kpi is not yet fetched', () => {
    mockedUseFetchKPIv2.mockReturnValue({
      response: undefined,
      isLoading: true,
    });

    const {result} = renderHook(() => useFetchAndTransformKPI(defaultParams), {wrapper});
    expect(result.current.isLoading).toEqual(true);
    expect(result.current.rawResponse).toBeUndefined();
  });

  test('Should call useFetchKPIv2 with correct params', async () => {
    mockedUseFetchKPIv2.mockReturnValue({
      response: undefined,
      isLoading: true,
    });

    renderHook(() => useFetchAndTransformKPI({...defaultParams, shouldFetch: false}), {wrapper});
    await waitFor(() =>
      expect(useFetchKPIv2).toHaveBeenCalledWith({
        payload: {
          kpi_type: 'cover_crop',
          common_filters: CommonFiltersMock,
          summarize_by: [
            'annualized' as const,
            'boundary' as const,
            'boundary_annualized' as const,
            'crop_type' as const,
          ],
        },
        shouldFetch: false,
      })
    );
  });

  test('Should successfully return response and top level values', () => {
    mockedUseFetchKPIv2.mockReturnValue({
      response: KPICoverCropMock,
      isLoading: false,
    });

    const {result} = renderHook(() => useFetchAndTransformKPI({...defaultParams, summaries: []}), {
      wrapper,
    });
    expect(result.current.isLoading).toEqual(false);
    expect(result.current.rawResponse).toEqual(KPICoverCropMock);
    expect(result.current.topLevel).toEqual(COVER_CROP_TOP_LEVEL_EXPECTED_OUTPUT_IMPERIAL_UNITS);
  });

  test('Should return undefined for summaries not requested', () => {
    mockedUseFetchKPIv2.mockReturnValue({
      response: KPICoverCropMock,
      isLoading: false,
    });

    const {result} = renderHook(() => useFetchAndTransformKPI({...defaultParams, summaries: []}), {
      wrapper,
    });
    const summaryKeys: Array<keyof typeof result.current> = [
      'boundarySummary',
      'annualizedSummary',
      'cropTypeSummary',
      'boundaryAnnualizedSummary',
    ];
    summaryKeys.forEach(s => {
      expect(result.current[s]).toBeUndefined();
    });
  });

  test('Should return null for summaries requested, but not defined', () => {
    mockedUseFetchKPIv2.mockReturnValue({
      response: KPICoverCropMock,
      isLoading: false,
    });

    const {result} = renderHook(() => useFetchAndTransformKPI(defaultParams), {
      wrapper,
    });
    const summaryKeys: Array<keyof typeof result.current> = [
      'boundarySummary',
      'annualizedSummary',
      'cropTypeSummary',
      'boundaryAnnualizedSummary',
    ];
    summaryKeys.forEach(s => {
      expect(result.current[s]).toBeNull();
    });
  });

  test('Should successfully return boundary summary values', () => {
    mockedUseFetchKPIv2.mockReturnValue({
      response: KPICoverCropSubsectionMock,
      isLoading: false,
    });

    const {result} = renderHook(
      () => useFetchAndTransformKPI({...defaultParams, summaries: ['boundary']}),
      {wrapper}
    );
    const summary = result.current.boundarySummary;
    expect(summary).toBeDefined();
    expect(summary?.length).toEqual(
      Object.keys(KPICoverCropSubsectionMock.boundary_summary).length
    );
    expect(summary?.[0]).toEqual(['872', COVER_CROP_SUBREGION_EXPECTED_OUTPUT_IMPERIAL_UNITS]);
  });

  test('Should successfully return annualized summary values', () => {
    mockedUseFetchKPIv2.mockReturnValue({
      response: KPICoverCropAnnualizedMock,
      isLoading: false,
    });

    const {result} = renderHook(
      () => useFetchAndTransformKPI({...defaultParams, summaries: ['annualized']}),
      {wrapper}
    );
    const summary = result.current.annualizedSummary;
    expect(summary).toBeDefined();
    expect(summary?.length).toEqual(
      Object.keys(KPICoverCropAnnualizedMock.annualized_summary).length
    );
    expect(summary?.[0]).toEqual(['2015', COVER_CROP_ANNUALIZED_EXPECTED_OUTPUT_IMPERIAL_UNITS]);
  });

  test('Should successfully return crop type summary values', () => {
    mockedUseFetchKPIv2.mockReturnValue({
      response: KPICoverCropCropTypeSummaryMock,
      isLoading: false,
    });

    const {result} = renderHook(
      () => useFetchAndTransformKPI({...defaultParams, summaries: ['crop_type']}),
      {wrapper}
    );
    const summary = result.current.cropTypeSummary;
    expect(summary).toBeDefined();
    expect(summary?.length).toEqual(
      Object.keys(KPICoverCropCropTypeSummaryMock.crop_type_summary).length
    );
    expect(summary?.[0]).toEqual(['1', COVER_CROP_CROP_TYPE_EXPECTED_OUTPUT_IMPERIAL_UNITS]);
  });

  test('Should successfully return boundary annualized summary values', () => {
    mockedUseFetchKPIv2.mockReturnValue({
      response: KPICoverCropSubsectionAnnualizedMock,
      isLoading: false,
    });

    const {result} = renderHook(
      () => useFetchAndTransformKPI({...defaultParams, summaries: ['boundary_annualized']}),
      {wrapper}
    );
    const summary = result.current.boundaryAnnualizedSummary;
    expect(summary).toBeDefined();
    expect(summary?.length).toEqual(
      Object.keys(KPICoverCropSubsectionAnnualizedMock.boundary_annualized_summary).length
    );

    const firstSubregionPair = summary?.[0];
    const firstSubregionFirstAnnualizedSummaryPair = firstSubregionPair?.[1]?.[0];

    expect(firstSubregionPair?.[0]).toEqual('872');
    expect(firstSubregionPair?.[1]).toHaveLength(
      Object.keys(KPICoverCropSubsectionAnnualizedMock.boundary_annualized_summary['872']!).length
    );
    expect(firstSubregionFirstAnnualizedSummaryPair?.[0]).toEqual('2015');
    expect(firstSubregionFirstAnnualizedSummaryPair?.[1]).toEqual(
      COVER_CROP_SUBREGION_ANNUALIZED_EXPECTED_OUTPUT_IMPERIAL_UNITS
    );
  });
});
