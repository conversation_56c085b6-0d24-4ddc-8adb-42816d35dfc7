import React, {useMemo} from 'react';

import type {AutocompleteOptionType} from '@regrow-internal/design-system';
import {Autocomplete, Box, SvgIcon, Tooltip} from '@regrow-internal/design-system';

import {isDefined, isEmptyArray, isFalsy, isNil, isNonEmptyArray} from '_common/utils/typeGuards';

import type {SupplyShedResponse} from 'containers/si/api/apiTypes';
import {useFetchSupplySheds} from 'containers/si/api/swr/hooks/useFetchSupplySheds';
import {UnavailableOptionLabel} from 'containers/si/components/filters/UnavailableOptionLabel';
import {useGetDataScenarioById} from 'containers/si/hooks/useGetDataScenarioById';
import {findSupplyShedById, getSubsections} from 'containers/si/module/helpers/supply-shed-helpers';
import type {FlattenedScenario} from 'containers/si/programs/configure/data-scenarios/types';
import type {KPIDataFiltersState} from 'containers/si/types';

interface RegionSelectProps {
  regionIds: KPIDataFiltersState['subsectionIds'];
  dataScenarioId?: KPIDataFiltersState['dataScenarioId'];
  onRegionsChange: (regions: KPIDataFiltersState['subsectionIds']) => void;
  label?: string;
}

export const RegionsSelect: React.FC<RegionSelectProps> = ({
  regionIds,
  dataScenarioId,
  onRegionsChange,
  label,
}) => {
  const {data: programSupplySheds} = useFetchSupplySheds();
  const {getDataScenarioById} = useGetDataScenarioById();
  const dataScenario = getDataScenarioById(dataScenarioId);

  const options: Array<AutocompleteOptionType> = useMemo(() => {
    if (isNil(programSupplySheds)) return [];
    return makeRegionOptions(programSupplySheds, dataScenario);
  }, [dataScenario, programSupplySheds]);

  const selected = useMemo(
    () => options.filter(option => regionIds.includes(Number(option.value))),
    [options, regionIds]
  );
  const validSelectedRegions = selected.filter(({disabled}) => isFalsy(disabled));
  const multiSelectTagsText = `${
    validSelectedRegions.length === options.length ? 'All' : validSelectedRegions.length
  } subregions selected`;
  const hasNoValidRegionsSelected = isNonEmptyArray(selected) && isEmptyArray(validSelectedRegions);

  const TooltipComponent = useMemo(() => {
    if (hasNoValidRegionsSelected) {
      return (
        <RegionSelectTooltip
          type="error"
          message={`${selected
            .map(({label: optionLabel}) => optionLabel)
            .join(', ')} reporting is not available with the selected data scenario`}
        />
      );
    }

    return null;
  }, [hasNoValidRegionsSelected, selected]);

  return (
    <Box display="flex" alignItems="center" gap={1}>
      <Autocomplete
        aria-label="Select subregions"
        error={hasNoValidRegionsSelected}
        fullWidth
        disableCloseOnSelect
        hasSelectAll
        hasClear
        multiple
        placeholder="Select subregions"
        localeText={{
          getLimitTagsText: () => `${multiSelectTagsText}`,
          getLimitTagsTextFocused: () => `${multiSelectTagsText}`,
          selectAll: 'Select all',
          deselectAll: 'Deselect all',
        }}
        options={options}
        onChange={(_, option) => onRegionsChange(option.map(({value}) => Number(value)))}
        value={selected}
        label={label}
      />
      {TooltipComponent}
    </Box>
  );
};

const makeRegionOptions = (
  supplySheds: Array<SupplyShedResponse>,
  scenario?: FlattenedScenario
): Array<AutocompleteOptionType> => {
  const regions = getSubsections(supplySheds);

  return regions.map(region => {
    const regionIsNotInScenario = isDefined(scenario) && !scenario.regions.includes(region.id);
    const isInactiveRegion = region.status !== 'ACTIVE';
    const disabled = regionIsNotInScenario || isInactiveRegion;

    return {
      label: region.name,
      value: region.id,
      group: findSupplyShedById(supplySheds, region.supply_shed_id)?.name,
      disabled: disabled,
      DisplayLabel: disabled ? (
        <UnavailableOptionLabel
          label={region.name}
          message={
            regionIsNotInScenario
              ? `${region.name} reporting is not available with the selected data scenario`
              : `${region.name} is not available due to its processing status: ${region.status}`
          }
        />
      ) : undefined,
    };
  });
};

const RegionSelectTooltip = ({type, message}: {type: 'error' | 'info'; message: string}) => (
  <Tooltip title={message} placement="right">
    <Box>
      <SvgIcon
        color={type === 'error' ? 'error' : undefined}
        fontSize="body1"
        type={type === 'error' ? 'warning-triangled' : 'info-circled'}
      />
    </Box>
  </Tooltip>
);
