import React from 'react';

import {FormControl, FormLabel, SelectField, Stack} from '@regrow-internal/design-system';

import {isDefined, isEmptyString} from '_common/utils/typeGuards';

import {useProgramYears} from 'containers/si/hooks/useProgramYears';
import type {KPIDataFiltersState} from 'containers/si/types';

interface YearsSelectProps {
  year: KPIDataFiltersState['year'];
  onYearChange: (year: KPIDataFiltersState['year']) => void;
  label?: string;
}

export const YearsSelect = ({year, onYearChange, label}: YearsSelectProps) => {
  const {programYears} = useProgramYears();

  return (
    <FormControl>
      <Stack gap={2}>
        {isDefined(label) && <FormLabel>{label}</FormLabel>}
        <SelectField<KPIDataFiltersState['year'] | ''>
          aria-label="Select year"
          placeholder="Select year"
          required
          options={programYears.map(option => ({label: `${option}`, value: option}))}
          onChange={e => onYearChange(isEmptyString(e.target.value) ? undefined : e.target.value)}
          value={year ?? ''}
        />
      </Stack>
    </FormControl>
  );
};
