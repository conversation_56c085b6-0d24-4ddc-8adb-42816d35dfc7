import React from 'react';

import {Stack, SvgIcon, Tooltip, TypographyOverflow} from '@regrow-internal/design-system';

interface UnavailableOptionLabelProps {
  label: string;
  message: string;
}

export const UnavailableOptionLabel: React.FC<UnavailableOptionLabelProps> = ({label, message}) => (
  <Stack
    maxWidth={1}
    direction="row"
    gap={2}
    alignItems="center"
    onClick={e => e.stopPropagation()} // prevents disabled menu item clicks from triggering menu selection
    overflow="hidden"
  >
    <TypographyOverflow>{label}</TypographyOverflow>
    <Tooltip title={message} disableInteractive placement="right">
      <span style={{pointerEvents: 'auto'}}>
        <SvgIcon fontSize="body1" type="warning-triangled" />
      </span>
    </Tooltip>
  </Stack>
);
