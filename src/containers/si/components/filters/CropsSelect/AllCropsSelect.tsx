import React, {useMemo} from 'react';

import {CropsAutocomplete} from 'containers/si/components/filters/CropsSelect/components/CropsAutocomplete';
import {useGetCropLabelById} from 'containers/si/hooks/useGetCropLabelById';

export const AllCropsSelect = ({
  cropIds,
  onCropsChange,
}: {
  cropIds: Array<number>;
  onCropsChange: (cropIds: Array<number>) => void;
}) => {
  const {SISupportedCropIds, getSISupportedCropLabelById, isLoading} = useGetCropLabelById();

  const options = useMemo(() => {
    if (isLoading) return [];

    return SISupportedCropIds.map(cropId => ({
      label: String(getSISupportedCropLabelById(cropId)),
      value: cropId,
    })).toSorted(({label: labelA}, {label: labelB}) => labelA.localeCompare(labelB));
  }, [SISupportedCropIds, getSISupportedCropLabelById, isLoading]);

  const selected = useMemo(
    () => options.filter(option => cropIds.includes(Number(option.value))),
    [cropIds, options]
  );

  return <CropsAutocomplete options={options} selected={selected} onCropsChange={onCropsChange} />;
};
