import React, {useMemo} from 'react';

import type {AutocompleteOptionType} from '@regrow-internal/design-system';

import {capitalizeFirstLetter} from '_common/utils/string';
import {isDefined} from '_common/utils/typeGuards';

import {CropsAutocomplete} from 'containers/si/components/filters/CropsSelect/components/CropsAutocomplete';
import {UnavailableOptionLabel} from 'containers/si/components/filters/UnavailableOptionLabel';
import {useGetCropLabelById} from 'containers/si/hooks/useGetCropLabelById';
import {useGetDataScenarioById} from 'containers/si/hooks/useGetDataScenarioById';
import {useProgramCrops} from 'containers/si/hooks/useProgramCrops';
import type {KPIDataFiltersState} from 'containers/si/types';
import {formatCropRotationsToUniqueSupportedCropIds} from 'containers/si/utils/scenarios.format';

export interface ProgramCropsSelectProps {
  cropIds: KPIDataFiltersState['cropIds'];
  regionIds: KPIDataFiltersState['subsectionIds'];
  dataScenarioId?: KPIDataFiltersState['dataScenarioId'];
  onCropsChange: (crops: KPIDataFiltersState['cropIds']) => void;
  label?: string;
  disableMultiple?: boolean;
  disableMultipleMessage?: string;
}

export const ProgramCropsSelect: React.FC<ProgramCropsSelectProps> = ({
  cropIds,
  regionIds,
  dataScenarioId,
  onCropsChange,
  label,
  disableMultiple,
  disableMultipleMessage,
}) => {
  const {getDataScenarioById} = useGetDataScenarioById();
  const {getSISupportedCropLabelById} = useGetCropLabelById();
  const {cropIds: programCropIds} = useProgramCrops();
  const {cropIds: cropIdsForSubregionsSelected} = useProgramCrops(regionIds);

  const dataScenario = getDataScenarioById(dataScenarioId);

  const options: Array<AutocompleteOptionType> = useMemo(() => {
    if (!isDefined(programCropIds)) return [];

    const availableCropsIds = isDefined(dataScenario)
      ? (
          dataScenario.crops ??
          formatCropRotationsToUniqueSupportedCropIds(dataScenario.cropRotations) ??
          []
        ).filter(cropId => cropIdsForSubregionsSelected.includes(cropId))
      : cropIdsForSubregionsSelected;

    const options_ = programCropIds // this is filtered to si supported program crops only
      .map(cropId => {
        const cropLabel = getSISupportedCropLabelById(cropId);
        const disabled = !(availableCropsIds ?? []).includes(cropId);

        return {
          label: String(cropLabel),
          value: cropId,
          disabled,
          DisplayLabel: disabled ? (
            <UnavailableOptionLabel
              label={String(cropLabel)}
              message={`${capitalizeFirstLetter(
                String(cropLabel)
              )} crop reporting is not available with the selected data scenario and/or subregion`}
            />
          ) : undefined,
        };
      })
      .toSorted(({label: labelA}, {label: labelB}) => labelA.localeCompare(labelB));

    return options_;
  }, [cropIdsForSubregionsSelected, dataScenario, getSISupportedCropLabelById, programCropIds]);

  const selected = options.filter(option => cropIds.includes(Number(option.value)));

  return (
    <CropsAutocomplete
      options={options}
      onCropsChange={onCropsChange}
      selected={selected}
      label={label}
      disableMultiple={disableMultiple}
      disableMultipleMessage={disableMultipleMessage}
    />
  );
};
