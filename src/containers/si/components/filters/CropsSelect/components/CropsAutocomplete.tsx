import React, {useMemo} from 'react';

import type {AutocompleteOptionType} from '@regrow-internal/design-system';
import {
  Autocomplete,
  Box,
  SvgIcon,
  Tooltip,
  type AutocompleteProps,
} from '@regrow-internal/design-system';

import {isDefined, isEmptyArray, isFalsy, isNonEmptyArray} from '_common/utils/typeGuards';

const PLACEHOLDER_MULTIPLE = 'Select commodities';
const PLACEHOLDER_MIN_WIDTH = 240;
const LISTBOX_MIN_WIDTH = 180;

type CropsAutocompleteProps<M extends boolean> = Omit<
  AutocompleteProps<AutocompleteOptionType, M>,
  'onChange' | 'value' | 'hasClear' | 'hasSelectAll' | 'multiple' | 'localeText' | 'color'
> & {
  selected: Array<AutocompleteOptionType>;
  onCropsChange: (crops: Array<number>) => void;
  disableMultiple?: boolean;
  disableMultipleMessage?: string;
};

export const CropsAutocomplete = <M extends boolean>({
  selected,
  onCropsChange,
  options,
  disableMultiple,
  disableMultipleMessage,
  ...autocompleteProps
}: CropsAutocompleteProps<M>) => {
  const validSelectedCrops = selected.filter(({disabled}) => isFalsy(disabled));
  const multiSelectTagsText = `${
    validSelectedCrops.length === options.length ? 'All' : validSelectedCrops.length
  } commodities selected`;
  const hasNoValidCropsSelected = isNonEmptyArray(selected) && isEmptyArray(validSelectedCrops);

  const commonProps = useMemo(
    () => ({
      'aria-label': PLACEHOLDER_MULTIPLE,
      error: hasNoValidCropsSelected,
      hasClear: true,
      placeholder: PLACEHOLDER_MULTIPLE,
      slotProps: {
        popper: {
          sx: {
            minWidth: 'max-content',
          },
        },
      },
      ListboxProps: {
        sx: {
          minWidth: isEmptyArray(selected)
            ? `${PLACEHOLDER_MIN_WIDTH}px`
            : `${LISTBOX_MIN_WIDTH}px`,
        },
      },
      options,
      ...autocompleteProps,
    }),
    [autocompleteProps, hasNoValidCropsSelected, options, selected]
  );

  const TooltipComponent = useMemo(() => {
    if (hasNoValidCropsSelected) {
      return (
        <CropSelectTooltip
          type="error"
          message={`${selected
            .map(({label}) => label)
            .join(
              ', '
            )} reporting is not available with the selected data scenario and/or subregion`}
        />
      );
    }

    if (disableMultiple && isDefined(disableMultipleMessage)) {
      return <CropSelectTooltip type="info" message={disableMultipleMessage} />;
    }

    return null;
  }, [disableMultiple, disableMultipleMessage, hasNoValidCropsSelected, selected]);

  const AutocompleteComponent = disableMultiple ? (
    <Autocomplete<AutocompleteOptionType, false>
      {...commonProps}
      hasClear={false}
      hasSelectAll={undefined}
      multiple={false}
      localeText={undefined}
      onChange={(_: React.SyntheticEvent<Element, Event>, {value}: AutocompleteOptionType) =>
        onCropsChange([Number(value)])
      }
      value={selected[0] ?? ''}
    />
  ) : (
    <Autocomplete<AutocompleteOptionType, true>
      {...commonProps}
      disableCloseOnSelect={true}
      hasSelectAll={true}
      multiple={true}
      localeText={{
        getLimitTagsText: () => multiSelectTagsText,
        getLimitTagsTextFocused: () => multiSelectTagsText,
        selectAll: 'Select all',
        deselectAll: 'Deselect all',
      }}
      onChange={(_: React.SyntheticEvent<Element, Event>, option: Array<AutocompleteOptionType>) =>
        onCropsChange(option.map(({value}) => Number(value)))
      }
      value={selected}
    />
  );

  return (
    <Box display="flex" alignItems="center" gap={1}>
      <Box minWidth={isEmptyArray(selected) ? PLACEHOLDER_MIN_WIDTH : LISTBOX_MIN_WIDTH}>
        {AutocompleteComponent}
      </Box>
      {TooltipComponent}
    </Box>
  );
};

const CropSelectTooltip = ({type, message}: {type: 'error' | 'info'; message: string}) => (
  <Tooltip title={message} placement="right">
    <Box>
      <SvgIcon
        color={type === 'error' ? 'error' : undefined}
        fontSize="body1"
        type={type === 'error' ? 'warning-triangled' : 'info-circled'}
      />
    </Box>
  </Tooltip>
);
