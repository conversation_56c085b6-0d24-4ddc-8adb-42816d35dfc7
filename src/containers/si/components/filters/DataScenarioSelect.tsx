import fromPairs from 'lodash/fromPairs';
import React, {useCallback} from 'react';

import {
  Box,
  FormControl,
  FormLabel,
  MenuItem,
  Select,
  Stack,
  SvgIcon,
  Tooltip,
  Typography,
  type SelectChangeEvent,
} from '@regrow-internal/design-system';

import {useAppSelector} from 'store/useRedux';

import {isDefined, isEmptyString} from '_common/utils/typeGuards';

import {selectMeasurement} from 'containers/login/login-selectors';
import {UnavailableOptionLabel} from 'containers/si/components/filters/UnavailableOptionLabel';
import {useGetCropLabelById} from 'containers/si/hooks/useGetCropLabelById';
import {useProgramCrops} from 'containers/si/hooks/useProgramCrops';
import {useProgramDataScenarios} from 'containers/si/hooks/useProgramDataScenarios';
import {useGetSourcingRegionsText} from 'containers/si/hooks/useSourcingRegionsText';
import {flattenDataScenario} from 'containers/si/programs/configure/data-scenarios/components/DataScenariosTable/scenarios.helpers';
import {TooltipContent} from 'containers/si/programs/report/comparison-dashboard/components/KPITooltip/TooltipContent';
import type {RowsDefinition} from 'containers/si/programs/report/comparison-dashboard/components/KPITooltip/types';
import type {KPIDataFiltersState} from 'containers/si/types';
import {formatScenarioTooltipRows} from 'containers/si/utils/scenarios.format';

export interface DataScenarioSelectProps {
  dataScenarioId: KPIDataFiltersState['dataScenarioId'];
  onDataScenarioChange: (dataScenarioId: KPIDataFiltersState['dataScenarioId']) => void;
  disabled?: boolean;
  disabledMessage?: string;
  label?: string;
}

const EMPTY_ROWS: RowsDefinition = [];

const DEFAULT_LABEL = 'Default data scenario';

export const DataScenarioSelect: React.FC<DataScenarioSelectProps> = ({
  dataScenarioId,
  onDataScenarioChange,
  disabled = false,
  disabledMessage,
  label,
}) => {
  const {dataScenarios} = useProgramDataScenarios();
  const {cropIds} = useProgramCrops();
  const {getSourcingRegionsText} = useGetSourcingRegionsText();
  const {getSISupportedCropLabelById} = useGetCropLabelById();
  const userUnitsSystem = useAppSelector(selectMeasurement);

  const onChange = useCallback(
    (e: SelectChangeEvent<KPIDataFiltersState['dataScenarioId']>) =>
      onDataScenarioChange(isEmptyString(e.target.value) ? undefined : e.target.value),
    [onDataScenarioChange]
  );

  const renderValue = useCallback(
    (value: KPIDataFiltersState['dataScenarioId']) => {
      const possibleScenarioName = dataScenarios.find(scenario => scenario.id === value)?.name;
      const scenarioName = isDefined(possibleScenarioName) ? possibleScenarioName : DEFAULT_LABEL;

      if (disabled && isDefined(disabledMessage))
        return <UnavailableOptionLabel label={scenarioName} message={disabledMessage} />;

      return scenarioName;
    },
    [dataScenarios, disabled, disabledMessage]
  );

  const tooltipRowsLookup = React.useMemo(
    () =>
      fromPairs(
        dataScenarios.map(scenario => [
          scenario.id,
          formatScenarioTooltipRows(
            flattenDataScenario(scenario, userUnitsSystem),
            cropIds,
            getSourcingRegionsText,
            getSISupportedCropLabelById
          ),
        ])
      ),
    [dataScenarios, cropIds, getSourcingRegionsText, getSISupportedCropLabelById, userUnitsSystem]
  );

  return (
    <FormControl>
      <Stack gap={2}>
        {isDefined(label) && <FormLabel>{label}</FormLabel>}
        <Select<KPIDataFiltersState['dataScenarioId']>
          aria-label="Select data scenario"
          placeholder="Default data scenario"
          onChange={onChange}
          disabled={disabled}
          displayEmpty
          renderValue={renderValue}
          value={dataScenarioId ?? ''}
        >
          <MenuItem value={''}>
            <Typography>{DEFAULT_LABEL}</Typography>
          </MenuItem>
          {dataScenarios.map(scenario => (
            <MenuItem key={scenario.id} value={scenario.id}>
              <Stack
                direction="row"
                justifyContent="space-between"
                alignContent="center"
                gap={1}
                width="100%"
              >
                <Box>{scenario.name}</Box>
                <Tooltip
                  placement="right"
                  title={<TooltipContent rows={tooltipRowsLookup[scenario.id] ?? EMPTY_ROWS} />}
                >
                  <span data-testid="info-icon">
                    <SvgIcon type="info-circled" fontSize="h5" />
                  </span>
                </Tooltip>
              </Stack>
            </MenuItem>
          ))}
        </Select>
      </Stack>
    </FormControl>
  );
};
