import {fireEvent, screen, waitFor, within} from '@testing-library/react';
import uniq from 'lodash/uniq';
import React from 'react';

import {renderWithProviders} from '_common/test_utils/renderWithProviders';
import {getTypedValues} from '_common/utils/object';

import {PROGRAM_CROP_ID_TO_NAME_LOOKUP_MOCK} from 'containers/si/__mocks__/ProgramCropsMock';
import {DATA_SCENARIOS_MOCK} from 'containers/si/__mocks__/ProgramDataScenariosMock';
import {PROGRAM_SUPPLYSHEDS_MOCK} from 'containers/si/__mocks__/ProgramSupplyShedsMock';
import {
  ProgramCropsSelect,
  type ProgramCropsSelectProps,
} from 'containers/si/components/filters/CropsSelect/ProgramCropsSelect';

jest.mock('containers/si/hooks/useProgramDataScenarios');

// This is a nested dependency of useProgramCrops
jest.mock('containers/si/api/swr/hooks/useFetchSupplySheds');

// TODO: SI-3050 - update when fetching crops from SI-service is implemented
// This is a nested dependency within useGetCropLabelById
jest.mock('_common/hooks/useLoadCrops');

describe('ProgramCropsSelect', () => {
  describe('common', () => {
    const defaultProps: ProgramCropsSelectProps = {
      cropIds: [1, 5, 23, 24, 41, 28], // all crops in useFetchSupplySheds mock
      regionIds: [872, 873, 874, 875, 8779], // all subregions in useFetchSupplySheds mock
      dataScenarioId: undefined,
      onCropsChange: () => {},
    };

    it('Should render placeholder when nothing is selected', () => {
      renderWithProviders(<ProgramCropsSelect {...defaultProps} cropIds={[]} />);
      expect(screen.getByPlaceholderText('Select commodities')).toBeInTheDocument();
    });

    it('Should render all program crops as options', async () => {
      // TODO: SI-3050 Use si-service crop list id to label lookup mock
      const programCropLabels = getTypedValues(PROGRAM_CROP_ID_TO_NAME_LOOKUP_MOCK);
      renderWithProviders(<ProgramCropsSelect {...defaultProps} />);

      fireEvent.click(screen.getByRole('button', {name: 'Open'}));
      await waitFor(() => {
        const options = screen
          .getAllByRole('option')
          .filter(o => !['Select all', 'Deselect all'].includes(o.textContent ?? ''));

        const optionLabels = options.map(o => o.textContent ?? '');
        const areAllOptionLabelsInProgramCropLabels = optionLabels.every(value =>
          programCropLabels.includes(value)
        );
        expect(areAllOptionLabelsInProgramCropLabels).toBe(true);
        const areAllProgramCropLabelsInOptionLabels = programCropLabels.every(value =>
          optionLabels.includes(value)
        );
        expect(areAllProgramCropLabelsInOptionLabels).toBe(true);
      });
    });

    it('Should render unavailable crops due to selected data scenario as disabled', async () => {
      const selectDataScenarioId = 163;
      const availableCropIds = DATA_SCENARIOS_MOCK.flattenedScenarios.find(
        ({id}) => id === selectDataScenarioId
      )!.crops!;
      // TODO: SI-3050 Use si-service crop list id to label lookup mock
      const availableCropNames = availableCropIds!.map(
        id => PROGRAM_CROP_ID_TO_NAME_LOOKUP_MOCK[id]!
      );

      renderWithProviders(
        <ProgramCropsSelect {...defaultProps} dataScenarioId={selectDataScenarioId} />
      );

      fireEvent.click(screen.getByRole('button', {name: 'Open'}));
      await waitFor(() => {
        const options = screen
          .getAllByRole('option')
          .filter(o => !['Select all', 'Deselect all'].includes(o.textContent ?? ''));

        options.forEach(option => {
          const optionLabel = option.textContent!;
          if (availableCropNames.includes(optionLabel)) {
            expect(option).toHaveAttribute('aria-disabled', 'false');
          } else {
            expect(option).toHaveAttribute('aria-disabled', 'true');
          }
        });
      });
    });

    it('Should render unavailable crops due to selected subregions as disabled', async () => {
      const selectedSubregionIds = [872];
      const availableCropIds = uniq(
        PROGRAM_SUPPLYSHEDS_MOCK.flatMap(supplyShed =>
          supplyShed.subsections
            .filter(subregion => selectedSubregionIds.includes(subregion.id))
            .flatMap(subsection => subsection.commodities.map(commodity => commodity.id))
        )
      );
      // TODO: SI-3050 Use si-service crop list id to label lookup mock
      const availableCropNames = availableCropIds!.map(
        id => PROGRAM_CROP_ID_TO_NAME_LOOKUP_MOCK[id]!
      );

      renderWithProviders(
        <ProgramCropsSelect {...defaultProps} regionIds={selectedSubregionIds} />
      );

      fireEvent.click(screen.getByRole('button', {name: 'Open'}));
      await waitFor(() => {
        const options = screen
          .getAllByRole('option')
          .filter(o => !['Select all', 'Deselect all'].includes(o.textContent ?? ''));

        options.forEach(option => {
          const optionLabel = option.textContent!;

          if (availableCropNames.includes(optionLabel)) {
            expect(option).toHaveAttribute('aria-disabled', 'false');
          } else {
            expect(option).toHaveAttribute('aria-disabled', 'true');
          }
        });
      });
    });
  });

  describe('multi-select', () => {
    const defaultProps: ProgramCropsSelectProps = {
      cropIds: [1, 5, 23, 24, 41, 28], // all crops in useFetchSupplySheds mock
      regionIds: [872, 873, 874, 875, 8779], // all subregions in useFetchSupplySheds mock
      dataScenarioId: undefined,
      onCropsChange: () => {},
    };

    it('Should render all selected text when all program crops are selected', () => {
      renderWithProviders(<ProgramCropsSelect {...defaultProps} />);

      expect(screen.getByText('All commodities selected')).toBeInTheDocument();
    });

    it('Should render number of crops selected if less than all program crops', () => {
      renderWithProviders(<ProgramCropsSelect {...defaultProps} cropIds={[1, 5, 23]} />);

      expect(screen.getByText('3 commodities selected')).toBeInTheDocument();
    });

    it('Should render clear all', () => {
      renderWithProviders(<ProgramCropsSelect {...defaultProps} />);

      expect(screen.getByRole('button', {name: 'Clear'})).toBeInTheDocument();
    });

    it('Should render Select All and Deselect All options', async () => {
      renderWithProviders(<ProgramCropsSelect {...defaultProps} />);

      fireEvent.click(screen.getByRole('button', {name: 'Open'}));
      await waitFor(() => {
        expect(screen.getByRole('option', {name: 'Select all'})).toBeInTheDocument();
        expect(screen.getByRole('option', {name: 'Deselect all'})).toBeInTheDocument();
      });
    });

    it('Should render crop options with checkboxes', async () => {
      renderWithProviders(<ProgramCropsSelect {...defaultProps} />);

      fireEvent.click(screen.getByRole('button', {name: 'Open'}));
      await waitFor(() => {
        const options = screen
          .getAllByRole('option')
          .filter(o => !['Select all', 'Deselect all'].includes(o.textContent ?? ''));

        options.forEach(o => {
          expect(within(o).queryByRole('checkbox')).toBeInTheDocument();
        });
      });
    });

    it('Should render selected crops as selected', async () => {
      const selectedCropIds = [1, 5, 23, 24];
      // TODO: SI-3050 Use si-service crop list id to label lookup mock
      const selectedCropNames = selectedCropIds.map(id => PROGRAM_CROP_ID_TO_NAME_LOOKUP_MOCK[id]!);

      renderWithProviders(<ProgramCropsSelect {...defaultProps} cropIds={selectedCropIds} />);

      fireEvent.click(screen.getByRole('button', {name: 'Open'}));
      await waitFor(() => {
        const options = screen
          .getAllByRole('option')
          .filter(o => !['Select all', 'Deselect all'].includes(o.textContent ?? ''));

        options.forEach(option => {
          const optionLabel = option.textContent;
          if (selectedCropNames.includes(optionLabel!)) {
            expect(option).toHaveAttribute('aria-selected', 'true');
          } else {
            expect(option).toHaveAttribute('aria-selected', 'false');
          }
        });
      });
    });
  });

  describe('single select', () => {
    const defaultProps: ProgramCropsSelectProps = {
      cropIds: [1, 5, 23, 24, 41, 28], // all crops in useFetchSupplySheds mock
      regionIds: [872, 873, 874, 875, 8779], // all subregions in useFetchSupplySheds mock
      dataScenarioId: undefined,
      onCropsChange: () => {},
      disableMultiple: true,
    };

    it('Should render selected crop', () => {
      renderWithProviders(<ProgramCropsSelect {...defaultProps} />);

      expect(screen.getByRole('combobox')).toHaveValue('Corn');
    });

    it('Should not render clear all', () => {
      renderWithProviders(<ProgramCropsSelect {...defaultProps} />);

      expect(screen.queryByRole('button', {name: 'Clear'})).not.toBeInTheDocument();
    });

    it('Should not render Select All and Deselect All options', async () => {
      renderWithProviders(<ProgramCropsSelect {...defaultProps} />);

      fireEvent.click(screen.getByRole('button', {name: 'Open'}));
      await waitFor(() => {
        expect(screen.queryByRole('option', {name: 'Select all'})).not.toBeInTheDocument();
        expect(screen.queryByRole('option', {name: 'Deselect all'})).not.toBeInTheDocument();
      });
    });

    it('Should not render crop options with checkboxes', async () => {
      renderWithProviders(<ProgramCropsSelect {...defaultProps} />);

      fireEvent.click(screen.getByRole('button', {name: 'Open'}));
      await waitFor(() => {
        const options = screen
          .getAllByRole('option')
          .filter(o => !['Select all', 'Deselect all'].includes(o.textContent ?? ''));

        options.forEach(o => {
          expect(within(o).queryByRole('checkbox')).not.toBeInTheDocument();
        });
      });
    });

    it('Should render disabledMessage if provided', () => {
      renderWithProviders(
        <ProgramCropsSelect {...defaultProps} disableMultipleMessage="Sample disabled message" />
      );

      expect(screen.getByLabelText('Sample disabled message')).toBeInTheDocument();
    });
  });
});
