import {fireEvent, screen, within} from '@testing-library/react';
import React from 'react';

import {renderWithProviders} from '_common/test_utils/renderWithProviders';

import {PROGRAM_SUPPLYSHEDS_STATUSFAILED_MOCK} from 'containers/si/__mocks__/ProgramSupplyShedsMock';
import {RegionsSelect} from 'containers/si/components/filters/RegionsSelect';

jest.mock(`containers/si/api/swr/hooks/useFetchSupplySheds.ts`);
jest.mock('containers/si/hooks/useProgramDataScenarios');

const useFetchSupplyShedsMock = jest.requireMock(
  'containers/si/api/swr/hooks/useFetchSupplySheds.ts'
).useFetchSupplySheds;

describe('RegionsSelect', () => {
  const props = {
    regionIds: [872, 8799, 873, 874, 875],
    dataScenarioId: undefined,
    onRegionsChange: jest.fn(),
  };

  describe('Input', () => {
    it('Should render all regions text if all subregions selected', () => {
      renderWithProviders(<RegionsSelect {...props} />);

      expect(screen.getByText('All subregions selected')).toBeInTheDocument();
    });

    it('Should render count of selected regions', () => {
      renderWithProviders(<RegionsSelect {...props} regionIds={[872, 873, 874]} />);

      expect(screen.getByText('3 subregions selected')).toBeInTheDocument();
    });

    it('Should render placeholder when nothing is selected', () => {
      renderWithProviders(<RegionsSelect {...props} regionIds={[]} />);

      expect(screen.getByPlaceholderText('Select subregions')).toBeInTheDocument();
    });
  });

  describe('Menu', () => {
    it('Should disable non-available region options in the data scenario', async () => {
      renderWithProviders(<RegionsSelect {...props} dataScenarioId={138} />);

      fireEvent.click(screen.getByRole('button', {name: 'Open'}));
      const listbox = await screen.findByRole('listbox');

      expect(within(listbox).getByText('Mountain West')).toHaveAttribute('aria-disabled', 'true');
    });

    it('Should disable non active region options', async () => {
      useFetchSupplyShedsMock.mockReturnValueOnce({
        error: undefined,
        data: PROGRAM_SUPPLYSHEDS_STATUSFAILED_MOCK,
        mutate: jest.fn(),
        isLoading: false,
        isValidating: false,
      });
      renderWithProviders(<RegionsSelect {...props} />);

      fireEvent.click(screen.getByRole('button', {name: 'Open'}));
      const listbox = await screen.findByRole('listbox');

      expect(within(listbox).getByText('Failed Supply Shed')).toHaveAttribute(
        'aria-disabled',
        'true'
      );
    });
  });
});
