import {fireEvent, screen, within} from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import React from 'react';

import {renderWithProviders} from '_common/test_utils/renderWithProviders';

import {DATA_SCENARIOS_MOCK} from 'containers/si/__mocks__/ProgramDataScenariosMock';
import {DataScenarioSelect} from 'containers/si/components/filters/DataScenarioSelect';

jest.mock('containers/si/hooks/useProgramDataScenarios');

const mockSelectedDataScenarioId = DATA_SCENARIOS_MOCK.dataScenarios[1]!.id;
const mockSelectedDataScenarioName = DATA_SCENARIOS_MOCK.dataScenarios[1]!.name;
const mockSelectedDataScenarioId2 = DATA_SCENARIOS_MOCK.dataScenarios[2]!.id;
const mockSelectedDataScenarioName2 = DATA_SCENARIOS_MOCK.dataScenarios[2]!.name;

describe('DataScenarioSelect', () => {
  const props = {
    dataScenarioId: undefined,
    onDataScenarioChange: jest.fn(),
    label: undefined,
  };

  it('Input: should render input and default text', () => {
    renderWithProviders(<DataScenarioSelect {...props} />);

    expect(screen.getByLabelText('Select data scenario')).toBeInTheDocument();
    expect(screen.getByText('Default data scenario')).toBeInTheDocument();
  });

  it('Input: should render the name of the selected data scenario', async () => {
    renderWithProviders(
      <DataScenarioSelect {...props} dataScenarioId={mockSelectedDataScenarioId} />
    );
    const selectedBtn = await screen.findByRole('button', {name: mockSelectedDataScenarioName});
    expect(selectedBtn).toBeInTheDocument();
  });

  it('Menu: should render the selected option', async () => {
    const {getByRole} = renderWithProviders(
      <DataScenarioSelect {...props} dataScenarioId={mockSelectedDataScenarioId} />
    );
    const selectedBtn = await screen.findByRole('button', {name: mockSelectedDataScenarioName});
    fireEvent.mouseDown(selectedBtn);

    const listbox = within(getByRole('listbox'));
    const selectedOption = listbox.getByRole('option', {name: mockSelectedDataScenarioName});
    expect(selectedOption).toHaveAttribute('aria-selected', 'true');
  });

  /**
   * TODO: update to assert correct tooltip data is rendered when
   * unit tests are added to row lookup helper functions (formatScenarioTooltipRows).
   */
  it('Menu: should render a tooltip on option icon hover', async () => {
    const user = userEvent.setup();
    const {getByRole} = renderWithProviders(
      <DataScenarioSelect {...props} dataScenarioId={mockSelectedDataScenarioId2} />
    );
    const selectedBtn = await screen.findByRole('button', {name: mockSelectedDataScenarioName2});
    expect(selectedBtn).toBeInTheDocument();

    fireEvent.mouseDown(selectedBtn);
    const listbox = within(getByRole('listbox'));

    const selectedOption = listbox.getByRole('option', {name: mockSelectedDataScenarioName2});
    const icon = within(selectedOption).getByTestId('info-icon');
    expect(icon).toBeInTheDocument();

    await user.hover(icon);

    const tooltip = await screen.findByRole('tooltip');
    expect(tooltip).toBeInTheDocument();
    const tooltipContent = await within(tooltip).findByText('Sourcing regions');
    expect(tooltipContent).toBeInTheDocument();
  });
});
