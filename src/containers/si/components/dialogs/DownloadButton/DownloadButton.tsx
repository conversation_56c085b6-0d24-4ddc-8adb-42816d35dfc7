import React, {useCallback, useState} from 'react';

import type {LoadingButtonProps} from '@regrow-internal/design-system';
import {LoadingButton} from '@regrow-internal/design-system';

import {useAppDispatch, useAppSelector} from 'store/useRedux';

import {showNotification} from '_common/components/NotificationSnackbar';
import {reportError} from '_common/utils/reportError';

import {DownloadAgreementDialog} from 'containers/si/components/dialogs/DownloadButton/DownloadAgreementDialog';
import {setDataExportAgreement} from 'containers/si/module/reducer';
import {selectDataExportAgreement} from 'containers/si/module/selectors';

interface DownloadButtonProps extends LoadingButtonProps {
  onDownload: () => Promise<void>;
}

export const DownloadButton: React.FC<DownloadButtonProps> = ({
  children,
  onDownload,
  ...buttonProps
}) => {
  const dispatch = useAppDispatch();

  const hasAcceptedExportAgreement = useAppSelector(selectDataExportAgreement);

  const [isDownloading, setIsDownloading] = useState(false);
  const [showDataAgreementDialog, setShowDataAgreementDialog] = useState(false);

  const startDownload = useCallback(async () => {
    setIsDownloading(true);
    try {
      await onDownload();
    } catch (error) {
      reportError(`Error in SI download ${JSON.stringify(error)}`);
      showNotification({
        type: 'error',
        title: 'Download error',
        message: 'There was a problem downloading this file.',
      });
    }
    setIsDownloading(false);
  }, [onDownload]);

  const onClickDownload = useCallback(() => {
    if (hasAcceptedExportAgreement) {
      void startDownload();
    } else {
      setShowDataAgreementDialog(true);
    }
  }, [hasAcceptedExportAgreement, startDownload]);

  const onConfirm = useCallback(() => {
    dispatch(setDataExportAgreement(true));
    setShowDataAgreementDialog(false);
    void startDownload();
  }, [dispatch, startDownload]);

  return (
    <>
      <LoadingButton
        variant={'contained'}
        color={'primary'}
        loading={isDownloading}
        onClick={onClickDownload}
        {...buttonProps}
      >
        {children}
      </LoadingButton>
      <DownloadAgreementDialog
        isOpen={showDataAgreementDialog}
        onClose={() => setShowDataAgreementDialog(false)}
        onConfirm={onConfirm}
      />
    </>
  );
};
