import React from 'react';

import {Typography} from '@regrow-internal/design-system';

import {ConfirmDialog} from 'containers/si/components/dialogs/ConfirmDialog';

type DownloadAgreementDialogProps = {
  isOpen: boolean;
  onConfirm: VoidFunction;
  onClose: VoidFunction;
};

export const DownloadAgreementDialog = (props: DownloadAgreementDialogProps) => {
  return (
    <ConfirmDialog
      visible={props.isOpen}
      title="Data Agreement"
      text={
        <Typography variant="body1">
          Customer shall not, directly, indirectly or through its Authorized Users, employees and/or
          the services of independent contractors: (a) attempt to sell, transfer, assign, rent,
          lend, lease, sublicense or otherwise provide third parties rights to the data; (b)
          "frame," "mirror," copy or otherwise enable third parties to use the data (or any
          component thereof); (c) allow access to the data by multiple individuals impersonating a
          single end user; (d) use the data in a manner that interferes with, degrades, or disrupts
          the integrity or performance of any Regrow technologies, services, data, systems or other
          offerings, including data transmission, storage and backup; (e) use the data for the
          purpose of developing a product or service that competes with the Regrow online products
          and services; (f) circumvent or disable any security features or functionality associated
          with the data; or (g) use the data in any manner prohibited by law. All rights not
          expressly granted to Customer are reserved by Regrow, its suppliers and licensor.
        </Typography>
      }
      onHide={props.onClose}
      onConfirm={() => {
        props.onClose();
        props.onConfirm();
      }}
      saveText="I accept these conditions"
      width="md"
    />
  );
};
