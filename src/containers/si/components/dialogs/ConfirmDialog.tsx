import React from 'react';

import {
  Button,
  DialogActions,
  DialogContent,
  SimpleDialog,
  type SimpleDialogProps,
} from '@regrow-internal/design-system';

type ConfirmDialogProps = {
  title: string;
  text: string | React.ReactNode;
  onHide: () => void;
  onConfirm: () => void;
  saveText?: string;
  noCancel?: boolean;
  disabled?: boolean;
  width?: SimpleDialogProps['maxWidth'];
  visible?: boolean;
};

export const ConfirmDialog = ({
  title,
  text,
  onHide,
  onConfirm,
  saveText = 'Save',
  noCancel = false,
  disabled = false,
  width = 'sm',
  visible = true,
}: ConfirmDialogProps) => {
  return (
    <SimpleDialog
      id={'si-dialog-popup'}
      title={title}
      onClose={onHide}
      open={visible}
      maxWidth={width}
    >
      <DialogContent>{text}</DialogContent>

      <DialogActions>
        {noCancel ? (
          <></>
        ) : (
          <Button variant="outlined" color="secondary" onClick={onHide}>
            Cancel
          </Button>
        )}
        <Button onClick={onConfirm} disabled={disabled}>
          {saveText}
        </Button>
      </DialogActions>
    </SimpleDialog>
  );
};
