import React, {useState} from 'react';

import {
  Box,
  Button,
  DialogActions,
  DialogContent,
  SimpleDialog,
  TextField,
  Typography,
} from '@regrow-internal/design-system';

import {isDefined} from '_common/utils/typeGuards';

// Returns number or string, someday fix so it only returns the type of value passed in
type SIInputProps<T extends string | number> = {
  title: string;
  subtitle?: string;
  type: 'text' | 'number';
  value: T;
  onSave: (value: T) => void;
  onHide: () => void;
  saveText?: string;
  noCancel?: boolean;
  disabled?: boolean;
  portal?: boolean;
  maxLength?: number; // used only for text, someday fix for only number props
};

export const SIInputPopup = <T extends string | number>({
  title,
  subtitle,
  value,
  onSave,
  onHide,
  type = 'text',
  saveText = 'Save',
  noCancel = false,
  disabled = false,
  maxLength = 50,
}: SIInputProps<T>) => {
  const [newValue, setNewValue] = useState<T>(value);

  return (
    <SimpleDialog id={'si-text-input-popup'} title={title} onClose={onHide} open={true}>
      <DialogContent>
        <Box display="flex" flexDirection="column">
          {isDefined(subtitle) && <Typography color="secondary">{subtitle}</Typography>}
          <TextField
            type={type}
            id="si-text-input"
            name="si-text-input"
            value={newValue}
            className="mt-0"
            onChange={ev => {
              if (maxLength && String(ev.target.value).length > maxLength) {
                return;
              }
              /* Casting hides warnings and errors. It can result in hard to debug code, and unpredictable behaviour. In almost all cases casting isn't needed. */
              /* eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- fixme */
              setNewValue(ev.target.value as T);
            }}
          />
        </Box>
      </DialogContent>
      <DialogActions>
        {noCancel ? null : (
          <Button color="secondary" variant="outlined" onClick={onHide}>
            Cancel
          </Button>
        )}
        <Button onClick={() => onSave(newValue)} disabled={disabled}>
          {saveText}
        </Button>
      </DialogActions>
    </SimpleDialog>
  );
};
