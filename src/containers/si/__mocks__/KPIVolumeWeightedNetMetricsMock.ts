import type {VolumeWeightedNetKgKPIResponse} from 'containers/si/api/apiTypes';

const KPIVolumeWeightedNetKgMock: VolumeWeightedNetKgKPIResponse = {
  kpi_name: 'volume_weighted_net_kg',
  year: 2023,
  metric: {
    volume_weighted_net_kg: 100,
  },
  units: 'kg',
  boundary_type: 'subsection',
};

export const KPIVolumeWeightedNetKgSummarizeByCropTypeMock: VolumeWeightedNetKgKPIResponse &
  Required<Pick<VolumeWeightedNetKgKPIResponse, 'crop_type_summary'>> = {
  ...KPIVolumeWeightedNetKgMock,
  crop_type_summary: {
    1: {volume_weighted_net_kg: 10},
    2: {volume_weighted_net_kg: 20},
    3: {volume_weighted_net_kg: 30},
  },
};
