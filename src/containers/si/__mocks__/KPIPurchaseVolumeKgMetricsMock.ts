import type {PurchaseVolumeKgKPIResponse} from 'containers/si/api/apiTypes';

/**
 * http://api.qa.internal:9089/si-service/programs/110/supply_sheds/kpi_data
 * Kpi Type: 'soc_emissions_factor'
 * Program: 110 (Regen Collective)
 * Common Filters:
  {
    "year_of_interest": 2023,
    "sourcing_regions": [
        {
            "unit_id": 872,
            "unit_type": "subsection"
        },
        {
            "unit_id": 873,
            "unit_type": "subsection"
        },
        {
            "unit_id": 874,
            "unit_type": "subsection"
        },
        {
            "unit_id": 875,
            "unit_type": "subsection"
        },
        {
            "unit_id": 16648,
            "unit_type": "subsection"
        }
    ],
    "crop_types": [1, 5, 23, 24, 41, 28]
  }
 */

const KPIPurchaseVolumeKgMock: PurchaseVolumeKgKPIResponse = {
  kpi_name: 'purchase_volume_kg',
  year: 2023,
  metric: {
    purchase_volume_kg: 15000.0,
  },
  units: 'kg',
  boundary_type: 'subsection',
};

export const KPIPurchaseVolumeKgSummarizeByCropTypeMock: PurchaseVolumeKgKPIResponse &
  Required<Pick<PurchaseVolumeKgKPIResponse, 'crop_type_summary'>> = {
  ...KPIPurchaseVolumeKgMock,
  crop_type_summary: {
    '1': {
      purchase_volume_kg: 8000.0,
    },
    '5': {
      purchase_volume_kg: 7000.0,
    },
  },
};
