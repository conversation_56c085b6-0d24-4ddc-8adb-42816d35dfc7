import type {ChoroplethGroups, TablePanelContent} from 'containers/si/programs/explore/types';

/**
 * Program: 110 (Regen Collective)
 * Year: 2021
 * Subsections: All Program available subsections
 * Crops: All SI available crops
 * KPI: Cover cropping
 */
export const TablePanelColumnsMock = [
  {
    headerName: 'Boundary name',
    field: 'boundaryName',
    minWidth: 150,
    flex: 1,
  },
  {
    headerName: 'Cover cropping',
    field: 'kpiMetrics',
    width: 150,
    type: 'number',
  },
  {
    headerName: 'Total farms',
    field: 'farm_count',
    width: 125,
    type: 'number',
  },
];

export const TablePanelRowsMock: TablePanelContent['rows'] = [
  {
    id: '872',
    boundaryName: 'Great Lakes/Corn Belt',
    boundaryTimeTrendColor: null,
    choroplethColor: {
      color: '#E09018',
      cssSelector: '4-5',
    },

    kpiMetrics: {
      primary: {
        value: 0.05319761816474185,
        unit: 'unit-interval',
        formattedValue: '5.3%',
      },
      secondary: {
        value: 3652480.3867655084,
        unit: 'ac',
        formattedValue: '3.7M',
      },
    },
  },
  {
    id: '873',
    boundaryName: 'Mountain West',
    boundaryTimeTrendColor: null,
    choroplethColor: {
      color: '#FEF9F2',
      cssSelector: '1-5',
    },

    kpiMetrics: {
      primary: {
        value: 0.011476279560778398,
        unit: 'unit-interval',
        formattedValue: '1.1%',
      },
      secondary: {
        value: 77002.49497569584,
        unit: 'ac',
        formattedValue: '77K',
      },
    },
  },
  {
    id: '874',
    boundaryName: 'Northern Plains',
    boundaryTimeTrendColor: null,
    choroplethColor: {
      color: '#E9DAB5',
      cssSelector: '2-5',
    },

    kpiMetrics: {
      primary: {
        value: 0.044265883647254686,
        unit: 'unit-interval',
        formattedValue: '4.4%',
      },
      secondary: {
        value: 2022200.0962080886,
        unit: 'ac',
        formattedValue: '2M',
      },
    },
  },
  {
    id: '875',
    boundaryName: 'Southern Plains',
    boundaryTimeTrendColor: null,
    choroplethColor: {
      color: '#E1AA58',
      cssSelector: '3-5',
    },

    kpiMetrics: {
      primary: {
        value: 0.05094761965609893,
        unit: 'unit-interval',
        formattedValue: '5.1%',
      },
      secondary: {
        value: 1616979.6447788004,
        unit: 'ac',
        formattedValue: '1.6M',
      },
    },
  },
  {
    id: '16648',
    boundaryName: 'Nebraska',
    boundaryTimeTrendColor: null,
    choroplethColor: {
      color: '#3E290A',
      cssSelector: '5-5',
    },

    kpiMetrics: {
      primary: {
        value: 0.07331124314889838,
        unit: 'unit-interval',
        formattedValue: '7.3%',
      },
      secondary: {
        value: 709706.0044590052,
        unit: 'ac',
        formattedValue: '710K',
      },
    },
  },
];

export const TablePanelSortingMock = {
  sortModel: [
    {
      field: 'kpiMetrics',
      sort: 'desc' as const,
    },
  ],
};

export const TablePanelChoroplethGroupsMock: ChoroplethGroups = [
  {
    color: '#FEF9F2',
    exclusiveLowerBound: 0.011476279560778398,
    inclusiveUpperBound: 0.03770796282995943,
    label: '',
    cssSelector: '',
    cssWidth: '',
  },
  {
    color: '#E9DAB5',
    exclusiveLowerBound: 0.03770796282995943,
    inclusiveUpperBound: 0.04827492525256123,
    label: '',
    cssSelector: '',
    cssWidth: '',
  },
  {
    color: '#E1AA58',
    exclusiveLowerBound: 0.04827492525256123,
    inclusiveUpperBound: 0.0518476190595561,
    label: '',
    cssSelector: '',
    cssWidth: '',
  },
  {
    color: '#E09018',
    exclusiveLowerBound: 0.0518476190595561,
    inclusiveUpperBound: 0.057220343161573206,
    label: '',
    cssSelector: '',
    cssWidth: '',
  },
  {
    color: '#3E290A',
    exclusiveLowerBound: 0.057220343161573206,
    inclusiveUpperBound: 0.07331124314889861,
    label: '',
    cssSelector: '',
    cssWidth: '',
  },
];
