import groupBy from 'lodash/groupBy';
import sortBy from 'lodash/sortBy';
import uniq from 'lodash/uniq';
import type {ComponentProps} from 'react';
import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {FormattedMessage, useIntl} from 'react-intl';

import {
  Box,
  Chip,
  Paper,
  styled,
  SvgIcon,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  useTheme,
  type Theme,
} from '@regrow-internal/design-system';

import {useAppDispatch, useAppSelector} from 'store/useRedux';

import {PayoutStructure} from '__generated__/mrv/mrvApi.types';
import {MRVApi} from '_common/api';
import {LoadingIcon} from '_common/components/icons/mrv-icons';
import {useParsedMatchParams} from '_common/hooks/use-parsed-match-params';
import {convertUnit} from '_common/utils/conversions';
import {MeasurementEnum} from '_common/utils/measurement-unit-options';
import {kiloFormatter} from '_common/utils/number-formatters';
import {reportError} from '_common/utils/reportError';
import {isDefined} from '_common/utils/typeGuards';

import {selectMeasurement} from 'containers/login/login-selectors';
import {useMRVValues} from 'containers/mrv/data/use-mrv-values';
import {selectDNDC} from 'containers/mrv/enrollment/carbon-store/selectors';
import type {DNDCResultsDetailedItem} from 'containers/mrv/enrollment/carbon-store/types';
import {
  selectAssignPracticeAttribute,
  selectAssignPracticesStage,
  selectSkipAssigningPracticeAttribute,
  selectViewOutcomesStage,
} from 'containers/mrv/monitoring/module/enrollment.selectors';
import {
  selectCurrentProgram,
  selectProjectFieldsList,
} from 'containers/mrv/monitoring/module/selectors';
import type {MRVEligibilityPerEntity} from 'containers/mrv/types';
import {DndcStatusChoices, PracticeChange, StageOptionalMetrics} from 'containers/mrv/types';

import {getPayoutPerValue} from './util';

/** Used to distinguish between empty and has not loaded or empty and has loaded */
const EMPTY_ELIGIBILITY: MRVEligibilityPerEntity = {};

export const ReviewEstimatesTable = React.memo(function ReviewEstimatesTable() {
  const intl = useIntl();
  const dispatch = useAppDispatch();
  const {projectId} = useParsedMatchParams();
  const program = useAppSelector(selectCurrentProgram);
  const dndc = useAppSelector(selectDNDC);
  const projectFields = useAppSelector(s => selectProjectFieldsList(s, projectId));
  const measurement = useAppSelector(selectMeasurement);
  const viewOutcomesStage = useAppSelector(selectViewOutcomesStage);
  const assignPracticesStage = useAppSelector(selectAssignPracticesStage);
  const skipAssignPracticesAttribute = useAppSelector(selectSkipAssigningPracticeAttribute);
  const assignPracticesAttribute = useAppSelector(selectAssignPracticeAttribute);
  const {entityRowsByStageId} = useMRVValues(projectId);
  const [fieldsEligibility, setFieldsEligibility] = useState<MRVEligibilityPerEntity | null>(null);
  const theme = useTheme();
  const payoutStructure = program?.payout_structure;
  const isPayPerPracticeOrArea =
    payoutStructure === PayoutStructure.PayPerPractice ||
    payoutStructure === PayoutStructure.PayPerAreaEnrolled;

  useEffect(() => {
    if (!projectId || !assignPracticesStage?.id) {
      return;
    }
    if (!assignPracticesStage?.eligibility_method) {
      reportError(
        `Assign practices stage is not configured with ELIGIBILITY METHOD and a grower is trying to use it.`
      );
      return;
    }
    void MRVApi.getStageEligibility(projectId, assignPracticesStage?.id).then(({data}) => {
      setFieldsEligibility(data ?? EMPTY_ELIGIBILITY);
    });
  }, [dispatch, projectId, assignPracticesStage?.id, assignPracticesStage?.eligibility_method]);

  const entityValueRows = useMemo(
    () =>
      assignPracticesStage && entityRowsByStageId?.[assignPracticesStage.id]
        ? entityRowsByStageId[assignPracticesStage.id]
        : {},
    [assignPracticesStage, entityRowsByStageId]
  );

  const fieldDndcResults = useMemo(() => {
    const result: Record<number, DNDCResultsDetailedItem> = {};
    dndc.detailedItems?.forEach(item => {
      result[item.field_id] = item;
    });
    return result;
  }, [dndc.detailedItems]);

  const isEnrollingWithoutPractices = useCallback(
    (fieldId: number) =>
      (skipAssignPracticesAttribute &&
        entityValueRows?.[fieldId]
          ?.map(row => row.values[skipAssignPracticesAttribute.id]?.value)
          .includes('1')) ||
      false,
    [entityValueRows, skipAssignPracticesAttribute]
  );

  const getFieldPractices = useCallback(
    (fieldId: number): PracticeChange[] =>
      assignPracticesAttribute
        ? uniq(
            entityValueRows?.[fieldId]?.map(row => row.values[assignPracticesAttribute?.id]?.value)
          )
        : [],
    [assignPracticesAttribute, entityValueRows]
  );

  const fieldsWithFarmName = useMemo(
    () =>
      Object.values(groupBy(projectFields, field => field?.farm_id)).flatMap(fields => {
        const farmFields = fields.map(field => ({field, farmName: ''}));
        const farmField = farmFields[0];
        if (farmField && fields[0]?.core_attributes.farm_name) {
          farmField.farmName = fields[0]?.core_attributes.farm_name;
        }

        return farmFields;
      }),
    [projectFields]
  );

  const programPaymentUnit = program?.units;

  return (
    <Paper>
      <TableContainer>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>
                {intl.formatMessage({
                  id: 'Farm',
                  defaultMessage: 'Farm',
                })}
              </TableCell>
              <TableCell sx={{padding: '0px 12px !important'}}>
                <Box
                  display="flex"
                  flexDirection="column"
                  alignItems="flex-start"
                  flex="1 1 0"
                  p="0px 0px"
                  justifyContent="center"
                >
                  <Typography variant="h5" sx={{minWidth: 'max-content'}}>
                    <FormattedMessage id="Field" />
                  </Typography>
                  <Typography variant="body2" color={theme.palette.semanticPalette.text.secondary}>
                    {intl.formatMessage({id: 'FieldID', defaultMessage: 'Field ID'})}&nbsp;/&nbsp;
                    {intl.formatMessage({id: measurement, defaultMessage: measurement})}
                  </Typography>
                </Box>
              </TableCell>
              <TableCell>
                {intl.formatMessage({
                  id: 'First enrolled',
                  defaultMessage: 'First enrolled',
                })}
              </TableCell>
              <TableCell>
                {intl.formatMessage({
                  id: 'Region',
                  defaultMessage: 'Region',
                })}
              </TableCell>

              <TableCell>
                {intl.formatMessage({
                  id: 'Intended practices',
                  defaultMessage: 'Intended practices',
                })}
              </TableCell>
              <TableCell>
                {intl.formatMessage({
                  id: 'Field eligibility',
                  defaultMessage: 'Field eligibility',
                })}
              </TableCell>
              {isPayPerPracticeOrArea && (
                <TableCell>
                  {measurement === MeasurementEnum.ImperialUnits
                    ? intl.formatMessage({
                        id: 'Payout per acre',
                        defaultMessage: 'Payout per acre',
                      })
                    : intl.formatMessage({
                        id: 'Payout per hectare',
                        defaultMessage: 'Payout per hectare',
                      })}
                </TableCell>
              )}
              {viewOutcomesStage?.enabled_metrics?.includes(
                StageOptionalMetrics.FieldLevelOutcomes
              ) && (
                <TableCell>
                  {intl.formatMessage({
                    id: 'Est. outcomes',
                    defaultMessage: 'Est. outcomes',
                  })}
                </TableCell>
              )}
              {viewOutcomesStage?.enabled_metrics?.includes(
                StageOptionalMetrics.FieldLevelPayments
              ) && (
                <TableCell>
                  {intl.formatMessage({
                    id: 'Est. payout',
                    defaultMessage: 'Est. payout',
                  })}
                </TableCell>
              )}
            </TableRow>
          </TableHead>
          <TableBody>
            {fieldsWithFarmName.map(({field, farmName}) => {
              if (!field?.id || !isDefined(field.fs_field_id)) {
                return null;
              }

              const fieldPractices = getFieldPractices(field.id);

              const payoutPerValue = getPayoutPerValue({
                programPaymentUnit,
                userMeasurementUnit: measurement,
                note: fieldDndcResults[field.id]?.note,
              });

              return (
                <TableRow key={field.id}>
                  <TableCell>
                    <Typography variant="h5">{farmName}</Typography>
                  </TableCell>
                  <TableCell padding="none">
                    <Box
                      display="flex"
                      flexDirection="column"
                      alignItems="flex-start"
                      flex="1 1 0"
                      px={3}
                      height={'52px'}
                      justifyContent="center"
                    >
                      <Typography variant="h5">{field.core_attributes.field_name}</Typography>
                      <Typography
                        variant="body2"
                        color={theme.palette.semanticPalette.text.secondary}
                      >
                        #{field?.id}&nbsp;/&nbsp;
                        {convertUnit(
                          measurement,
                          MeasurementEnum.ImperialUnits,
                          field.area,
                          false,
                          2
                        )}
                        &nbsp;
                        {intl.formatMessage({id: measurement, defaultMessage: measurement})}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body1">{field?.baseline_year}</Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body1">
                      {field.core_attributes.region ? field.core_attributes.region : '—'}
                    </Typography>
                  </TableCell>

                  <TableCell padding="none">
                    <Box px={4} py={2} display="flex" alignItems="center">
                      {isEnrollingWithoutPractices(field.id) || !fieldPractices?.length ? (
                        <Typography variant="body1">
                          <FormattedMessage
                            id="Stage.ReviewEstimates.NoPractices"
                            defaultMessage="I do not plan on any regenerative practices this year"
                          />
                        </Typography>
                      ) : (
                        <PracticeChips practices={fieldPractices} />
                      )}
                    </Box>
                  </TableCell>
                  <TableCell padding="none">
                    <Box px={4} display="flex" alignItems="center">
                      {fieldsEligibility !== null ? (
                        fieldsEligibility?.[field.id]?.eligible ? (
                          <Chip color="success" label={<FormattedMessage id="Eligible" />} />
                        ) : (
                          <Chip color="error" label={<FormattedMessage id="Ineligible" />} />
                        )
                      ) : (
                        <LoadingIcon />
                      )}
                    </Box>
                  </TableCell>
                  {isPayPerPracticeOrArea && (
                    <TableCell>
                      {fieldDndcResults[field.id]?.value ? (
                        <Typography color="semanticPalette.text.success" fontWeight="bold">
                          {program?.currency_char}
                          {kiloFormatter(payoutPerValue, 2) || '—'}
                        </Typography>
                      ) : (
                        '—'
                      )}
                    </TableCell>
                  )}
                  {viewOutcomesStage?.enabled_metrics?.includes(
                    StageOptionalMetrics.FieldLevelOutcomes
                  ) && (
                    <TableCell>
                      <Typography variant="body1">
                        {(dndc?.status === DndcStatusChoices.Success || isPayPerPracticeOrArea) &&
                        fieldDndcResults[field.id]?.quantity
                          ? `${kiloFormatter(fieldDndcResults[field.id]?.quantity, 2)} tCO2e`
                          : '—'}
                      </Typography>
                    </TableCell>
                  )}
                  {viewOutcomesStage?.enabled_metrics?.includes(
                    StageOptionalMetrics.FieldLevelPayments
                  ) && (
                    <TableCell>
                      <Typography color="semanticPalette.text.success" fontWeight="bold">
                        {(dndc?.status === DndcStatusChoices.Success || isPayPerPracticeOrArea) &&
                        fieldDndcResults[field?.id]?.value
                          ? `${program?.currency_char}${
                              kiloFormatter(fieldDndcResults[field.id]?.value, 2) || '—'
                            }`
                          : '—'}
                      </Typography>
                    </TableCell>
                  )}
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </TableContainer>
    </Paper>
  );
});

export const PracticeChips = ({practices}: {practices: PracticeChange[] | PracticeChange}) => {
  const theme = useTheme();

  type PracticeData = {
    practice: PracticeChange;
    icon?: ComponentProps<typeof SvgIcon>['type'];
    colorCategory: keyof typeof theme.palette.categoryPalette;
    order: number;
  };

  const sorted = useMemo(
    () =>
      sortBy(
        (Array.isArray(practices) ? practices : [practices]).map<PracticeData>(practice => {
          switch (practice) {
            case PracticeChange.CoverCrops:
            case PracticeChange.BasicCoverCrops:
            case PracticeChange.PremiumCoverCrops:
            case PracticeChange.NoCoverCrop:
              return {practice, icon: 'crop', colorCategory: '2', order: 0};
            case PracticeChange.NoTill:
            case PracticeChange.ReducedTill:
            case PracticeChange.ConventionalTill:
              return {practice, icon: 'tillage', colorCategory: '7', order: 1};
            default:
              return {practice, colorCategory: '7', order: 2};
          }
        }),
        practice => practice.order
      ),
    [practices]
  );

  return (
    <Box display="flex" gap={2} flexWrap="wrap">
      {sorted.map(({practice, colorCategory, icon}) => (
        <StyledPracticeChip
          key={practice}
          icon={icon && <SvgIcon type={icon} />}
          color={colorCategory}
          label={
            <Typography color={theme.palette.categoryPalette[colorCategory].text}>
              <FormattedMessage id={practice} defaultMessage={practice} />
            </Typography>
          }
        />
      ))}
    </Box>
  );
};

const StyledPracticeChip = styled(Chip)<{
  color: keyof Theme['palette']['categoryPalette'];
}>`
  & svg {
    color: ${({theme, color}) => {
      return color ? `${theme.palette.categoryPalette[color]?.text} !important` : 'inherit';
    }};
  }
`;
