import {Mutex} from 'async-mutex';
import type {DebouncedFunc} from 'lodash';
import debounce from 'lodash/debounce';
import {useCallback, useEffect, useMemo, useRef, useState} from 'react';

import {useAppDispatch, useAppSelector} from 'store/useRedux';

import {MRVApi} from '_common/api';
import {showNotification} from '_common/components/NotificationSnackbar';
import {useParsedMatchParams} from '_common/hooks/use-parsed-match-params';
import {selectFarms} from '_common/modules/farms/selectors';
import {selectFieldsByFarmId} from '_common/modules/global/map-selectors';
import {withMutex} from '_common/utils/mutex';
import {reportError} from '_common/utils/reportError';
import {isDefined} from '_common/utils/typeGuards';

import {useFeatureEnabled} from 'containers/mrv/_hooks/useFeatures';
import {usePhaseReadOnly} from 'containers/mrv/_hooks/usePhaseReadOnly';
import {FEATURE_FLAGS} from 'containers/mrv/constants';
import {useMRVValues} from 'containers/mrv/data/use-mrv-values';
import {
  selectAssignPracticeAttribute,
  selectSkipAssigningPracticeAttribute,
} from 'containers/mrv/monitoring/module/enrollment.selectors';
import {
  selectMRVProgramById,
  selectPhaseStageByType,
  selectProjectById,
  selectProjectFieldsList,
  selectProjectPractices,
} from 'containers/mrv/monitoring/module/selectors';
import {
  clearDNDCData,
  fetchStageCompletion,
  removeProjectValues,
  updateMRVValues,
  validateExistingAssignedPractices,
} from 'containers/mrv/monitoring/module/thunks';
import {MRV_STAGE} from 'containers/mrv/routes';
import type {
  MRVEligibilityPerEntity,
  MRVValueInput,
  PracticeChange,
  RowsByEntityId,
} from 'containers/mrv/types';
import {MRVPhaseType, MRVStageType, ProgressChoices} from 'containers/mrv/types';

export const useIntendedPractices = () => {
  const dispatch = useAppDispatch();
  const {projectId: projectIdString} = useParsedMatchParams<{projectId: string}>(MRV_STAGE);
  const projectId = parseInt(projectIdString, 10);
  const {isReadOnly: isEnrollmentReadOnly} = usePhaseReadOnly(MRVPhaseType.Enrolment);
  const practicesList = useAppSelector(s => selectProjectPractices(s, projectId));
  const projectFields = useAppSelector(s => selectProjectFieldsList(s, projectId));
  const project = useAppSelector(s => selectProjectById(s, projectId));
  const program = useAppSelector(s => selectMRVProgramById(s, project?.program_id));
  const assignPracticesStage = useAppSelector(s =>
    selectPhaseStageByType(s, MRVPhaseType.Enrolment, MRVStageType.ASSIGN_PRACTICES)
  );
  const assignPracticesAttribute = useAppSelector(selectAssignPracticeAttribute);
  const skipAssignPracticesAttribute = useAppSelector(selectSkipAssigningPracticeAttribute);

  const fieldsByFarmId = useAppSelector(selectFieldsByFarmId);
  const farms = useAppSelector(selectFarms);
  const [fieldsEligibility, setFieldsEligibility] = useState<MRVEligibilityPerEntity | null>(null);
  const {entityRowsByStageId, revalidate, updateAssignPractices} = useMRVValues(projectId);

  const entityValueRows: RowsByEntityId = useMemo(
    () => (assignPracticesStage?.id && entityRowsByStageId?.[assignPracticesStage?.id]) || {},
    [assignPracticesStage?.id, entityRowsByStageId]
  );

  // Create refs to store fresh data for use in callbacks, to avoid stale closures
  const entityValueRowsRef = useRef(entityValueRows);
  const assignPracticesStageRef = useRef(assignPracticesStage);
  const assignPracticesAttributeRef = useRef(assignPracticesAttribute);
  const skipAssignPracticesAttributeRef = useRef(skipAssignPracticesAttribute);

  // Update refs whenever values change
  useEffect(() => {
    entityValueRowsRef.current = entityValueRows;
  }, [entityValueRows]);

  useEffect(() => {
    assignPracticesStageRef.current = assignPracticesStage;
  }, [assignPracticesStage]);

  useEffect(() => {
    assignPracticesAttributeRef.current = assignPracticesAttribute;
  }, [assignPracticesAttribute]);

  useEffect(() => {
    skipAssignPracticesAttributeRef.current = skipAssignPracticesAttribute;
  }, [skipAssignPracticesAttribute]);

  const enableCompletionValidation = useFeatureEnabled(FEATURE_FLAGS.PROGRESS_BLOCKING_VALIDATION);

  useEffect(() => {
    if (!projectId || !assignPracticesStage?.id) {
      return;
    }
    if (!assignPracticesStage?.eligibility_method) {
      reportError(
        `Assign practices stage is not configured with ELIGIBILITY METHOD and a grower is trying to use it.`
      );
      return;
    }
    void MRVApi.getStageEligibility(projectId, assignPracticesStage?.id).then(({data}) => {
      void dispatch(validateExistingAssignedPractices({enableCompletionValidation})); // validate on the beginning
      setFieldsEligibility(data);
    });
  }, [
    dispatch,
    projectId,
    assignPracticesStage?.id,
    assignPracticesStage?.eligibility_method,
    enableCompletionValidation,
  ]);

  const assignPracticesToAField = useCallback(
    async (entityId: number, practices: PracticeChange[]) => {
      // Get fresh data from refs
      const currentAssignPracticesStage = assignPracticesStageRef.current;
      const currentAssignPracticesAttribute = assignPracticesAttributeRef.current;
      const currentEntityValueRows = entityValueRowsRef.current;
      const currentSkipAssignPracticesAttribute = skipAssignPracticesAttributeRef.current;

      // Satisfy null checks
      if (!currentAssignPracticesStage || !currentAssignPracticesAttribute) {
        return;
      }

      const entityRows = currentEntityValueRows[entityId] || [];
      const existingValues = entityRows.map(
        row =>
          row.values[currentAssignPracticesAttribute.id] ||
          row.values[currentSkipAssignPracticesAttribute?.id || 0]
      );

      const entityType = currentAssignPracticesStage.entity_type;
      const valueIds: number[] = [];
      // remove existing values entirely
      existingValues.forEach(v => {
        if (v?.id) {
          valueIds.push(v.id);
        }
      });

      if (valueIds.length) {
        await dispatch(
          removeProjectValues({
            valueIds,
            projectId,
            entityType,
            enableCompletionValidation,
          })
        );
      }

      let rowIdCounter = 0;

      const valuesToAdd: MRVValueInput[] = practices.map(value => {
        return {
          value,
          entity_type: entityType,
          locked: false,
          confirmed: true,
          attribute_id: currentAssignPracticesAttribute.id,
          progress: ProgressChoices.Enrolment,
          row_id: ++rowIdCounter,
          attribute_type: currentAssignPracticesAttribute.type,
        };
      });

      // if a change was made, we should update
      if (valuesToAdd.length) {
        await dispatch(
          updateMRVValues({
            update: {[entityId]: valuesToAdd},
            projectId,
            entityType,
            enableCompletionValidation,
          })
        );
      }

      // update swr state
      updateAssignPractices({
        entityId,
        values: valuesToAdd,
        stageEndYear: currentAssignPracticesStage.year_end,
        entityType: currentAssignPracticesStage.entity_type,
        stageId: currentAssignPracticesStage.id,
      });

      // refetch mrv values
      await revalidate();

      // Make sure we can run DNDC again, after assigned practices have changed.
      void dispatch(clearDNDCData({projectId}));

      // fetch completion for AssignPractices stage
      void dispatch(
        fetchStageCompletion({
          projectId,
          stageId: currentAssignPracticesStage.id,
          enableValidation: enableCompletionValidation,
        })
      );
    },
    [dispatch, projectId, revalidate, updateAssignPractices, enableCompletionValidation]
  );

  /** Used to specifically opt out of assigning a practice */
  const assignNoPracticesToAField = useCallback(
    async (entityId: number) => {
      // Get fresh data from refs
      const currentAssignPracticesStage = assignPracticesStageRef.current;
      const currentAssignPracticesAttribute = assignPracticesAttributeRef.current;
      const currentEntityValueRows = entityValueRowsRef.current;
      const currentSkipAssignPracticesAttribute = skipAssignPracticesAttributeRef.current;

      // Satisfy null checks
      if (
        !currentAssignPracticesStage ||
        !currentAssignPracticesAttribute ||
        !currentSkipAssignPracticesAttribute
      ) {
        if (!currentSkipAssignPracticesAttribute) {
          reportError(
            'User tried to assign no practices (EWAP) but the attribute is not configured so mrv_values could not be posted.'
          );
        }
        showNotification({
          type: 'error',
          title: 'Error',
          message: 'the attribute is not configured, please contact support',
        });

        return;
      }

      // This is the values for just this one field (field is an entity)
      const entityRows = currentEntityValueRows[entityId] || [];

      // Get the values for `assign practices` in order to remove with `removeProjectValues`
      const entityType = currentAssignPracticesStage.entity_type;
      const existingValues = entityRows.map(
        row =>
          row.values[currentAssignPracticesAttribute.id] ||
          row.values[currentSkipAssignPracticesAttribute?.id || 0]
      );
      const existingValuesIds = existingValues.map(v => v?.id).filter(isDefined);
      await dispatch(
        removeProjectValues({
          valueIds: existingValuesIds,
          projectId,
          entityType,
          enableCompletionValidation,
        })
      );

      // Finally, submit the attribute for enrol without assigning practices
      const valuesToAdd: MRVValueInput[] = [
        {
          attribute_id: currentSkipAssignPracticesAttribute.id,
          attribute_type: currentSkipAssignPracticesAttribute.type,
          confirmed: true,
          entity_type: entityType,
          locked: false,
          progress: ProgressChoices.Enrolment,
          row_id: 0,
          value: 1,
        },
      ];

      if (valuesToAdd.length) {
        await dispatch(
          updateMRVValues({
            update: {[entityId]: valuesToAdd},
            projectId,
            entityType,
            enableCompletionValidation,
          })
        );
      }

      const updatedValues = [...existingValues, ...valuesToAdd].filter(isDefined);

      // update swr state
      updateAssignPractices({
        entityId,
        values: updatedValues,
        stageEndYear: currentAssignPracticesStage.year_end,
        entityType: currentAssignPracticesStage.entity_type,
        stageId: currentAssignPracticesStage.id,
      });

      // refetch mrv values
      await revalidate();

      // Make sure we can run DNDC again, after assigned practices have changed.
      void dispatch(clearDNDCData({projectId}));

      // fetch completion for AssignPractices stage
      void dispatch(
        fetchStageCompletion({
          projectId,
          stageId: currentAssignPracticesStage.id,
          enableValidation: enableCompletionValidation,
        })
      );
    },
    [dispatch, projectId, revalidate, updateAssignPractices, enableCompletionValidation]
  );

  const getFieldsPractices = useCallback(
    (fieldId: number): PracticeChange[] =>
      (
        (assignPracticesAttribute &&
          entityValueRows[fieldId]?.map(row => row.values[assignPracticesAttribute?.id]?.value)) ||
        []
      ).filter(isDefined),
    [assignPracticesAttribute, entityValueRows]
  );

  const getIsEnrollingWithoutAssignedPractices = useCallback(
    (fieldId: number) => {
      const isEnrollingWithoutPractices =
        (skipAssignPracticesAttribute &&
          entityValueRows[fieldId]
            ?.map(row => row.values[skipAssignPracticesAttribute.id]?.value)
            .includes('1')) ||
        false;
      return isEnrollingWithoutPractices;
    },
    [entityValueRows, skipAssignPracticesAttribute]
  );

  // Maps to store per-field debounced functions and mutexes
  const fieldMutexMap = useRef(new Map<number, Mutex>());
  const debouncedFunctionsMap = useRef(
    new Map<
      number,
      {
        assignPractices: DebouncedFunc<typeof assignPracticesToAField>;
        assignNoPractices: DebouncedFunc<typeof assignNoPracticesToAField>;
      }
    >()
  );

  // Helper to get or create debounced functions for a specific field
  const getDebouncedFunctionsForField = useCallback(
    (fieldId: number) => {
      if (!debouncedFunctionsMap.current.has(fieldId)) {
        // Create a mutex specific to this field
        const fieldMutex = new Mutex();
        fieldMutexMap.current.set(fieldId, fieldMutex);

        // Create debounced functions for this field
        debouncedFunctionsMap.current.set(fieldId, {
          assignPractices: debounce(withMutex(fieldMutex, assignPracticesToAField), 1000),
          assignNoPractices: debounce(withMutex(fieldMutex, assignNoPracticesToAField), 1000),
        });
      }
      const debouncedFunctions = debouncedFunctionsMap.current.get(fieldId);
      if (!debouncedFunctions) {
        throw new Error(`Failed to create debounced functions for field ${fieldId}`);
      }
      return debouncedFunctions;
    },
    [assignPracticesToAField, assignNoPracticesToAField]
  );

  // Wrapped functions that use per-field debouncing
  const assignPracticesToAFieldDebounced = useCallback(
    (entityId: number, practices: PracticeChange[]) =>
      getDebouncedFunctionsForField(entityId).assignPractices(entityId, practices),
    [getDebouncedFunctionsForField]
  );

  const assignNoPracticesToAFieldDebounced = useCallback(
    (entityId: number) => getDebouncedFunctionsForField(entityId).assignNoPractices(entityId),
    [getDebouncedFunctionsForField]
  );

  return useMemo(
    () => ({
      assignPracticesStage,
      assignNoPracticesToAField: assignNoPracticesToAFieldDebounced,
      assignPracticesToAField: assignPracticesToAFieldDebounced,
      farms,
      fieldsByFarmId,
      fieldsEligibility,
      getFieldsPractices,
      getIsEnrollingWithoutAssignedPractices,
      isEnrollmentReadOnly,
      practicesList,
      program,
      projectFields,
      projectId,
    }),
    [
      assignNoPracticesToAFieldDebounced,
      assignPracticesStage,
      assignPracticesToAFieldDebounced,
      farms,
      fieldsByFarmId,
      fieldsEligibility,
      getFieldsPractices,
      getIsEnrollingWithoutAssignedPractices,
      isEnrollmentReadOnly,
      practicesList,
      program,
      projectFields,
      projectId,
    ]
  );
};
