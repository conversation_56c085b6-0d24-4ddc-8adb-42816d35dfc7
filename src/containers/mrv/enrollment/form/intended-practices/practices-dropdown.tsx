import ListSubheader from '@mui/material/ListSubheader';
import type {ReactElement} from 'react';
import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {FormattedMessage, useIntl} from 'react-intl';

import type {SelectChangeEvent} from '@regrow-internal/design-system';
import {
  Box,
  Checkbox,
  MenuItem,
  Select,
  SelectField,
  Tooltip,
  Typography,
} from '@regrow-internal/design-system';

import {usePracticeChanges} from 'containers/mrv/_hooks/usePracticeChanges';
import {useStagePracticeChangeRules} from 'containers/mrv/_hooks/useStagePracticeChangeRules';
import {PracticeRuleTypes} from 'containers/mrv/admin/types';
import type {PracticeChange} from 'containers/mrv/types';
import {EligibilityTypes} from 'containers/mrv/types';

import {PracticeChips} from '../review-estimates/ReviewEstimatesTable';

type Props = {
  stageId: number;
  eligibilityMethod: EligibilityTypes | null;
  eligibleButUnassigned: boolean;
  /** Currently chosen practice set. See `practicesItems` for all available options */
  fieldPractices: PracticeChange[];
  ineligible: boolean;
  onAssignPractices: (practices: PracticeChange[]) => void;
  /** All of the available practice combinations, eligible for this given field. See `fieldPractices` for currently chosen assigned practice */
  practicesItems: PracticeChange[][];
  baselinePractices: PracticeChange[];
  readonly?: boolean;
};

const MethodOpen = ({
  eligibleButUnassigned,
  fieldPractices,
  ineligible,
  onAssignPractices,
  practicesItems,
  readonly,
}: Props) => {
  const intl = useIntl();
  const [selectedPractices, setSelectedPractices] = useState<PracticeChange[]>(
    fieldPractices || []
  );

  const individualPractices = useMemo(
    () => practicesItems.filter(p => p.length === 1).flat(),
    [practicesItems]
  );

  const options = useMemo(
    () =>
      individualPractices.map(practiceChange => ({
        label: <PracticeChips practices={practiceChange} />,
        value: practiceChange,
      })),
    [individualPractices]
  );

  return (
    <SelectField<PracticeChange[]>
      autoWidth
      color={eligibleButUnassigned ? 'warning' : undefined}
      disabled={ineligible || readonly}
      sx={{
        padding: '0px !important',
        '& .MuiSelect-select': {
          height: 'auto',
          display: 'flex',
          alignItems: 'center',
          paddingLeft: '12px !important',
          paddingTop: '8px !important',
          paddingBottom: '8px !important',
          '-webkit-text-fill-color': 'inherit !important',
        },
      }}
      value={selectedPractices}
      onChange={event => {
        setSelectedPractices(event.target.value);
        onAssignPractices(event.target.value);
      }}
      multiple={true}
      options={options}
      localeText={{getMultiSelectText: () => 'Select practices'}}
      variant="filled"
      renderValue={selected => {
        if (eligibleButUnassigned) {
          return (
            <Box>
              {intl.formatMessage({
                id: 'Select practices',
                defaultMessage: 'Select practices',
              })}
            </Box>
          );
        }
        return (
          <Box display="flex" flexWrap="wrap" gap={2}>
            {selected.map(optionValue => {
              return options.find(option => option.value === optionValue)?.label || null;
            })}
          </Box>
        );
      }}
    />
  );
};

const EMPTY_OPTION_ELEMENTS: ReactElement[] = [];

const EMPTY_PRACTICE_SELECTION: PracticeChange[] = [];

const MethodRuleBased = ({
  stageId,
  fieldPractices,
  ineligible,
  onAssignPractices,
  practicesItems,
  baselinePractices,
  readonly,
}: Props) => {
  const intl = useIntl();
  const allowedPracticesSet = useMemo(
    () => new Set(practicesItems.filter(p => p.length === 1).flat()),
    [practicesItems]
  );

  // Leave the selection to be empty if any of the practices are not allowed, due to value changes
  const validatedFieldPractices = useMemo(
    () =>
      fieldPractices.some(practice => !allowedPracticesSet.has(practice))
        ? EMPTY_PRACTICE_SELECTION
        : fieldPractices,
    [fieldPractices, allowedPracticesSet]
  );

  const [selectedPractices, setSelectedPractices] =
    useState<PracticeChange[]>(validatedFieldPractices);
  const {practiceGroups} = usePracticeChanges();
  const {practiceChangeRules, validatePractices, validateEligibilityMatrixExactMatch} =
    useStagePracticeChangeRules(stageId);

  const [eligibilityMatrixError, setEligibilityMatrixError] = useState<string>('');

  // validate eligibility matrix rule on practice change
  // if not a exactly match, then show error
  useEffect(() => {
    const matrixRule = practiceChangeRules?.find(
      rule => rule.type === PracticeRuleTypes.ELIGIBILITY_MATRIX
    );
    if (!matrixRule) return;
    if (selectedPractices.length === 0) {
      setEligibilityMatrixError('');
      return;
    }
    const {isValid, message} = validateEligibilityMatrixExactMatch(
      baselinePractices,
      selectedPractices
    );
    setEligibilityMatrixError(isValid ? '' : message);
  }, [
    baselinePractices,
    practiceChangeRules,
    selectedPractices,
    validateEligibilityMatrixExactMatch,
  ]);

  // practice is disabled if it is not selected and violating any rules if being selected
  const isPracticesDisabled = useCallback(
    (practice: PracticeChange) =>
      !selectedPractices.includes(practice) &&
      practiceChangeRules &&
      practiceChangeRules.some(
        rule => !validatePractices(baselinePractices, [...selectedPractices, practice], rule)
      ),
    [validatePractices, practiceChangeRules, selectedPractices, baselinePractices]
  );

  const options = useMemo(() => {
    if (!practiceGroups) return EMPTY_OPTION_ELEMENTS;
    return Object.entries(practiceGroups).flatMap(([group, practices]) => {
      const elements: ReactElement[] = [];
      for (const practice of practices) {
        if (!allowedPracticesSet.has(practice)) continue;
        const disabled = isPracticesDisabled(practice);
        if (!elements.length) {
          elements.push(
            <ListSubheader key={group}>
              <Box py={1}>
                <Typography variant="body2" color="secondary">
                  <FormattedMessage id={`PracticeGroupOption.${group}`} defaultMessage={group} />
                </Typography>
              </Box>
            </ListSubheader>
          );
        }
        elements.push(
          <MenuItem key={`${group}-${practice}`} value={practice} disabled={disabled}>
            <Box component="span" pl={1}>
              <Checkbox checked={!disabled && selectedPractices.includes(practice)} edge="start" />
            </Box>
            <PracticeChips practices={practice} />
          </MenuItem>
        );
      }
      return elements;
    });
  }, [allowedPracticesSet, isPracticesDisabled, practiceGroups, selectedPractices]);

  const onPracticesChange = useCallback(
    (event: SelectChangeEvent<PracticeChange[]>) => {
      let practices = event.target.value;

      // Group selection rules are not creating cascading effects
      const rules = practiceChangeRules?.filter(
        rule =>
          rule.type !== PracticeRuleTypes.GROUP_PRACTICE_SELECTION &&
          rule.type !== PracticeRuleTypes.ELIGIBILITY_MATRIX
      );

      if (rules?.length) {
        // Remove practices that fail the rules cascadingly, ensuring that all the rules must be satisfied
        let i = 0;
        while (i < rules.length) {
          const rule = rules[i];
          if (!rule) break;
          const isValid = validatePractices(baselinePractices, practices, rule);
          if (isValid) {
            i++;
          } else {
            practices = practices.filter(practice => rule.target !== practice);
            // Restart the loop to ensure that all the rules are satisfied
            i = 0;
          }
        }
      }

      setSelectedPractices(practices);
      onAssignPractices(practices);
    },
    [onAssignPractices, practiceChangeRules, validatePractices, baselinePractices]
  );

  return (
    <Tooltip title={eligibilityMatrixError}>
      <Select<PracticeChange[]>
        displayEmpty
        fullWidth
        color={
          selectedPractices.length === 0 ? 'warning' : eligibilityMatrixError ? 'error' : undefined
        }
        error={!!eligibilityMatrixError}
        disabled={ineligible || readonly}
        multiple={true}
        sx={{
          padding: '0px !important',
          '& .MuiSelect-select': {
            height: 'auto',
            minHeight: '36px',
            display: 'flex',
            alignItems: 'center',
            paddingLeft: '12px !important',
            marginTop: '8px !important',
            marginBottom: '8px !important',
            '-webkit-text-fill-color': 'inherit !important',
          },
        }}
        value={selectedPractices}
        onChange={onPracticesChange}
        variant="filled"
        renderValue={selected => {
          if (selected.length === 0) {
            return (
              <Box>
                {intl.formatMessage({
                  id: 'Select practices',
                  defaultMessage: 'Select practices',
                })}
              </Box>
            );
          }
          return (
            <Box display="flex" flexWrap="wrap" gap={2}>
              {selected.map(optionValue => (
                <PracticeChips key={optionValue} practices={optionValue} />
              ))}
            </Box>
          );
        }}
      >
        {options}
      </Select>
    </Tooltip>
  );
};

const MethodStandard = ({
  eligibleButUnassigned,
  fieldPractices,
  ineligible,
  onAssignPractices,
  practicesItems,
  readonly,
}: Props) => {
  const intl = useIntl();

  const sortedAssignedPractice = [...fieldPractices].sort();
  const rowIndexForCurrentSelection = practicesItems.findIndex(practiceCombo => {
    const sortedPracticesOptions = practiceCombo.sort();
    return sortedPracticesOptions.join() === sortedAssignedPractice.join();
  });
  const [localValue, setLocalValue] = useState<number | ''>('');

  let displayValue: typeof localValue = '';
  if (localValue !== '') {
    displayValue = localValue;
  } else if (rowIndexForCurrentSelection !== -1 && !eligibleButUnassigned) {
    displayValue = rowIndexForCurrentSelection;
  } else {
    displayValue = '';
  }

  return (
    <Select<number>
      color={eligibleButUnassigned ? 'warning' : undefined}
      sx={{
        padding: '0px !important',
        '& .MuiSelect-select': {
          height: '48px',
          display: 'flex',
          alignItems: 'center',
          paddingLeft: '12px !important',
        },
      }}
      variant="filled"
      autoWidth
      onChange={({target: {value}}) => {
        const practiceItemValue = practicesItems[value];
        if (practiceItemValue !== undefined) {
          setLocalValue(value);
          onAssignPractices(practiceItemValue);
        }
      }}
      value={displayValue}
      displayEmpty={ineligible || eligibleButUnassigned}
      renderValue={
        ineligible || eligibleButUnassigned
          ? () => {
              if (ineligible) {
                return (
                  <Box>
                    {intl.formatMessage({
                      id: 'No eligible practice improvements available',
                      defaultMessage: 'No eligible practice improvements available',
                    })}
                  </Box>
                );
              }
              if (eligibleButUnassigned) {
                return (
                  <Box>
                    {intl.formatMessage({
                      id: 'Select practices',
                      defaultMessage: 'Select practices',
                    })}
                  </Box>
                );
              }
              return false;
            }
          : undefined
      }
      disabled={ineligible || readonly}
    >
      {practicesItems.map((practices, index) => (
        <MenuItem key={index} value={index}>
          <PracticeChips practices={practices} />
        </MenuItem>
      ))}
    </Select>
  );
};

export const PracticesDropdown = ({
  stageId,
  eligibilityMethod,
  eligibleButUnassigned,
  fieldPractices,
  ineligible,
  onAssignPractices,
  practicesItems,
  baselinePractices,
  readonly,
}: Props) => {
  switch (eligibilityMethod) {
    case EligibilityTypes.ELIGIBILITY_ALWAYS_TRUE: {
      return (
        <MethodOpen
          stageId={stageId}
          eligibilityMethod={eligibilityMethod}
          eligibleButUnassigned={eligibleButUnassigned}
          fieldPractices={fieldPractices}
          ineligible={ineligible}
          onAssignPractices={onAssignPractices}
          practicesItems={practicesItems}
          baselinePractices={baselinePractices}
          readonly={readonly}
        />
      );
    }
    case EligibilityTypes.PARAMETERISED_ELIGIBILITY: {
      return (
        <MethodRuleBased
          stageId={stageId}
          eligibilityMethod={eligibilityMethod}
          eligibleButUnassigned={eligibleButUnassigned}
          fieldPractices={fieldPractices}
          ineligible={ineligible}
          onAssignPractices={onAssignPractices}
          practicesItems={practicesItems}
          baselinePractices={baselinePractices}
          readonly={readonly}
        />
      );
    }
    default: {
      return (
        <MethodStandard
          stageId={stageId}
          eligibilityMethod={eligibilityMethod}
          eligibleButUnassigned={eligibleButUnassigned}
          fieldPractices={fieldPractices}
          ineligible={ineligible}
          onAssignPractices={onAssignPractices}
          practicesItems={practicesItems}
          baselinePractices={baselinePractices}
          readonly={readonly}
        />
      );
    }
  }
};
