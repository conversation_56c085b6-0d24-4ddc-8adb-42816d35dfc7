import React from 'react';
import {FormattedMessage, useIntl} from 'react-intl';

import {
  Table as BaseTable,
  Box,
  Button,
  Chip,
  FormControlLabel,
  styled,
  SvgIcon,
  Switch,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tooltip,
  Typography,
  useTheme,
} from '@regrow-internal/design-system';

import {useAppDispatch, useAppSelector} from 'store/useRedux';

import {convertUnit} from '_common/utils/conversions';
import {MeasurementEnum} from '_common/utils/measurement-unit-options';
import {isDefined} from '_common/utils/typeGuards';

import {selectMeasurement} from 'containers/login/login-selectors';
import {useHasPermissions} from 'containers/mrv/_hooks/useHasPermissions';
import {enrollFields} from 'containers/mrv/enrollment/carbon-store/carbon-thunks';
import {removeMRVFields} from 'containers/mrv/monitoring/module/thunks';
import {
  EligibilityTypes,
  EPermissions,
  type MRVField,
  type PracticeChange,
} from 'containers/mrv/types';

import {PracticesDropdown} from './practices-dropdown';
import {RemoveFieldDialog} from './remove-field-dialog';
import {useIntendedPractices} from './useIntendedPractices';

type Props = {
  projectFields: MRVField[];
};

const Table = styled(BaseTable)`
  th,
  td {
    padding: 16px 12px;
  }
`;

export const IntendedPracticesTable = React.memo(function IntendedPracticesTable({
  projectFields,
}: Props) {
  const intl = useIntl();
  const measurement = useAppSelector(selectMeasurement);
  const {
    assignPracticesStage,
    assignPracticesToAField,
    assignNoPracticesToAField,
    fieldsEligibility,
    getFieldsPractices,
    getIsEnrollingWithoutAssignedPractices,
    isEnrollmentReadOnly,
    projectId,
  } = useIntendedPractices();
  const theme = useTheme();

  // For the stage, individual fields must still be checked
  const isEnrollingWithoutPracticesAllowed = Boolean(
    assignPracticesStage?.allow_enrol_without_assigned_practice
  );
  const [canUpdateProjectEntitiesValues] = useHasPermissions([
    EPermissions.UPDATE_PROJECT_ENTITIES_VALUES,
  ]);
  const isReadOnly = isEnrollmentReadOnly || !canUpdateProjectEntitiesValues;

  const dispatch = useAppDispatch();
  const [fieldIdToRemove, setFieldIdToRemove] = React.useState<number | null>(null);

  const handleRemoveField = () => {
    setFieldIdToRemove(null);
    if (isReadOnly || !fieldIdToRemove) return;
    const fsFieldId = projectFields.find(f => f.id === fieldIdToRemove)?.core_attributes?.kml_id;
    void dispatch(
      removeMRVFields({
        projectId,
        mrvFieldIds: [fieldIdToRemove],
        stageId: assignPracticesStage?.id,
      })
    ); // remove for back-end
    if (isDefined(fsFieldId)) {
      void dispatch(enrollFields({[fsFieldId]: false})); // remove for UI
    }
  };

  if (assignPracticesStage === undefined || fieldsEligibility === null) {
    return null;
  }

  return (
    <TableContainer>
      <Table>
        <TableHead>
          <TableRow sx={{height: 52}}>
            <TableCell>
              <Typography variant="h5">
                <FormattedMessage id="Farm" defaultMessage="Farm" />
              </Typography>
            </TableCell>
            <TableCell sx={{padding: '0px 12px !important'}}>
              <Box
                display="flex"
                flexDirection="column"
                alignItems="flex-start"
                flex="1 1 0"
                p={0}
                justifyContent="center"
              >
                <Typography variant="h5" sx={{minWidth: 'max-content'}}>
                  <FormattedMessage id="Field" />
                </Typography>
                <Typography variant="body2" color={theme.palette.semanticPalette.text.secondary}>
                  {intl.formatMessage({id: 'Field ID', defaultMessage: 'Field ID'})}&nbsp;/&nbsp;
                  {intl.formatMessage({id: 'area', defaultMessage: 'area'})}
                </Typography>
              </Box>
            </TableCell>
            <TableCell sx={{width: 135, padding: '0px 16px !important', height: 52}}>
              <Typography variant="h5">
                <FormattedMessage id="First Enrollment Year" />
              </Typography>
            </TableCell>
            {isEnrollingWithoutPracticesAllowed && (
              <TableCell width={160} sx={{padding: '0px 16px !important', height: 52}}>
                <Typography variant="h5">
                  <FormattedMessage id="Do you plan on regen practices?" />
                </Typography>
              </TableCell>
            )}
            <TableCell>
              <Typography variant="h5">
                <FormattedMessage id="Field Eligibility" />
              </Typography>
            </TableCell>
            <TableCell>
              <Box display="flex" justifyContent="space-between">
                <Typography variant="h5">
                  <FormattedMessage id="Intended Practices" />
                </Typography>
                {assignPracticesStage?.eligibility_method ===
                  EligibilityTypes.PARAMETERISED_ELIGIBILITY && (
                  <Tooltip
                    placement="top"
                    title={
                      <FormattedMessage
                        id="IntendedPracticeColumn.Tooltip"
                        defaultMessage="You can choose one or more practices, but some combinations may not be allowed based on your program’s rules. If you don’t pick a practice in a category (like tillage), we’ll assume you plan to apply conventional practices."
                      />
                    }
                  >
                    <Box>
                      <SvgIcon type="info-circled" fontSize="small" />
                    </Box>
                  </Tooltip>
                )}
              </Box>
            </TableCell>

            {!isReadOnly && <TableCell padding="none">{/*Remove field button*/}</TableCell>}
          </TableRow>
        </TableHead>
        <TableBody>
          {projectFields.map(field => {
            const eligible = fieldsEligibility?.[field.id]?.eligible;
            const fieldPractices = getFieldsPractices(field.id);
            const isEnrollingWithoutPractices = getIsEnrollingWithoutAssignedPractices(field.id);
            const eligibleButUnassigned = Boolean(eligible && fieldPractices.length === 0);
            const isFieldReturning = field.is_returning;
            const isFieldWithoutPracticesEligibleToBeEnrolled =
              isEnrollingWithoutPracticesAllowed && isFieldReturning;
            const disabled = isReadOnly || !isFieldWithoutPracticesEligibleToBeEnrolled;
            const disabledReason = (() => {
              if (isReadOnly) {
                return intl.formatMessage({
                  id: 'Stage is read only',
                  defaultMessage: 'Stage is read only',
                });
              } else if (!isFieldReturning) {
                return intl.formatMessage({
                  id: 'Fields in their first enrollment year must plan to have regen practices.',
                  defaultMessage:
                    'Fields in their first enrollment year must plan to have regen practices.',
                });
              }
              return '';
            })();
            return (
              <TableRow key={field.id}>
                {/* Farm */}
                <TableCell>
                  <Typography variant="h5" sx={{minWidth: 'max-content'}}>
                    {field.core_attributes.farm_name}
                  </Typography>
                </TableCell>

                {/* Field */}
                <TableCell sx={{padding: '0px 12px !important'}}>
                  <Box
                    display="flex"
                    flexDirection="column"
                    alignItems="flex-start"
                    flex="1 1 0"
                    p={0}
                    justifyContent="center"
                  >
                    <Typography variant="h5" sx={{minWidth: 'max-content'}}>
                      {field.core_attributes.field_name}
                    </Typography>
                    <Typography
                      variant="body2"
                      color={theme.palette.semanticPalette.text.secondary}
                    >
                      #{field.id}&nbsp;/&nbsp;
                      {convertUnit(measurement, MeasurementEnum.ImperialUnits, field.area)}&nbsp;
                      {intl.formatMessage({id: measurement, defaultMessage: measurement})}
                    </Typography>
                  </Box>
                </TableCell>

                {/* First Enrollment year */}
                <TableCell>
                  <Typography variant="body1">{field.baseline_year}</Typography>
                </TableCell>

                {/* Do you plan on regen practices? */}
                {isEnrollingWithoutPracticesAllowed && (
                  <TableCell>
                    <Tooltip title={disabled ? disabledReason : null}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={Boolean(!isEnrollingWithoutPractices)}
                            onChange={async event => {
                              const value = event.target.checked;
                              if (!value) {
                                await assignNoPracticesToAField(field.id);
                              } else {
                                await assignPracticesToAField(field.id, []);
                              }
                            }}
                            disabled={disabled}
                          />
                        }
                        label={
                          !isEnrollingWithoutPractices ? (
                            <FormattedMessage id="Yes" defaultMessage="Yes" />
                          ) : (
                            <FormattedMessage id="No" defaultMessage="No" />
                          )
                        }
                      />
                    </Tooltip>
                  </TableCell>
                )}

                {/* Field Eligibility */}
                <TableCell sx={{padding: '0px !important'}}>
                  <Box px={4} display="flex" alignItems="center">
                    {eligible ? (
                      <Chip
                        color="success"
                        size="medium"
                        label={<FormattedMessage id="Eligible" />}
                      />
                    ) : (
                      <Chip
                        color="error"
                        size="medium"
                        label={<FormattedMessage id="Ineligible" />}
                      />
                    )}
                  </Box>
                </TableCell>

                {/* Practices dropdown */}
                <TableCell
                  sx={{
                    backgroundColor: eligibleButUnassigned
                      ? theme.palette.semanticPalette.surface.warning
                      : 'inherit',
                    padding: '0px 0px !important',
                  }}
                >
                  <Box p={0} display="flex">
                    {!isEnrollingWithoutPractices && (
                      <PracticesDropdown
                        stageId={assignPracticesStage.id}
                        eligibilityMethod={assignPracticesStage.eligibility_method}
                        eligibleButUnassigned={eligibleButUnassigned}
                        fieldPractices={fieldPractices}
                        ineligible={!eligible}
                        onAssignPractices={(practices: PracticeChange[]) =>
                          assignPracticesToAField(field.id, practices)
                        }
                        practicesItems={fieldsEligibility?.[field.id]?.eligible_practices || []}
                        baselinePractices={fieldsEligibility?.[field.id]?.baseline_practices || []}
                        readonly={isReadOnly}
                      />
                    )}
                  </Box>
                </TableCell>

                {!isReadOnly && (
                  <TableCell align="center" sx={{padding: '0px 8px !important'}}>
                    <Button
                      size="small"
                      variant="outlined"
                      color="secondary"
                      onClick={() => setFieldIdToRemove(field.id)}
                      disabled={isReadOnly}
                    >
                      <SvgIcon type="cross" />
                    </Button>
                    <RemoveFieldDialog
                      open={!!fieldIdToRemove}
                      onClose={() => setFieldIdToRemove(null)}
                      onRemoveField={handleRemoveField}
                    />
                  </TableCell>
                )}
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </TableContainer>
  );
});
